{"name": "venue-preorder-backend", "version": "1.0.0", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc && node --experimental-specifier-resolution=node dist/index.js"}, "dependencies": {"@neondatabase/serverless": "^0.10.4", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.39.0", "@tanstack/react-query": "^5.83.0", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "express": "^4.21.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^2.0.1", "neon": "^2.0.0", "openid-client": "^6.5.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.16.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "spotify-web-api-node": "^5.0.2", "stripe": "^18.3.0", "tailwindcss": "^4.1.11", "vite": "^7.0.4", "wouter": "^3.7.1", "ws": "^8.18.0", "zod": "^3.25.76", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/express-session": "^1.18.0", "@types/jsonwebtoken": "^9.0.9", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.13", "@types/node": "^20.19.7", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/spotify-web-api-node": "^5.0.11", "@types/ws": "^8.5.13", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.5", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "^5.8.3"}}