// server/index.ts
import dotenv2 from "dotenv";
import express2 from "express";

// server/routes.ts
import express from "express";
import { createServer } from "http";
import <PERSON><PERSON> from "stripe";
import multer from "multer";
import path from "path";

// server/storage.ts
import {
  users,
  venues,
  events,
  menuCategories,
  menuItems,
  orders,
  orderItems,
  artistMerch,
  queueStatus
} from "@shared/schema";

// server/db.ts
import dotenv from "dotenv";
import { Pool, neonConfig } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-serverless";
import ws from "ws";
import * as schema from "@shared/schema";
dotenv.config();
neonConfig.webSocketConstructor = ws;
if (!process.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database?"
  );
}
var pool = new Pool({ connectionString: process.env.DATABASE_URL });
var db = drizzle({ client: pool, schema });

// server/storage.ts
import { eq, and } from "drizzle-orm";
var DatabaseStorage = class {
  async getUser(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || void 0;
  }
  async getUserByEmail(email) {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || void 0;
  }
  async getUserByUsername(username) {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || void 0;
  }
  async createUser(insertUser) {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }
  async updateUser(id, updates) {
    const [user] = await db.update(users).set(updates).where(eq(users.id, id)).returning();
    return user || void 0;
  }
  async getVenue(id) {
    const [venue] = await db.select().from(venues).where(eq(venues.id, id));
    return venue || void 0;
  }
  async getActiveVenues() {
    return await db.select().from(venues).where(eq(venues.isActive, true));
  }
  async createVenue(insertVenue) {
    const [venue] = await db.insert(venues).values(insertVenue).returning();
    return venue;
  }
  async getEvent(id) {
    const [event] = await db.select().from(events).where(eq(events.id, id));
    return event || void 0;
  }
  async getActiveEvents() {
    return await db.select().from(events).where(eq(events.isActive, true));
  }
  async getEventsByVenue(venueId) {
    return await db.select().from(events).where(eq(events.venueId, venueId));
  }
  async createEvent(insertEvent) {
    const [event] = await db.insert(events).values(insertEvent).returning();
    return event;
  }
  async getMenuCategories(eventId) {
    return await db.select().from(menuCategories).where(eq(menuCategories.eventId, eventId));
  }
  async getMenuItems(categoryId) {
    return await db.select().from(menuItems).where(eq(menuItems.categoryId, categoryId));
  }
  async getMenuItem(id) {
    const [item] = await db.select().from(menuItems).where(eq(menuItems.id, id));
    return item || void 0;
  }
  async createMenuItem(insertItem) {
    const [item] = await db.insert(menuItems).values(insertItem).returning();
    return item;
  }
  async updateMenuItemStock(id, stock) {
    const [item] = await db.update(menuItems).set({ stock }).where(eq(menuItems.id, id)).returning();
    return item || void 0;
  }
  async getOrder(id) {
    const [order] = await db.select().from(orders).where(eq(orders.id, id));
    return order || void 0;
  }
  async getOrdersByUser(userId) {
    return await db.select().from(orders).where(eq(orders.userId, userId));
  }
  async getOrdersByEvent(eventId) {
    return await db.select().from(orders).where(eq(orders.eventId, eventId));
  }
  async createOrder(insertOrder) {
    const [order] = await db.insert(orders).values(insertOrder).returning();
    return order;
  }
  async updateOrderStatus(id, status) {
    const [order] = await db.update(orders).set({ status, updatedAt: /* @__PURE__ */ new Date() }).where(eq(orders.id, id)).returning();
    return order || void 0;
  }
  async getOrderItems(orderId) {
    return await db.select().from(orderItems).where(eq(orderItems.orderId, orderId));
  }
  async createOrderItem(insertItem) {
    const [item] = await db.insert(orderItems).values(insertItem).returning();
    return item;
  }
  async getArtistMerch(artistId, eventId) {
    return await db.select().from(artistMerch).where(and(eq(artistMerch.artistId, artistId), eq(artistMerch.eventId, eventId)));
  }
  async getArtistMerchItem(id) {
    const [merch] = await db.select().from(artistMerch).where(eq(artistMerch.id, id));
    return merch || void 0;
  }
  async getEventMerch(eventId) {
    return await db.select().from(artistMerch).where(eq(artistMerch.eventId, eventId));
  }
  async createArtistMerch(insertMerch) {
    const [merch] = await db.insert(artistMerch).values(insertMerch).returning();
    return merch;
  }
  async updateMerchAvailability(id, isAvailable, stock) {
    const updateData = { isAvailable };
    if (stock !== void 0) {
      updateData.stock = stock;
    }
    const [merch] = await db.update(artistMerch).set(updateData).where(eq(artistMerch.id, id)).returning();
    return merch || void 0;
  }
  async getQueueStatus(eventId) {
    const [status] = await db.select().from(queueStatus).where(eq(queueStatus.eventId, eventId));
    return status || void 0;
  }
  async updateQueueStatus(eventId, insertStatus) {
    const existing = await this.getQueueStatus(eventId);
    if (existing) {
      const [status] = await db.update(queueStatus).set({ ...insertStatus, updatedAt: /* @__PURE__ */ new Date() }).where(eq(queueStatus.eventId, eventId)).returning();
      return status;
    } else {
      const [status] = await db.insert(queueStatus).values({ ...insertStatus, eventId, updatedAt: /* @__PURE__ */ new Date() }).returning();
      return status;
    }
  }
};
var storage = new DatabaseStorage();

// server/routes.ts
import { insertOrderSchema, insertOrderItemSchema, insertUserSchema } from "@shared/schema";

// server/auth.ts
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
var JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
async function hashPassword(password) {
  return bcrypt.hash(password, 12);
}
async function verifyPassword(password, hash) {
  return bcrypt.compare(password, hash);
}
function generateToken(user) {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role
    },
    JWT_SECRET,
    { expiresIn: "24h" }
  );
}
function verifyToken(token) {
  return jwt.verify(token, JWT_SECRET);
}
function authenticate(req, res, next) {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(" ")[1];
  if (!token) {
    return res.status(401).json({ message: "Access token required" });
  }
  try {
    const decoded = verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ message: "Invalid token" });
  }
}
function authorize(...roles) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: "Authentication required" });
    }
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: "Insufficient permissions" });
    }
    next();
  };
}

// server/spotify.ts
import SpotifyWebApi from "spotify-web-api-node";
if (!process.env.SPOTIFY_CLIENT_ID || !process.env.SPOTIFY_CLIENT_SECRET) {
  throw new Error("Missing required Spotify credentials: SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET");
}
var spotifyApi = new SpotifyWebApi({
  clientId: process.env.SPOTIFY_CLIENT_ID,
  clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
  redirectUri: process.env.SPOTIFY_REDIRECT_URI || "http://localhost:5000/api/auth/spotify/callback"
});
function getSpotifyAuthUrl(state) {
  const scopes = [
    "user-read-private",
    "user-read-email",
    "user-top-read",
    "user-read-recently-played"
  ];
  return spotifyApi.createAuthorizeURL(scopes, state);
}
async function exchangeCodeForTokens(code) {
  try {
    const data = await spotifyApi.authorizationCodeGrant(code);
    spotifyApi.setAccessToken(data.body.access_token);
    spotifyApi.setRefreshToken(data.body.refresh_token);
    return {
      accessToken: data.body.access_token,
      refreshToken: data.body.refresh_token,
      expiresIn: data.body.expires_in
    };
  } catch (error) {
    console.error("Error exchanging code for tokens:", error);
    throw new Error("Failed to authenticate with Spotify");
  }
}
async function getUserProfile(accessToken) {
  try {
    spotifyApi.setAccessToken(accessToken);
    const data = await spotifyApi.getMe();
    return {
      id: data.body.id,
      displayName: data.body.display_name,
      email: data.body.email,
      images: data.body.images,
      followers: data.body.followers?.total || 0,
      country: data.body.country
    };
  } catch (error) {
    console.error("Error fetching user profile:", error);
    throw new Error("Failed to fetch Spotify profile");
  }
}
async function searchArtists(query, accessToken) {
  try {
    spotifyApi.setAccessToken(accessToken);
    const data = await spotifyApi.searchArtists(query, { limit: 20 });
    return data.body.artists?.items.map((artist) => ({
      id: artist.id,
      name: artist.name,
      images: artist.images,
      genres: artist.genres,
      popularity: artist.popularity,
      followers: artist.followers.total,
      externalUrls: artist.external_urls
    })) || [];
  } catch (error) {
    console.error("Error searching artists:", error);
    throw new Error("Failed to search artists");
  }
}
async function getArtist(artistId, accessToken) {
  try {
    spotifyApi.setAccessToken(accessToken);
    const data = await spotifyApi.getArtist(artistId);
    return {
      id: data.body.id,
      name: data.body.name,
      images: data.body.images,
      genres: data.body.genres,
      popularity: data.body.popularity,
      followers: data.body.followers.total,
      externalUrls: data.body.external_urls
    };
  } catch (error) {
    console.error("Error fetching artist:", error);
    throw new Error("Failed to fetch artist details");
  }
}
async function getArtistTopTracks(artistId, accessToken, country = "US") {
  try {
    spotifyApi.setAccessToken(accessToken);
    const data = await spotifyApi.getArtistTopTracks(artistId, country);
    return data.body.tracks.map((track) => ({
      id: track.id,
      name: track.name,
      album: {
        id: track.album.id,
        name: track.album.name,
        images: track.album.images,
        releaseDate: track.album.release_date
      },
      artists: track.artists.map((artist) => ({
        id: artist.id,
        name: artist.name
      })),
      duration: track.duration_ms,
      popularity: track.popularity,
      previewUrl: track.preview_url,
      externalUrls: track.external_urls
    }));
  } catch (error) {
    console.error("Error fetching artist top tracks:", error);
    throw new Error("Failed to fetch artist top tracks");
  }
}
async function getUserTopArtists(accessToken, timeRange = "medium_term", limit = 20) {
  try {
    spotifyApi.setAccessToken(accessToken);
    const data = await spotifyApi.getMyTopArtists({
      time_range: timeRange,
      limit
    });
    return data.body.items.map((artist) => ({
      id: artist.id,
      name: artist.name,
      images: artist.images,
      genres: artist.genres,
      popularity: artist.popularity,
      followers: artist.followers.total
    }));
  } catch (error) {
    console.error("Error fetching user top artists:", error);
    throw new Error("Failed to fetch top artists");
  }
}
function generateMockEventData(artist) {
  const venues2 = [
    "Madison Square Garden",
    "The Forum",
    "Red Rocks Amphitheatre",
    "Hollywood Bowl",
    "The Fillmore",
    "House of Blues",
    "Terminal 5",
    "The Wiltern"
  ];
  const cities = [
    "New York, NY",
    "Los Angeles, CA",
    "Chicago, IL",
    "Austin, TX",
    "Nashville, TN",
    "Seattle, WA",
    "Boston, MA",
    "Denver, CO"
  ];
  const eventCount = Math.floor(Math.random() * 3) + 1;
  const events2 = [];
  for (let i = 0; i < eventCount; i++) {
    const venue = venues2[Math.floor(Math.random() * venues2.length)];
    const city = cities[Math.floor(Math.random() * cities.length)];
    const daysFromNow = Math.floor(Math.random() * 90) + 7;
    const eventDate = /* @__PURE__ */ new Date();
    eventDate.setDate(eventDate.getDate() + daysFromNow);
    events2.push({
      id: `event_${artist.id}_${i}`,
      artistId: artist.id,
      artistName: artist.name,
      venue,
      city,
      date: eventDate.toISOString(),
      ticketPrice: Math.floor(Math.random() * 100) + 25,
      // $25-$125
      availableTickets: Math.floor(Math.random() * 500) + 100,
      // 100-600 tickets
      description: `${artist.name} live at ${venue}`,
      image: artist.images?.[0]?.url || null
    });
  }
  return events2;
}
async function getClientCredentialsToken() {
  try {
    const data = await spotifyApi.clientCredentialsGrant();
    spotifyApi.setAccessToken(data.body.access_token);
    return {
      accessToken: data.body.access_token,
      expiresIn: data.body.expires_in
    };
  } catch (error) {
    console.error("Error getting client credentials token:", error);
    throw new Error("Failed to get Spotify app token");
  }
}

// server/printful.ts
import axios from "axios";
if (!process.env.PRINTFUL_API_KEY) {
  throw new Error("Missing required Printful credentials: PRINTFUL_API_KEY");
}
var printfulApi = axios.create({
  baseURL: "https://api.printful.com",
  headers: {
    "Authorization": `Bearer ${process.env.PRINTFUL_API_KEY}`,
    "Content-Type": "application/json"
  }
});
async function getAvailableProducts() {
  try {
    const response = await printfulApi.get("/products");
    return response.data.result.map((product) => ({
      id: product.id,
      type: product.type,
      title: product.title,
      description: product.description,
      image: product.image,
      variants: product.variants || []
    }));
  } catch (error) {
    console.error("Error fetching Printful products:", error.response?.data || error.message);
    throw new Error("Failed to fetch available products");
  }
}
async function getProductDetails(productId) {
  try {
    const response = await printfulApi.get(`/products/${productId}`);
    const product = response.data.result.product;
    const variants = response.data.result.variants;
    return {
      id: product.id,
      type: product.type,
      title: product.title,
      description: product.description,
      image: product.image,
      variants: variants.map((variant) => ({
        id: variant.id,
        name: variant.name,
        size: variant.size,
        color: variant.color,
        colorCode: variant.color_code,
        image: variant.image,
        price: variant.price,
        inStock: variant.in_stock
      }))
    };
  } catch (error) {
    console.error("Error fetching product details:", error.response?.data || error.message);
    throw new Error("Failed to fetch product details");
  }
}
async function uploadDesignFile(fileBuffer, fileName) {
  try {
    const formData = new FormData();
    const blob = new Blob([fileBuffer]);
    formData.append("file", blob, fileName);
    const response = await printfulApi.post("/files", formData, {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    });
    return {
      id: response.data.result.id,
      type: response.data.result.type,
      hash: response.data.result.hash,
      url: response.data.result.url,
      filename: response.data.result.filename,
      mime_type: response.data.result.mime_type,
      size: response.data.result.size,
      width: response.data.result.width,
      height: response.data.result.height
    };
  } catch (error) {
    console.error("Error uploading design file:", error.response?.data || error.message);
    throw new Error("Failed to upload design file");
  }
}
async function createStoreProduct(productData) {
  try {
    const storeProductData = {
      sync_product: {
        name: productData.name,
        thumbnail: "",
        // Will be auto-generated
        is_ignored: false
      },
      sync_variants: [
        {
          retail_price: productData.retailPrice.toFixed(2),
          variant_id: productData.variantId,
          is_ignored: false,
          files: [
            {
              id: productData.designFileId,
              type: "default",
              hash: "",
              // Will be filled by Printful
              url: "",
              // Will be filled by Printful
              filename: "design.png",
              mime_type: "image/png",
              size: 0,
              width: 0,
              height: 0,
              x: 0,
              y: 0,
              scale: 1,
              visible: true
            }
          ]
        }
      ]
    };
    const response = await printfulApi.post("/store/products", storeProductData);
    return {
      id: response.data.result.id,
      external_id: response.data.result.external_id,
      name: response.data.result.name,
      variants: response.data.result.sync_variants.map((variant) => ({
        id: variant.id,
        external_id: variant.external_id,
        variant_id: variant.variant_id,
        retail_price: variant.retail_price,
        sku: variant.sku,
        currency: variant.currency,
        product: variant.product,
        image: variant.files?.[0]?.preview_url || ""
      })),
      artistId: productData.artistId,
      eventId: productData.eventId
    };
  } catch (error) {
    console.error("Error creating store product:", error.response?.data || error.message);
    throw new Error("Failed to create store product");
  }
}
async function createPrintfulOrder(orderData) {
  try {
    const printfulOrderData = {
      external_id: orderData.externalId,
      shipping: "STANDARD",
      recipient: orderData.recipient,
      items: orderData.items.map((item) => ({
        sync_variant_id: item.sync_variant_id,
        quantity: item.quantity,
        retail_price: item.retail_price.toFixed(2)
      })),
      packing_slip: orderData.packing_slip
    };
    const response = await printfulApi.post("/orders", printfulOrderData);
    return {
      id: response.data.result.id,
      external_id: response.data.result.external_id,
      status: response.data.result.status,
      shipping: response.data.result.shipping,
      created: response.data.result.created,
      updated: response.data.result.updated,
      recipient: response.data.result.recipient,
      items: response.data.result.items,
      costs: response.data.result.costs,
      retail_costs: response.data.result.retail_costs,
      shipments: response.data.result.shipments || []
    };
  } catch (error) {
    console.error("Error creating Printful order:", error.response?.data || error.message);
    throw new Error("Failed to create Printful order");
  }
}
async function getOrderStatus(orderId) {
  try {
    const response = await printfulApi.get(`/orders/${orderId}`);
    return {
      id: response.data.result.id,
      external_id: response.data.result.external_id,
      status: response.data.result.status,
      shipping: response.data.result.shipping,
      created: response.data.result.created,
      updated: response.data.result.updated,
      shipments: response.data.result.shipments?.map((shipment) => ({
        id: shipment.id,
        carrier: shipment.carrier,
        service: shipment.service,
        tracking_number: shipment.tracking_number,
        tracking_url: shipment.tracking_url,
        created: shipment.created,
        ship_date: shipment.ship_date,
        shipped_at: shipment.shipped_at,
        reshipment: shipment.reshipment
      })) || []
    };
  } catch (error) {
    console.error("Error fetching order status:", error.response?.data || error.message);
    throw new Error("Failed to fetch order status");
  }
}
async function calculateShipping(orderData) {
  try {
    const response = await printfulApi.post("/shipping/rates", orderData);
    return response.data.result.map((rate) => ({
      id: rate.id,
      name: rate.name,
      rate: rate.rate,
      currency: rate.currency,
      minDeliveryDays: rate.minDeliveryDays,
      maxDeliveryDays: rate.maxDeliveryDays
    }));
  } catch (error) {
    console.error("Error calculating shipping:", error.response?.data || error.message);
    throw new Error("Failed to calculate shipping costs");
  }
}
function processWebhook(webhookData) {
  const { type, created, retries, store, data } = webhookData;
  switch (type) {
    case "order_updated":
      return {
        type: "order_status_changed",
        orderId: data.order.id,
        externalId: data.order.external_id,
        status: data.order.status,
        shipments: data.order.shipments || []
      };
    case "order_failed":
      return {
        type: "order_failed",
        orderId: data.order.id,
        externalId: data.order.external_id,
        reason: data.reason || "Unknown error"
      };
    case "package_shipped":
      return {
        type: "package_shipped",
        orderId: data.order.id,
        externalId: data.order.external_id,
        shipment: data.shipment
      };
    default:
      return {
        type: "unknown",
        originalType: type,
        data
      };
  }
}

// server/canva.ts
import axios2 from "axios";
import crypto from "crypto";
if (!process.env.CANVA_CLIENT_ID || !process.env.CANVA_CLIENT_SECRET) {
  throw new Error("Missing required Canva credentials: CANVA_CLIENT_ID and CANVA_CLIENT_SECRET");
}
var CANVA_API_BASE = "https://api.canva.com/rest/v1";
function generateCanvaAuthUrl(userId, artistId) {
  const state = crypto.randomBytes(32).toString("hex");
  const stateData = {
    userId,
    artistId,
    timestamp: Date.now(),
    nonce: state
  };
  const encodedState = Buffer.from(JSON.stringify(stateData)).toString("base64");
  const authUrl = new URL("https://www.canva.com/api/oauth/authorize");
  authUrl.searchParams.set("client_id", process.env.CANVA_CLIENT_ID);
  authUrl.searchParams.set("redirect_uri", process.env.CANVA_REDIRECT_URI);
  authUrl.searchParams.set("response_type", "code");
  authUrl.searchParams.set("state", encodedState);
  authUrl.searchParams.set("scope", "design:read design:write asset:read asset:write folder:read folder:write");
  return {
    authUrl: authUrl.toString(),
    state: encodedState
  };
}
async function exchangeCanvaCode(code, state) {
  try {
    const stateData = JSON.parse(Buffer.from(state, "base64").toString());
    if (Date.now() - stateData.timestamp > 10 * 60 * 1e3) {
      throw new Error("State parameter expired");
    }
    const tokenResponse = await axios2.post("https://api.canva.com/rest/v1/oauth/token", {
      grant_type: "authorization_code",
      client_id: process.env.CANVA_CLIENT_ID,
      client_secret: process.env.CANVA_CLIENT_SECRET,
      redirect_uri: process.env.CANVA_REDIRECT_URI,
      code
    }, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    });
    const { access_token, refresh_token, expires_in, scope } = tokenResponse.data;
    const userResponse = await axios2.get(`${CANVA_API_BASE}/users/me`, {
      headers: {
        "Authorization": `Bearer ${access_token}`
      }
    });
    return {
      accessToken: access_token,
      refreshToken: refresh_token,
      expiresIn: expires_in,
      scope,
      canvaUser: userResponse.data,
      userId: stateData.userId,
      artistId: stateData.artistId
    };
  } catch (error) {
    console.error("Error exchanging Canva code:", error.response?.data || error.message);
    throw new Error("Failed to authenticate with Canva");
  }
}
async function createCanvaDesign(accessToken, designType = "presentation", title = "Merchandise Design") {
  try {
    const response = await axios2.post(`${CANVA_API_BASE}/designs`, {
      design_type: designType,
      title
    }, {
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json"
      }
    });
    return {
      id: response.data.design.id,
      title: response.data.design.title,
      designType: response.data.design.design_type,
      createdAt: response.data.design.created_at,
      updatedAt: response.data.design.updated_at,
      urls: response.data.design.urls
    };
  } catch (error) {
    console.error("Error creating Canva design:", error.response?.data || error.message);
    throw new Error("Failed to create design in Canva");
  }
}
async function getUserDesigns(accessToken, limit = 20) {
  try {
    const response = await axios2.get(`${CANVA_API_BASE}/designs`, {
      headers: {
        "Authorization": `Bearer ${accessToken}`
      },
      params: {
        limit
      }
    });
    return response.data.items.map((design) => ({
      id: design.id,
      title: design.title,
      designType: design.design_type,
      createdAt: design.created_at,
      updatedAt: design.updated_at,
      thumbnail: design.thumbnail?.url,
      urls: design.urls
    }));
  } catch (error) {
    console.error("Error fetching user designs:", error.response?.data || error.message);
    throw new Error("Failed to fetch designs from Canva");
  }
}
async function exportCanvaDesign(accessToken, designId, format = "PNG") {
  try {
    const response = await axios2.post(`${CANVA_API_BASE}/designs/${designId}/export`, {
      format: {
        type: format.toLowerCase(),
        ...format.toUpperCase() === "PNG" && { quality: "high", dpi: 300 }
      }
    }, {
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Content-Type": "application/json"
      }
    });
    return {
      jobId: response.data.job.id,
      status: response.data.job.status,
      designId,
      format
    };
  } catch (error) {
    console.error("Error exporting Canva design:", error.response?.data || error.message);
    throw new Error("Failed to export design from Canva");
  }
}
async function getExportJobStatus(accessToken, jobId) {
  try {
    const response = await axios2.get(`${CANVA_API_BASE}/exports/${jobId}`, {
      headers: {
        "Authorization": `Bearer ${accessToken}`
      }
    });
    return {
      jobId: response.data.job.id,
      status: response.data.job.status,
      result: response.data.job.result,
      downloadUrl: response.data.job.result?.url,
      error: response.data.job.error
    };
  } catch (error) {
    console.error("Error getting export job status:", error.response?.data || error.message);
    throw new Error("Failed to get export job status");
  }
}
async function processCanvaExport(exportData, artistId, eventId) {
  try {
    const response = await axios2.get(exportData.exportUrl, {
      responseType: "arraybuffer"
    });
    const designBuffer = Buffer.from(response.data);
    const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-");
    const filename = `canva-design-${exportData.designId}-${timestamp}.${exportData.format.toLowerCase()}`;
    return {
      designId: exportData.designId,
      title: exportData.title,
      filename,
      buffer: designBuffer,
      format: exportData.format,
      dimensions: exportData.dimensions,
      artistId,
      eventId,
      source: "canva",
      createdAt: (/* @__PURE__ */ new Date()).toISOString()
    };
  } catch (error) {
    console.error("Error processing Canva export:", error.message);
    throw new Error("Failed to process design export from Canva");
  }
}
function processCanvaWebhook(webhookData) {
  const { type, timestamp, data } = webhookData;
  switch (type) {
    case "design.exported":
      return {
        type: "design_exported",
        designId: data.design.id,
        exportUrl: data.export.url,
        title: data.design.title,
        format: data.export.format,
        dimensions: data.export.dimensions,
        userId: data.user.id,
        timestamp
      };
    case "design.created":
      return {
        type: "design_created",
        designId: data.design.id,
        title: data.design.title,
        userId: data.user.id,
        timestamp
      };
    case "design.updated":
      return {
        type: "design_updated",
        designId: data.design.id,
        title: data.design.title,
        userId: data.user.id,
        timestamp
      };
    default:
      return {
        type: "unknown",
        originalType: type,
        data,
        timestamp
      };
  }
}
function createMerchTemplate(merchType) {
  const templates = {
    "t-shirt": {
      dimensions: { width: 3e3, height: 3600 },
      printArea: { width: 2400, height: 3e3 },
      safeArea: { width: 2200, height: 2800 },
      description: "Standard t-shirt design template"
    },
    "hoodie": {
      dimensions: { width: 3e3, height: 3600 },
      printArea: { width: 2400, height: 3e3 },
      safeArea: { width: 2200, height: 2800 },
      description: "Hoodie design template"
    },
    "mug": {
      dimensions: { width: 2700, height: 1080 },
      printArea: { width: 2500, height: 900 },
      safeArea: { width: 2300, height: 800 },
      description: "Coffee mug wrap-around design"
    },
    "poster": {
      dimensions: { width: 2400, height: 3600 },
      printArea: { width: 2200, height: 3400 },
      safeArea: { width: 2e3, height: 3200 },
      description: "Concert poster design template"
    },
    "sticker": {
      dimensions: { width: 1200, height: 1200 },
      printArea: { width: 1100, height: 1100 },
      safeArea: { width: 1e3, height: 1e3 },
      description: "Square sticker design template"
    }
  };
  const template = templates[merchType];
  if (!template) {
    throw new Error(`Unsupported merchandise type: ${merchType}`);
  }
  return {
    type: merchType,
    ...template,
    guidelines: {
      dpi: 300,
      colorMode: "RGB",
      fileFormat: "PNG",
      transparentBackground: true,
      bleed: 0.125,
      // 1/8 inch bleed for print
      notes: [
        "Keep important elements within the safe area",
        "Use high contrast colors for better print quality",
        "Avoid very thin lines (less than 1pt)",
        "Text should be at least 8pt for readability"
      ]
    }
  };
}
function generateArtistBrand(artistData) {
  const genreColors = {
    "rock": ["#000000", "#FF0000", "#FFFFFF"],
    "pop": ["#FF69B4", "#00BFFF", "#FFD700"],
    "hip-hop": ["#000000", "#FFD700", "#FF0000"],
    "electronic": ["#00FFFF", "#FF00FF", "#00FF00"],
    "country": ["#8B4513", "#DAA520", "#FFFFFF"],
    "jazz": ["#4B0082", "#FFD700", "#000000"],
    "classical": ["#000080", "#C0C0C0", "#FFFFFF"],
    "alternative": ["#696969", "#FF4500", "#FFFFFF"]
  };
  const primaryGenre = artistData.genres[0]?.toLowerCase() || "rock";
  const colors = genreColors[primaryGenre] || genreColors.rock;
  return {
    artist: {
      id: artistData.id,
      name: artistData.name,
      image: artistData.images[0]?.url || null
    },
    colors: [
      artistData.primaryColor || colors[0],
      artistData.secondaryColor || colors[1],
      ...colors.slice(2),
      "#000000",
      // Always include black
      "#FFFFFF"
      // Always include white
    ],
    fonts: [
      "Impact",
      // Bold, attention-grabbing
      "Arial Black",
      // Strong, readable
      "Helvetica",
      // Clean, modern
      "Times New Roman",
      // Classic, elegant
      "Courier New"
      // Typewriter, vintage
    ],
    templates: {
      logoPlacement: {
        corner: { x: 50, y: 50, size: 200 },
        center: { x: 1500, y: 1800, size: 400 },
        bottom: { x: 1500, y: 3200, size: 300 }
      },
      textStyles: {
        headline: { size: 72, weight: "bold", color: colors[0] },
        subheading: { size: 48, weight: "normal", color: colors[1] },
        body: { size: 24, weight: "normal", color: "#000000" }
      }
    }
  };
}
function validateDesignForPrint(designData) {
  const issues = [];
  const warnings = [];
  if (designData.dimensions.width < 1200 || designData.dimensions.height < 1200) {
    issues.push("Design dimensions too small for quality printing (minimum 1200x1200px)");
  }
  if (designData.dpi && designData.dpi < 300) {
    warnings.push("DPI below 300 may result in lower print quality");
  }
  if (!["PNG", "JPG", "JPEG", "PDF"].includes(designData.format.toUpperCase())) {
    issues.push("Unsupported file format for printing");
  }
  if (designData.colorMode && designData.colorMode !== "RGB") {
    warnings.push("CMYK color mode recommended for print, but RGB is acceptable");
  }
  return {
    isValid: issues.length === 0,
    issues,
    warnings,
    recommendations: [
      "Use 300 DPI for best print quality",
      "Keep important elements away from edges",
      "Use high contrast colors",
      "Test design on different background colors"
    ]
  };
}

// server/routes.ts
if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("Missing required Stripe secret: STRIPE_SECRET_KEY");
}
var stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: "2023-10-16"
});
var upload = multer({
  storage: multer.diskStorage({
    destination: "./uploads/",
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
      cb(null, file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname));
    }
  }),
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error("Only image files are allowed"));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 }
  // 5MB limit
});
async function registerRoutes(app2) {
  app2.post("/api/auth/register", async (req, res) => {
    try {
      const { email, password, role, firstName, lastName, username } = req.body;
      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }
      const passwordHash = await hashPassword(password);
      const user = await storage.createUser({
        email,
        passwordHash,
        role: role || "guest",
        firstName,
        lastName,
        username,
        isGuest: false
      });
      const token = generateToken({ id: user.id, email: user.email, role: user.role || "guest" });
      res.status(201).json({
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      const user = await storage.getUserByEmail(email);
      if (!user || !user.passwordHash) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      const isValid = await verifyPassword(password, user.passwordHash);
      if (!isValid) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      const token = generateToken({ id: user.id, email: user.email, role: user.role || "guest" });
      res.json({
        token,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          firstName: user.firstName,
          lastName: user.lastName
        }
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/auth/me", authenticate, async (req, res) => {
    try {
      const user = await storage.getUser(req.user.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json({
        id: user.id,
        email: user.email,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/auth/spotify", (req, res) => {
    try {
      const state = Math.random().toString(36).substring(2, 15);
      const authUrl = getSpotifyAuthUrl(state);
      res.json({ authUrl, state });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/auth/spotify/callback", async (req, res) => {
    try {
      const { code, state, error } = req.query;
      if (error) {
        return res.status(400).json({ message: `Spotify auth error: ${error}` });
      }
      if (!code) {
        return res.status(400).json({ message: "No authorization code received" });
      }
      const tokens = await exchangeCodeForTokens(code);
      const profile = await getUserProfile(tokens.accessToken);
      let user = await storage.getUserByEmail(profile.email || `${profile.id}@spotify.local`);
      if (!user) {
        user = await storage.createUser({
          email: profile.email || `${profile.id}@spotify.local`,
          passwordHash: "",
          // No password for Spotify users
          role: "guest",
          firstName: profile.displayName?.split(" ")[0] || "Spotify",
          lastName: profile.displayName?.split(" ").slice(1).join(" ") || "User",
          username: profile.id,
          isGuest: false,
          spotifyId: profile.id,
          spotifyAccessToken: tokens.accessToken,
          spotifyRefreshToken: tokens.refreshToken
        });
      } else {
        await storage.updateUser(user.id, {
          spotifyId: profile.id,
          spotifyAccessToken: tokens.accessToken,
          spotifyRefreshToken: tokens.refreshToken
        });
      }
      const jwtToken = generateToken({
        id: user.id,
        email: user.email,
        role: user.role || "guest"
      });
      res.redirect(`http://localhost:5000/?token=${jwtToken}&spotify=true`);
    } catch (error) {
      console.error("Spotify callback error:", error);
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/spotify/profile", authenticate, async (req, res) => {
    try {
      const user = await storage.getUser(req.user.id);
      if (!user?.spotifyAccessToken) {
        return res.status(400).json({ message: "User not connected to Spotify" });
      }
      const profile = await getUserProfile(user.spotifyAccessToken);
      res.json(profile);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/spotify/search/artists", async (req, res) => {
    try {
      const { q } = req.query;
      if (!q) {
        return res.status(400).json({ message: "Query parameter 'q' is required" });
      }
      const { accessToken } = await getClientCredentialsToken();
      const artists = await searchArtists(q, accessToken);
      res.json(artists);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/spotify/artists/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const { accessToken } = await getClientCredentialsToken();
      const artist = await getArtist(id, accessToken);
      const topTracks = await getArtistTopTracks(id, accessToken);
      const mockEvents = generateMockEventData(artist);
      res.json({
        artist,
        topTracks,
        events: mockEvents
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/spotify/me/top-artists", authenticate, async (req, res) => {
    try {
      const user = await storage.getUser(req.user.id);
      if (!user?.spotifyAccessToken) {
        return res.status(400).json({ message: "User not connected to Spotify" });
      }
      const topArtists = await getUserTopArtists(user.spotifyAccessToken);
      res.json(topArtists);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/upload", authenticate, authorize("artist", "admin"), upload.single("image"), (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }
      const imageUrl = `/uploads/${req.file.filename}`;
      res.json({ imageUrl });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  const staticMiddleware = express.static("uploads");
  app2.use("/uploads", staticMiddleware);
  app2.get("/api/venues", async (req, res) => {
    try {
      const venues2 = await storage.getActiveVenues();
      res.json(venues2);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/venues/:id", async (req, res) => {
    try {
      const venue = await storage.getVenue(parseInt(req.params.id));
      if (!venue) {
        return res.status(404).json({ message: "Venue not found" });
      }
      res.json(venue);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/events", async (req, res) => {
    try {
      const events2 = await storage.getActiveEvents();
      res.json(events2);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/events/:id", async (req, res) => {
    try {
      const event = await storage.getEvent(parseInt(req.params.id));
      if (!event) {
        return res.status(404).json({ message: "Event not found" });
      }
      res.json(event);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/events/:eventId/menu", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const categories = await storage.getMenuCategories(eventId);
      const menuWithItems = await Promise.all(
        categories.map(async (category) => {
          const items = await storage.getMenuItems(category.id);
          return { ...category, items };
        })
      );
      res.json(menuWithItems);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/menu-items/:id", async (req, res) => {
    try {
      const item = await storage.getMenuItem(parseInt(req.params.id));
      if (!item) {
        return res.status(404).json({ message: "Menu item not found" });
      }
      res.json(item);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/users", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const user = await storage.createUser(userData);
      res.status(201).json(user);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.get("/api/users/:id", async (req, res) => {
    try {
      const user = await storage.getUser(parseInt(req.params.id));
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/orders", async (req, res) => {
    try {
      const { items, ...orderData } = req.body;
      const validatedOrder = insertOrderSchema.parse(orderData);
      const order = await storage.createOrder(validatedOrder);
      if (items && Array.isArray(items)) {
        for (const item of items) {
          const orderItem = insertOrderItemSchema.parse({
            ...item,
            orderId: order.id
          });
          await storage.createOrderItem(orderItem);
          const menuItem = await storage.getMenuItem(item.menuItemId);
          if (menuItem) {
            await storage.updateMenuItemStock(
              item.menuItemId,
              Math.max(0, menuItem.stock - item.quantity)
            );
          }
        }
      }
      const queueStatus2 = await storage.getQueueStatus(order.eventId);
      if (queueStatus2) {
        await storage.updateQueueStatus(order.eventId, {
          preparing: queueStatus2.preparing + 1,
          ready: queueStatus2.ready,
          avgWaitTime: queueStatus2.avgWaitTime
        });
      }
      res.status(201).json(order);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  });
  app2.get("/api/orders/:id", async (req, res) => {
    try {
      const orderId = parseInt(req.params.id);
      const order = await storage.getOrder(orderId);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }
      const items = await storage.getOrderItems(orderId);
      const itemsWithDetails = await Promise.all(
        items.map(async (item) => {
          const menuItem = await storage.getMenuItem(item.menuItemId);
          return { ...item, menuItem };
        })
      );
      res.json({ ...order, items: itemsWithDetails });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.patch("/api/orders/:id/status", async (req, res) => {
    try {
      const { status } = req.body;
      const orderId = parseInt(req.params.id);
      const order = await storage.updateOrderStatus(orderId, status);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }
      const queueStatus2 = await storage.getQueueStatus(order.eventId);
      if (queueStatus2) {
        let updates = { ...queueStatus2 };
        if (status === "ready") {
          updates.preparing = Math.max(0, updates.preparing - 1);
          updates.ready = updates.ready + 1;
        } else if (status === "completed") {
          updates.ready = Math.max(0, updates.ready - 1);
        }
        await storage.updateQueueStatus(order.eventId, updates);
      }
      res.json(order);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/events/:eventId/orders", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const orders2 = await storage.getOrdersByEvent(eventId);
      const ordersWithItems = await Promise.all(
        orders2.map(async (order) => {
          const items = await storage.getOrderItems(order.id);
          return { ...order, items };
        })
      );
      res.json(ordersWithItems);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/events/:eventId/queue-status", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const status = await storage.getQueueStatus(eventId);
      if (!status) {
        return res.status(404).json({ message: "Queue status not found" });
      }
      res.json(status);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/create-payment-intent", async (req, res) => {
    try {
      const { amount, eventId, userId } = req.body;
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100),
        // Convert to cents
        currency: "usd",
        metadata: {
          eventId: eventId.toString(),
          userId: userId?.toString() || "guest"
        }
      });
      res.json({ clientSecret: paymentIntent.client_secret });
    } catch (error) {
      res.status(500).json({ message: "Error creating payment intent: " + error.message });
    }
  });
  app2.post("/api/merch/upload", authenticate, authorize("artist", "admin"), upload.single("image"), async (req, res) => {
    try {
      const { name, description, price, stock, eventId, category, sizes, isPresaleOnly, isVipExclusive } = req.body;
      if (!req.file) {
        return res.status(400).json({ message: "Image file is required" });
      }
      const imageUrl = `/uploads/${req.file.filename}`;
      const merch = await storage.createArtistMerch({
        artistId: req.user.id,
        eventId: parseInt(eventId),
        name,
        description,
        price: parseFloat(price),
        stock: parseInt(stock),
        imageUrl,
        category: category || "apparel",
        sizes: sizes ? JSON.parse(sizes) : null,
        isPresaleOnly: isPresaleOnly === "true",
        isVipExclusive: isVipExclusive === "true",
        isAvailable: true
      });
      res.status(201).json(merch);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/merch/generate", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { artistId, eventId, style, colors, merchType } = req.body;
      const generatedMerch = {
        id: Date.now(),
        artistId: artistId || req.user.id,
        eventId: parseInt(eventId),
        name: `AI Generated ${merchType || "T-Shirt"}`,
        description: `Custom ${merchType || "T-Shirt"} design generated with AI for this event`,
        price: 25,
        stock: 100,
        imageUrl: "/uploads/ai-generated-placeholder.jpg",
        // Placeholder
        category: "apparel",
        sizes: ["S", "M", "L", "XL"],
        isPresaleOnly: false,
        isVipExclusive: false,
        isAvailable: true,
        aiGenerated: true,
        generationParams: {
          style,
          colors,
          merchType,
          prompt: `${style} style ${merchType} with ${colors} colors`
        }
      };
      res.status(201).json({
        message: "AI merchandise generation initiated",
        merch: generatedMerch,
        status: "pending_generation",
        estimatedTime: "2-5 minutes"
      });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/orders/new", authenticate, async (req, res) => {
    try {
      const { eventId, items, deliveryMethod, pickupLocation, specialInstructions } = req.body;
      let totalAmount = 0;
      const orderItems2 = [];
      for (const item of items) {
        let itemPrice = 0;
        let itemName = "";
        if (item.type === "menu") {
          const menuItem = await storage.getMenuItem(item.id);
          if (!menuItem) {
            return res.status(400).json({ message: `Menu item ${item.id} not found` });
          }
          itemPrice = parseFloat(menuItem.price);
          itemName = menuItem.name;
        } else if (item.type === "merch") {
          const merchItem = await storage.getArtistMerchItem(item.id);
          if (!merchItem) {
            return res.status(400).json({ message: `Merch item ${item.id} not found` });
          }
          itemPrice = parseFloat(merchItem.price);
          itemName = merchItem.name;
        }
        const itemTotal = itemPrice * item.quantity;
        totalAmount += itemTotal;
        orderItems2.push({
          itemId: item.id,
          itemType: item.type,
          itemName,
          quantity: item.quantity,
          unitPrice: itemPrice,
          totalPrice: itemTotal,
          customizations: item.customizations || {}
        });
      }
      const order = await storage.createOrder({
        userId: req.user.id,
        eventId: parseInt(eventId),
        totalAmount,
        status: "pending",
        deliveryMethod: deliveryMethod || "pickup",
        pickupLocation,
        specialInstructions,
        qrCode: `ORDER_${Date.now()}_${req.user.id}`,
        items: orderItems2
      });
      res.status(201).json(order);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/artists/:artistId/events/:eventId/merch", async (req, res) => {
    try {
      const artistId = parseInt(req.params.artistId);
      const eventId = parseInt(req.params.eventId);
      const merch = await storage.getArtistMerch(artistId, eventId);
      res.json(merch);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/events/:eventId/merch", async (req, res) => {
    try {
      const eventId = parseInt(req.params.eventId);
      const merch = await storage.getEventMerch(eventId);
      res.json(merch);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.patch("/api/merch/:id/availability", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const merchId = parseInt(req.params.id);
      const { isAvailable, stock } = req.body;
      const merch = await storage.updateMerchAvailability(merchId, isAvailable, stock);
      if (!merch) {
        return res.status(404).json({ message: "Merchandise not found" });
      }
      res.json(merch);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/printful/products", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const products = await getAvailableProducts();
      res.json(products);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/printful/products/:id", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      const productDetails = await getProductDetails(productId);
      res.json(productDetails);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/printful/store-products", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { name, description, variantId, designFileId, retailPrice, eventId } = req.body;
      const storeProduct = await createStoreProduct({
        name,
        description,
        variantId: parseInt(variantId),
        designFileId: parseInt(designFileId),
        retailPrice: parseFloat(retailPrice),
        artistId: req.user.id.toString(),
        eventId: eventId.toString()
      });
      res.status(201).json(storeProduct);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/printful/upload-design", authenticate, authorize("artist", "admin"), upload.single("design"), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "Design file is required" });
      }
      const fileInfo = await uploadDesignFile(req.file.buffer, req.file.originalname);
      res.status(201).json(fileInfo);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/printful/shipping", async (req, res) => {
    try {
      const { recipient, items } = req.body;
      const shippingRates = await calculateShipping({ recipient, items });
      res.json(shippingRates);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/printful/orders", authenticate, async (req, res) => {
    try {
      const orderData = req.body;
      const printfulOrder = await createPrintfulOrder(orderData);
      res.status(201).json(printfulOrder);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/printful/orders/:id", authenticate, async (req, res) => {
    try {
      const orderId = parseInt(req.params.id);
      const orderStatus = await getOrderStatus(orderId);
      res.json(orderStatus);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/printful/webhook", async (req, res) => {
    try {
      const webhookData = req.body;
      const processedData = processWebhook(webhookData);
      console.log("Printful webhook received:", processedData);
      res.status(200).json({ message: "Webhook processed successfully" });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/canva/auth", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { artistId } = req.query;
      const authData = generateCanvaAuthUrl(
        req.user.id.toString(),
        artistId || req.user.id.toString()
      );
      res.json(authData);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/canva/oauth/redirect", async (req, res) => {
    try {
      const { code, state, error } = req.query;
      if (error) {
        return res.status(400).json({ message: `Canva auth error: ${error}` });
      }
      if (!code || !state) {
        return res.status(400).json({ message: "Missing authorization code or state" });
      }
      const authResult = await exchangeCanvaCode(code, state);
      res.redirect(`${process.env.CANVA_BASE_URL}/?canva_auth=success&user_id=${authResult.userId}`);
    } catch (error) {
      console.error("Canva OAuth callback error:", error);
      res.redirect(`${process.env.CANVA_BASE_URL}/?canva_auth=error&message=${encodeURIComponent(error.message)}`);
    }
  });
  app2.get("/api/canva/return-nav", async (req, res) => {
    try {
      res.redirect(`${process.env.CANVA_BASE_URL}/dashboard?tab=designs`);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/canva/designs", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { designType, title, accessToken } = req.body;
      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }
      const design = await createCanvaDesign(accessToken, designType, title);
      res.status(201).json(design);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/canva/designs", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { accessToken, limit } = req.query;
      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }
      const designs = await getUserDesigns(accessToken, parseInt(limit) || 20);
      res.json(designs);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/canva/designs/:id/export", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { id } = req.params;
      const { accessToken, format } = req.body;
      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }
      const exportJob = await exportCanvaDesign(accessToken, id, format || "PNG");
      res.status(201).json(exportJob);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/canva/exports/:jobId", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { jobId } = req.params;
      const { accessToken } = req.query;
      if (!accessToken) {
        return res.status(400).json({ message: "Canva access token required" });
      }
      const jobStatus = await getExportJobStatus(accessToken, jobId);
      res.json(jobStatus);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/canva/export", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { exportData, artistId, eventId } = req.body;
      const processedDesign = await processCanvaExport(
        exportData,
        artistId || req.user.id.toString(),
        eventId
      );
      res.status(201).json(processedDesign);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.get("/api/canva/templates/:merchType", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { merchType } = req.params;
      const template = createMerchTemplate(merchType);
      res.json(template);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/canva/artist-brand", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { artistData } = req.body;
      const brandConfig = generateArtistBrand(artistData);
      res.json(brandConfig);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/canva/validate-design", authenticate, authorize("artist", "admin"), async (req, res) => {
    try {
      const { designData } = req.body;
      const validation = validateDesignForPrint(designData);
      res.json(validation);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  app2.post("/api/canva/webhook", async (req, res) => {
    try {
      const webhookData = req.body;
      const processedData = processCanvaWebhook(webhookData);
      console.log("Canva webhook received:", processedData);
      res.status(200).json({ message: "Webhook processed successfully" });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  });
  const httpServer = createServer(app2);
  setInterval(async () => {
    try {
      const events2 = await storage.getActiveEvents();
      for (const event of events2) {
        const currentStatus = await storage.getQueueStatus(event.id);
        if (currentStatus) {
          const preparing = Math.max(0, currentStatus.preparing + Math.floor(Math.random() * 3) - 1);
          const ready = Math.max(0, currentStatus.ready + Math.floor(Math.random() * 2) - 1);
          const avgWaitTime = Math.max(5, Math.min(20, currentStatus.avgWaitTime + Math.floor(Math.random() * 6) - 3));
          await storage.updateQueueStatus(event.id, {
            preparing,
            ready,
            avgWaitTime
          });
        }
      }
    } catch (error) {
      console.error("Queue update error:", error);
    }
  }, 1e4);
  return httpServer;
}

// server/index.ts
dotenv2.config();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path2 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path2.startsWith("/api")) {
      let logLine = `${req.method} ${path2} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  const port = process.env.PORT || 3e3;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();
