// This file is auto-generated by @hey-api/openapi-ts

import {
  createClient,
  createConfig,
  type Options,
  urlSearchParamsBodySerializer,
} from "@hey-api/client-fetch";
import type {
  GetAppJwksData,
  GetAppJwksError,
  GetAppJwksResponse2,
  DeleteAssetData,
  DeleteAssetError,
  DeleteAssetResponse,
  GetAssetData,
  GetAssetError,
  GetAssetResponse2,
  UpdateAssetData,
  UpdateAssetError,
  UpdateAssetResponse2,
  CreateAssetUploadJobData,
  CreateAssetUploadJobError,
  CreateAssetUploadJobResponse2,
  GetAssetUploadJobData,
  GetAssetUploadJobError,
  GetAssetUploadJobResponse2,
  CreateDesignAutofillJobData,
  CreateDesignAutofillJobError,
  CreateDesignAutofillJobResponse2,
  GetDesignAutofillJobData,
  GetDesignAutofillJobError,
  GetDesignAutofillJobResponse2,
  List<PERSON>randTemplatesData,
  ListBrandTemplatesError,
  ListBrandTemplatesResponse2,
  GetBrandTemplateData,
  GetBrandTemplateError,
  GetBrandTemplateResponse2,
  GetBrandTemplateDatasetData,
  GetBrandTemplateDatasetError,
  GetBrandTemplateDatasetResponse2,
  CreateCommentData,
  CreateCommentError,
  CreateCommentResponse2,
  CreateReplyDeprecatedData,
  CreateReplyDeprecatedError,
  CreateReplyDeprecatedResponse,
  ListRepliesData,
  ListRepliesError,
  ListRepliesResponse2,
  CreateReplyData,
  CreateReplyError,
  CreateReplyResponse2,
  GetThreadData,
  GetThreadError,
  GetThreadResponse2,
  GetReplyData,
  GetReplyError,
  GetReplyResponse2,
  CreateThreadData,
  CreateThreadError,
  CreateThreadResponse2,
  GetSigningPublicKeysData,
  GetSigningPublicKeysError,
  GetSigningPublicKeysResponse2,
  ListDesignsData,
  ListDesignsError,
  ListDesignsResponse,
  CreateDesignData,
  CreateDesignError,
  CreateDesignResponse2,
  GetDesignData,
  GetDesignError,
  GetDesignResponse2,
  GetDesignPagesData,
  GetDesignPagesError,
  GetDesignPagesResponse2,
  GetDesignExportFormatsData,
  GetDesignExportFormatsError,
  GetDesignExportFormatsResponse2,
  CreateDesignImportJobData,
  CreateDesignImportJobError,
  CreateDesignImportJobResponse2,
  GetDesignImportJobData,
  GetDesignImportJobError,
  GetDesignImportJobResponse2,
  CreateUrlImportJobData,
  CreateUrlImportJobError,
  CreateUrlImportJobResponse2,
  GetUrlImportJobData,
  GetUrlImportJobError,
  GetUrlImportJobResponse2,
  CreateDesignExportJobData,
  CreateDesignExportJobError,
  CreateDesignExportJobResponse2,
  GetDesignExportJobData,
  GetDesignExportJobError,
  GetDesignExportJobResponse2,
  DeleteFolderData,
  DeleteFolderError,
  DeleteFolderResponse,
  GetFolderData,
  GetFolderError,
  GetFolderResponse2,
  UpdateFolderData,
  UpdateFolderError,
  UpdateFolderResponse2,
  ListFolderItemsData,
  ListFolderItemsError,
  ListFolderItemsResponse2,
  MoveFolderItemData,
  MoveFolderItemError,
  MoveFolderItemResponse,
  CreateFolderData,
  CreateFolderError,
  CreateFolderResponse2,
  ExchangeAccessTokenData,
  ExchangeAccessTokenError,
  ExchangeAccessTokenResponse2,
  IntrospectTokenData,
  IntrospectTokenError,
  IntrospectTokenResponse2,
  RevokeTokensData,
  RevokeTokensError,
  RevokeTokensResponse2,
  CreateDesignResizeJobData,
  CreateDesignResizeJobError,
  CreateDesignResizeJobResponse2,
  GetDesignResizeJobData,
  GetDesignResizeJobError,
  GetDesignResizeJobResponse2,
  UsersMeData,
  UsersMeError,
  UsersMeResponse2,
  GetUserCapabilitiesData,
  GetUserCapabilitiesError,
  GetUserCapabilitiesResponse2,
  GetUserProfileData,
  GetUserProfileError,
  GetUserProfileResponse,
} from "./types.gen";

export const client = createClient(createConfig());

export class AppService {
  /**
   * Returns the Json Web Key Set (public keys) of an app. These keys are used to
   * verify JWTs sent to app backends.
   */
  public static getAppJwks<ThrowOnError extends boolean = false>(
    options: Options<GetAppJwksData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetAppJwksResponse2,
      GetAppJwksError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/apps/{appId}/jwks",
    });
  }
}

export class AssetService {
  /**
   * You can delete an asset by specifying its `assetId`. This operation mirrors the behavior
   * in the Canva UI. Deleting an item moves it to the trash.
   * Deleting an asset doesn't remove it from designs that already use it.
   */
  public static deleteAsset<ThrowOnError extends boolean = false>(
    options: Options<DeleteAssetData, ThrowOnError>,
  ) {
    return (options?.client ?? client).delete<
      DeleteAssetResponse,
      DeleteAssetError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/assets/{assetId}",
    });
  }

  /**
   * You can retrieve the metadata of an asset by specifying its `assetId`.
   */
  public static getAsset<ThrowOnError extends boolean = false>(
    options: Options<GetAssetData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetAssetResponse2,
      GetAssetError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/assets/{assetId}",
    });
  }

  /**
   * You can update the name and tags of an asset by specifying its `assetId`. Updating the tags
   * replaces all existing tags of the asset.
   */
  public static updateAsset<ThrowOnError extends boolean = false>(
    options: Options<UpdateAssetData, ThrowOnError>,
  ) {
    return (options?.client ?? client).patch<
      UpdateAssetResponse2,
      UpdateAssetError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/assets/{assetId}",
    });
  }

  /**
   * Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to upload an asset to the user's content library. Supported file types for assets are listed in the [Assets API overview](https://www.canva.dev/docs/connect/api-reference/assets/).
   *
   * The request format for this endpoint is an `application/octet-stream` body of bytes. Attach
   * information about the upload using an `Asset-Upload-Metadata` header.
   *
   *
   * <Note>
   *
   * For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of asset upload jobs created with this API using the [Get asset upload job API](https://www.canva.dev/docs/connect/api-reference/assets/get-asset-upload-job/).
   *
   * </Note>
   */
  public static createAssetUploadJob<ThrowOnError extends boolean = false>(
    options: Options<CreateAssetUploadJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateAssetUploadJobResponse2,
      CreateAssetUploadJobError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/octet-stream",
        ...options?.headers,
      },
      url: "/v1/asset-uploads",
    });
  }

  /**
   * Get the result of an asset upload job that was created using the [Create asset upload job API](https://www.canva.dev/docs/connect/api-reference/assets/create-asset-upload-job/).
   *
   * You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   */
  public static getAssetUploadJob<ThrowOnError extends boolean = false>(
    options: Options<GetAssetUploadJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetAssetUploadJobResponse2,
      GetAssetUploadJobError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/asset-uploads/{jobId}",
    });
  }
}

export class AutofillService {
  /**
   * <Warning>
   *
   * Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.
   *
   * </Warning>
   *
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.
   *
   * </Note>
   *
   * Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to autofill a Canva design using a brand template and input data.
   *
   * To get a list of input data fields, use the [Get brand template dataset
   * API](https://www.canva.dev/docs/connect/api-reference/brand-templates/get-brand-template-dataset/).
   *
   * Available data field types to autofill include:
   *
   * - Images
   * - Text
   * - Charts
   *
   * WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
   *
   * <Note>
   *
   * For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of autofill jobs created with this API using the [Get design autofill job API](https://www.canva.dev/docs/connect/api-reference/autofills/get-design-autofill-job/).
   *
   * </Note>
   */
  public static createDesignAutofillJob<ThrowOnError extends boolean = false>(
    options?: Options<CreateDesignAutofillJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateDesignAutofillJobResponse2,
      CreateDesignAutofillJobError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/autofills",
    });
  }

  /**
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.
   *
   * </Note>
   *
   * Get the result of a design autofill job that was created using the [Create design autofill job
   * API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).
   *
   * You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   */
  public static getDesignAutofillJob<ThrowOnError extends boolean = false>(
    options: Options<GetDesignAutofillJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignAutofillJobResponse2,
      GetDesignAutofillJobError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/autofills/{jobId}",
    });
  }
}

export class BrandTemplateService {
  /**
   * <Warning>
   *
   * Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.
   *
   * </Warning>
   *
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.
   *
   * </Note>
   *
   * Get a list of the [brand templates](https://www.canva.com/help/publish-team-template/) the user has access to.
   */
  public static listBrandTemplates<ThrowOnError extends boolean = false>(
    options?: Options<ListBrandTemplatesData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      ListBrandTemplatesResponse2,
      ListBrandTemplatesError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/brand-templates",
    });
  }

  /**
   * <Warning>
   *
   * Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.
   *
   * </Warning>
   *
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.
   *
   * </Note>
   *
   * Retrieves the metadata for a brand template.
   */
  public static getBrandTemplate<ThrowOnError extends boolean = false>(
    options: Options<GetBrandTemplateData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetBrandTemplateResponse2,
      GetBrandTemplateError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/brand-templates/{brandTemplateId}",
    });
  }

  /**
   * <Warning>
   *
   * Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.
   *
   * </Warning>
   *
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.
   *
   * </Note>
   *
   * Gets the dataset definition of a brand template. If the brand
   * template contains autofill data fields, this API returns an object with the data field
   * names and the type of data they accept.
   *
   * Available data field types include:
   *
   * - Images
   * - Text
   * - Charts
   *
   * You can autofill a brand template using the [Create a design autofill job
   * API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).
   *
   * WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
   */
  public static getBrandTemplateDataset<ThrowOnError extends boolean = false>(
    options: Options<GetBrandTemplateDatasetData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetBrandTemplateDatasetResponse2,
      GetBrandTemplateDatasetError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/brand-templates/{brandTemplateId}/dataset",
    });
  }
}

export class CommentService {
  /**
   * @deprecated
   * <Warning>
   *
   * This API is deprecated, so you should use the [Create thread](https://www.canva.dev/docs/connect/api-reference/comments/create-thread/) API instead.
   *
   * </Warning>
   *
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Create a new top-level comment on a design.
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/). A design can have a maximum
   * of 1000 comments.
   */
  public static createComment<ThrowOnError extends boolean = false>(
    options: Options<CreateCommentData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateCommentResponse2,
      CreateCommentError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/comments",
    });
  }

  /**
   * @deprecated
   * <Warning>
   *
   * This API is deprecated, so you should use the [Create reply](https://www.canva.dev/docs/connect/api-reference/comments/create-reply/) API instead.
   *
   * </Warning>
   *
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Creates a reply to a comment in a design.
   * To reply to an existing thread of comments, you can use either the `id` of the parent
   * (original) comment, or the `thread_id` of a comment in the thread. Each comment can
   * have a maximum of 100 replies created for it.
   *
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/).
   */
  public static createReplyDeprecated<ThrowOnError extends boolean = false>(
    options: Options<CreateReplyDeprecatedData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateReplyDeprecatedResponse,
      CreateReplyDeprecatedError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/comments/{commentId}/replies",
    });
  }

  /**
   * <Warning>
   * This API is currently provided as a preview. Be aware of the following:
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   * </Warning>
   *
   * Retrieves a list of replies for a comment or suggestion thread on a design.
   *
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/).
   */
  public static listReplies<ThrowOnError extends boolean = false>(
    options: Options<ListRepliesData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      ListRepliesResponse2,
      ListRepliesError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs/{designId}/comments/{threadId}/replies",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Creates a reply to a comment or suggestion thread on a design.
   * To reply to an existing thread, you must provide the ID of the thread
   * which is returned when a thread is created, or from the `thread_id` value
   * of an existing reply in the thread. Each thread can
   * have a maximum of 100 replies created for it.
   *
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/).
   */
  public static createReply<ThrowOnError extends boolean = false>(
    options: Options<CreateReplyData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateReplyResponse2,
      CreateReplyError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/designs/{designId}/comments/{threadId}/replies",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Gets a comment or suggestion thread on a design.
   * To retrieve a reply to a comment thread, use the [Get reply](https://www.canva.dev/docs/connect/api-reference/comments/get-reply/) API.
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/).
   */
  public static getThread<ThrowOnError extends boolean = false>(
    options: Options<GetThreadData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetThreadResponse2,
      GetThreadError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs/{designId}/comments/{threadId}",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Gets a reply to a comment or suggestion thread on a design.
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/).
   */
  public static getReply<ThrowOnError extends boolean = false>(
    options: Options<GetReplyData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetReplyResponse2,
      GetReplyError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs/{designId}/comments/{threadId}/replies/{replyId}",
    });
  }

  /**
   * <Warning>
   * This API is currently provided as a preview. Be aware of the following:
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   * </Warning>
   *
   * Creates a new comment thread on a design.
   * For information on comments and how they're used in the Canva UI, see the
   * [Canva Help Center](https://www.canva.com/help/comments/).
   */
  public static createThread<ThrowOnError extends boolean = false>(
    options: Options<CreateThreadData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateThreadResponse2,
      CreateThreadError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/designs/{designId}/comments",
    });
  }
}

export class ConnectService {
  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * The Keys API (`connect/keys`) is a security measure you can use to verify the authenticity
   * of webhooks you receive from Canva Connect. The Keys API returns a
   * [JSON Web Key (JWK)](https://www.rfc-editor.org/rfc/rfc7517#section-2), which you can use to
   * decrypt the webhook signature and verify it came from Canva and not a potentially malicious
   * actor. This helps to protect your systems from
   * [Replay attacks](https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures/).
   *
   * The keys returned by the Keys API can rotate. We recommend you cache the keys you receive
   * from this API where possible, and only access this API when you receive a webhook signed
   * with an unrecognized key. This allows you to verify webhooks quicker than accessing this API
   * every time you receive a webhook.
   */
  public static getSigningPublicKeys<ThrowOnError extends boolean = false>(
    options?: Options<GetSigningPublicKeysData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetSigningPublicKeysResponse2,
      GetSigningPublicKeysError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/connect/keys",
    });
  }
}

export class DesignService {
  /**
   * Lists metadata for all the designs in a Canva user's
   * [projects](https://www.canva.com/help/find-designs-and-folders/). You can also:
   *
   * - Use search terms to filter the listed designs.
   * - Show designs either created by, or shared with the user.
   * - Sort the results.
   */
  public static listDesigns<ThrowOnError extends boolean = false>(
    options?: Options<ListDesignsData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      ListDesignsResponse,
      ListDesignsError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs",
    });
  }

  /**
   * Creates a new Canva design. To create a new design, you can either:
   *
   * - Use a preset design type.
   * - Set height and width dimensions for a custom design.
   *
   * Additionally, you can also provide the `asset_id` of an asset in the user's [projects](https://www.canva.com/help/find-designs-and-folders/) to add to the new design. Currently, this only supports image assets. To list the assets in a folder in the user's projects, use the [List folder items API](https://www.canva.dev/docs/connect/api-reference/folders/list-folder-items/).
   */
  public static createDesign<ThrowOnError extends boolean = false>(
    options?: Options<CreateDesignData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateDesignResponse2,
      CreateDesignError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/designs",
    });
  }

  /**
   * Gets the metadata for a design. This includes owner information, URLs for editing and viewing, and thumbnail information.
   */
  public static getDesign<ThrowOnError extends boolean = false>(
    options: Options<GetDesignData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignResponse2,
      GetDesignError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs/{designId}",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Lists metadata for pages in a design, such as page-specific thumbnails.
   *
   * For the specified design, you can provide `offset` and `limit` values to specify the range of pages to return.
   *
   * NOTE: Some design types don't have pages (for example, Canva docs).
   */
  public static getDesignPages<ThrowOnError extends boolean = false>(
    options: Options<GetDesignPagesData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignPagesResponse2,
      GetDesignPagesError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs/{designId}/pages",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Lists the available file formats for [exporting a design](https://www.canva.dev/docs/connect/api-reference/exports/create-design-export-job/).
   */
  public static getDesignExportFormats<ThrowOnError extends boolean = false>(
    options: Options<GetDesignExportFormatsData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignExportFormatsResponse2,
      GetDesignExportFormatsError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/designs/{designId}/export-formats",
    });
  }
}

export class DesignImportService {
  /**
   * Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to import an external file as a new design in Canva.
   *
   * The request format for this endpoint has an `application/octet-stream` body of bytes,
   * and the information about the import is provided using an `Import-Metadata` header.
   *
   * Supported file types for imports are listed in [Design imports overview](https://www.canva.dev/docs/connect/api-reference/design-imports/#supported-file-types).
   *
   * <Note>
   *
   * For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of design import jobs created with this API using the [Get design import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/get-design-import-job/).
   *
   * </Note>
   */
  public static createDesignImportJob<ThrowOnError extends boolean = false>(
    options: Options<CreateDesignImportJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateDesignImportJobResponse2,
      CreateDesignImportJobError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/octet-stream",
        ...options?.headers,
      },
      url: "/v1/imports",
    });
  }

  /**
   * Gets the result of a design import job created using the [Create design import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/create-design-import-job/).
   *
   * You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   */
  public static getDesignImportJob<ThrowOnError extends boolean = false>(
    options: Options<GetDesignImportJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignImportJobResponse2,
      GetDesignImportJobError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/imports/{jobId}",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to import an external file from a URL as a new design in Canva.
   *
   * Supported file types for imports are listed in [Design imports overview](https://www.canva.dev/docs/connect/api-reference/design-imports/#supported-file-types).
   *
   * <Note>
   *
   * For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of design import jobs created with this API using the [Get URL import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/get-url-import-job/).
   *
   * </Note>
   */
  public static createUrlImportJob<ThrowOnError extends boolean = false>(
    options: Options<CreateUrlImportJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateUrlImportJobResponse2,
      CreateUrlImportJobError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/url-imports",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Gets the result of a URL import job created using the [Create URL import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/create-url-import-job/).
   *
   * You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   */
  public static getUrlImportJob<ThrowOnError extends boolean = false>(
    options: Options<GetUrlImportJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetUrlImportJobResponse2,
      GetUrlImportJobError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/url-imports/{jobId}",
    });
  }
}

export class ExportService {
  /**
   * Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to export a file from Canva. Once the exported file is generated, you can download
   * it using the URL(s) provided. The download URLs are only valid for 24 hours.
   *
   * The request requires the design ID and the exported file format type.
   *
   * Supported file formats (and export file type values): PDF (`pdf`), JPG (`jpg`), PNG (`png`), GIF (`gif`), Microsoft PowerPoint (`pptx`), and MP4 (`mp4`).
   *
   * <Note>
   *
   * For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of export jobs created with this API using the [Get design export job API](https://www.canva.dev/docs/connect/api-reference/exports/get-design-export-job/).
   *
   * </Note>
   */
  public static createDesignExportJob<ThrowOnError extends boolean = false>(
    options?: Options<CreateDesignExportJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateDesignExportJobResponse2,
      CreateDesignExportJobError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/exports",
    });
  }

  /**
   * Gets the result of a design export job that was created using the [Create design export job API](https://www.canva.dev/docs/connect/api-reference/exports/create-design-export-job/).
   *
   * If the job is successful, the response includes an array
   * of download URLs. Depending on the design type and export format, there is a download URL for each page in the design. The download URLs are only valid for 24 hours.
   *
   * You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   */
  public static getDesignExportJob<ThrowOnError extends boolean = false>(
    options: Options<GetDesignExportJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignExportJobResponse2,
      GetDesignExportJobError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/exports/{exportId}",
    });
  }
}

export class FolderService {
  /**
   * Deletes a folder with the specified `folderID`.
   * Deleting a folder moves the user's content in the folder to the
   * [Trash](https://www.canva.com/help/deleted-designs/) and content owned by
   * other users is moved to the top level of the owner's
   * [projects](https://www.canva.com/help/find-designs-and-folders/).
   */
  public static deleteFolder<ThrowOnError extends boolean = false>(
    options: Options<DeleteFolderData, ThrowOnError>,
  ) {
    return (options?.client ?? client).delete<
      DeleteFolderResponse,
      DeleteFolderError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/folders/{folderId}",
    });
  }

  /**
   * Gets the name and other details of a folder using a folder's `folderID`.
   */
  public static getFolder<ThrowOnError extends boolean = false>(
    options: Options<GetFolderData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetFolderResponse2,
      GetFolderError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/folders/{folderId}",
    });
  }

  /**
   * Updates a folder's details using its `folderID`.
   * Currently, you can only update a folder's name.
   */
  public static updateFolder<ThrowOnError extends boolean = false>(
    options: Options<UpdateFolderData, ThrowOnError>,
  ) {
    return (options?.client ?? client).patch<
      UpdateFolderResponse2,
      UpdateFolderError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/folders/{folderId}",
    });
  }

  /**
   * Lists the items in a folder, including each item's `type`.
   *
   * Folders can contain:
   *
   * - Other folders.
   * - Designs, such as Instagram posts, Presentations, and Documents ([Canva Docs](https://www.canva.com/create/documents/)).
   * - Image assets.
   *
   * Currently, video assets are not returned in the response.
   */
  public static listFolderItems<ThrowOnError extends boolean = false>(
    options: Options<ListFolderItemsData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      ListFolderItemsResponse2,
      ListFolderItemsError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/folders/{folderId}/items",
      querySerializer: {
        array: {
          explode: false,
          style: "form",
        },
      },
    });
  }

  /**
   * Moves an item to another folder. You must specify the folder ID of the destination folder, as well as the ID of the item you want to move.
   *
   * NOTE: In some situations, a single item can exist in multiple folders. If you attempt to move an item that exists in multiple folders, the API returns an `item_in_multiple_folders` error. In this case, you must use the Canva UI to move the item to another folder.
   */
  public static moveFolderItem<ThrowOnError extends boolean = false>(
    options?: Options<MoveFolderItemData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      MoveFolderItemResponse,
      MoveFolderItemError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/folders/move",
    });
  }

  /**
   * Creates a folder in either the top level of a Canva user's
   * [projects](https://www.canva.com/help/find-designs-and-folders/) (using the ID `root`), or
   * another folder (using the parent folder's ID). When a folder is successfully created, the
   * endpoint returns its folder ID, along with other information.
   */
  public static createFolder<ThrowOnError extends boolean = false>(
    options: Options<CreateFolderData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateFolderResponse2,
      CreateFolderError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/folders",
    });
  }
}

export class OauthService {
  /**
   * This endpoint implements the OAuth 2.0 `token` endpoint, as part of the Authorization Code flow with Proof Key for Code Exchange (PKCE). For more information, see [Authentication](https://www.canva.dev/docs/connect/authentication/).
   *
   * To generate an access token, you must provide one of the following:
   *
   * - An authorization code
   * - A refresh token
   *
   * Generating a token using either an authorization code or a refresh token allows your integration to act on behalf of a user. You must first [obtain user authorization and get an authorization code](https://www.canva.dev/docs/connect/authentication/#obtain-user-authorization).
   *
   * Access tokens may be up to 4 KB in size, and are only valid for a specified period of time. The expiry time (currently 4 hours) is shown in the endpoint response and is subject to change.
   *
   * **Endpoint authentication**
   *
   * Requests to this endpoint require authentication with your client ID and client secret, using _one_ of the following methods:
   *
   * - **Basic access authentication** (Recommended): For [basic access authentication](https://en.wikipedia.org/wiki/Basic_access_authentication), the `{credentials}` string must be a Base64 encoded value of `{client id}:{client secret}`.
   * - **Body parameters**: Provide your integration's credentials using the `client_id` and `client_secret` body parameters.
   *
   * This endpoint can't be called from a user's web-browser client because it uses client authentication with client secrets. Requests must come from your integration's backend, otherwise they'll be blocked by Canva's [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) policy.
   *
   * **Generate an access token using an authorization code**
   *
   * To generate an access token with an authorization code, you must:
   *
   * - Set `grant_type` to `authorization_code`.
   * - Provide the `code_verifier` value that you generated when creating the user authorization URL.
   * - Provide the authorization code you received after the user authorized the integration.
   *
   * **Generate an access token using a refresh token**
   *
   * Using the `refresh_token` value from a previous user token request, you can get a new access token with the same or smaller scope as the previous one, but with a refreshed expiry time. You will also receive a new refresh token that you can use to refresh the access token again.
   *
   * To refresh an existing access token, you must:
   *
   * - Set `grant_type` to `refresh_token`.
   * - Provide the `refresh_token` from a previous token request.
   */
  public static exchangeAccessToken<ThrowOnError extends boolean = false>(
    options: Options<ExchangeAccessTokenData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      ExchangeAccessTokenResponse2,
      ExchangeAccessTokenError,
      ThrowOnError
    >({
      ...options,
      ...urlSearchParamsBodySerializer,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        ...options?.headers,
      },
      url: "/v1/oauth/token",
    });
  }

  /**
   * Introspect an access token to see whether it is valid and active. You can also verify some token properties, such as its claims, scopes, and validity times.
   *
   * Requests to this endpoint require authentication with your client ID and client secret, using _one_ of the following methods:
   *
   * - **Basic access authentication** (Recommended): For [basic access authentication](https://en.wikipedia.org/wiki/Basic_access_authentication), the `{credentials}` string must be a Base64 encoded value of `{client id}:{client secret}`.
   * - **Body parameters**: Provide your integration's credentials using the `client_id` and `client_secret` body parameters.
   *
   * This endpoint can't be called from a user's web-browser client because it uses client authentication with client secrets. Requests must come from your integration's backend, otherwise they'll be blocked by Canva's [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) policy.
   */
  public static introspectToken<ThrowOnError extends boolean = false>(
    options: Options<IntrospectTokenData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      IntrospectTokenResponse2,
      IntrospectTokenError,
      ThrowOnError
    >({
      ...options,
      ...urlSearchParamsBodySerializer,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        ...options?.headers,
      },
      url: "/v1/oauth/introspect",
    });
  }

  /**
   * Revoke an access token or a refresh token.
   *
   * If you revoke a _refresh token_, be aware that:
   *
   * - The refresh token's lineage is also revoked. This means that access tokens created from that refresh token are also revoked.
   * - The user's consent for your integration is also revoked. This means that the user must go through the OAuth process again to use your integration.
   *
   * Requests to this endpoint require authentication with your client ID and client secret, using _one_ of the following methods:
   *
   * - **Basic access authentication** (Recommended): For [basic access authentication](https://en.wikipedia.org/wiki/Basic_access_authentication), the `{credentials}` string must be a Base64 encoded value of `{client id}:{client secret}`.
   * - **Body parameters**: Provide your integration's credentials using the `client_id` and `client_secret` body parameters.
   *
   * This endpoint can't be called from a user's web-browser client because it uses client authentication with client secrets. Requests must come from your integration's backend, otherwise they'll be blocked by Canva's [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) policy.
   */
  public static revokeTokens<ThrowOnError extends boolean = false>(
    options: Options<RevokeTokensData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      RevokeTokensResponse2,
      RevokeTokensError,
      ThrowOnError
    >({
      ...options,
      ...urlSearchParamsBodySerializer,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        ...options?.headers,
      },
      url: "/v1/oauth/revoke",
    });
  }
}

export class ResizeService {
  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's on a Canva plan with premium features (such as Canva Pro).
   *
   * </Note>
   *
   * Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints)
   * to create a resized copy of a design. The new resized design is
   * added to the top level of the user's
   * [projects](https://www.canva.com/help/find-designs-and-folders/) (`root` folder).
   *
   * To resize a design into a new design, you can either:
   *
   * - Use a preset design type.
   * - Set height and width dimensions for a custom design.
   *
   * NOTE: [Canva docs](https://www.canva.com/create/documents/) can't be resized, and other design types can't be resized to a Canva doc.
   *
   * <Note>
   * For more information on the workflow for using asynchronous jobs,
   * see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   * You can check the status and get the results of resize jobs created with this API using the
   * [Get design resize job API](https://www.canva.dev/docs/connect/api-reference/resizes/get-design-resize-job/).
   * </Note>
   */
  public static createDesignResizeJob<ThrowOnError extends boolean = false>(
    options?: Options<CreateDesignResizeJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).post<
      CreateDesignResizeJobResponse2,
      CreateDesignResizeJobError,
      ThrowOnError
    >({
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      url: "/v1/resizes",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * <Note>
   *
   * To use this API, your integration must act on behalf of a user that's on a Canva plan with premium features (such as Canva Pro).
   *
   * </Note>
   *
   * Gets the result of a design resize job that was created using the [Create design resize
   * job API](https://www.canva.dev/docs/connect/api-reference/resizes/create-design-resize-job/).
   *
   * If the job is successful, the response includes a summary of the new resized design, including its metadata.
   *
   * You might need to make multiple requests to this endpoint until you get a `success` or `failed` status.
   * For more information on the workflow for using asynchronous jobs,
   * see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
   */
  public static getDesignResizeJob<ThrowOnError extends boolean = false>(
    options: Options<GetDesignResizeJobData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetDesignResizeJobResponse2,
      GetDesignResizeJobError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/resizes/{jobId}",
    });
  }
}

export class UserService {
  /**
   * Returns the User ID and Team ID of the user
   * account associated with the provided access token.
   */
  public static usersMe<ThrowOnError extends boolean = false>(
    options?: Options<UsersMeData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      UsersMeResponse2,
      UsersMeError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/users/me",
    });
  }

  /**
   * <Warning>
   *
   * This API is currently provided as a preview. Be aware of the following:
   *
   * - There might be unannounced breaking changes.
   * - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
   * - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
   *
   * </Warning>
   *
   * Lists the API capabilities for the user account associated with the provided access token. For more information, see [Capabilities](https://www.canva.dev/docs/connect/capabilities/).
   */
  public static getUserCapabilities<ThrowOnError extends boolean = false>(
    options?: Options<GetUserCapabilitiesData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetUserCapabilitiesResponse2,
      GetUserCapabilitiesError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/users/me/capabilities",
    });
  }

  /**
   * Currently, this returns the display name of the user account associated with the provided access token. More user information is expected to be included in the future.
   */
  public static getUserProfile<ThrowOnError extends boolean = false>(
    options?: Options<GetUserProfileData, ThrowOnError>,
  ) {
    return (options?.client ?? client).get<
      GetUserProfileResponse,
      GetUserProfileError,
      ThrowOnError
    >({
      ...options,
      url: "/v1/users/me/profile",
    });
  }
}
