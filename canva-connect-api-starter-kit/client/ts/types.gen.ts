// This file is auto-generated by @hey-api/openapi-ts

export type GetAppJwksResponse = {
  /**
   * The value of the "keys" parameter is an array of JWK values. The order of keys has no
   * meaning.
   */
  keys: Array<JsonWebKey>;
};

/**
 * Standard Json Web Key specification following https://www.rfc-editor.org/rfc/rfc7517 and
 * https://www.rfc-editor.org/rfc/rfc7518.html.
 */
export type JsonWebKey = {
  /**
   * The "alg" (algorithm) parameter identifies the algorithm intended for
   * use with the key.
   * See https://www.rfc-editor.org/rfc/rfc7517#section-4
   */
  alg?: string;
  /**
   * The "kid" (key ID) parameter is used to match a specific key.  This
   * is used, for instance, to choose among a set of keys within a JWK Set
   * during key rollover. When "kid" values are used within a JWK Set,
   * different keys within the JWK Set SHOULD use distinct "kid" values.
   * The "kid" value is a case-sensitive string.
   * See https://www.rfc-editor.org/rfc/rfc7517#section-4
   */
  kid: string;
  /**
   * The "kty" (key type) parameter identifies the cryptographic algorithm
   * family used with the key, such as "RSA" or "EC". The "kty" value is a
   * case-sensitive string. At the moment, only "RSA" is supported.
   * See https://www.rfc-editor.org/rfc/rfc7517#section-4
   */
  kty: string;
  /**
   * The "n" (modulus) parameter contains the modulus value for the RSA
   * public key.  It is represented as a Base64urlUInt-encoded value.
   * See https://www.rfc-editor.org/rfc/rfc7518.html#section-6.3
   */
  n: string;
  /**
   * The "e" (exponent) parameter contains the exponent value for the RSA
   * public key.  It is represented as a Base64urlUInt-encoded value.
   * See https://www.rfc-editor.org/rfc/rfc7518.html#section-6.3
   */
  e: string;
  /**
   * The "use" (public key use) parameter identifies the intended use of
   * the public key. The "use" parameter is employed to indicate whether
   * a public key is used for encrypting data or verifying the signature
   * on data. Values are commonly "sig" (signature) or "enc" (encryption).
   * See https://www.rfc-editor.org/rfc/rfc7517#section-4
   */
  use?: string;
};

export type GetAssetResponse = {
  asset: Asset;
};

export type UpdateAssetRequest = {
  /**
   * The name of the asset. This is shown in the Canva UI.
   * When this field is undefined or empty, nothing is updated.
   */
  name?: string;
  /**
   * The replacement tags for the asset.
   * When this field is undefined, nothing is updated.
   */
  tags?: Array<string>;
};

export type UpdateAssetResponse = {
  asset: Asset;
};

/**
 * The asset object, which contains metadata about the asset.
 */
export type Asset = {
  type: AssetType;
  /**
   * The ID of the asset.
   */
  id: string;
  /**
   * The name of the asset.
   */
  name: string;
  /**
   * The user-facing tags attached to the asset.
   * Users can add these tags to their uploaded assets, and they can search their uploaded
   * assets in the Canva UI by searching for these tags. For information on how users use
   * tags, see the
   * [Canva Help Center page on asset tags](https://www.canva.com/help/add-edit-tags/).
   */
  tags: Array<string>;
  import_status?: ImportStatus;
  /**
   * When the asset was added to Canva, as a Unix timestamp (in seconds since the Unix
   * Epoch).
   */
  created_at: number;
  /**
   * When the asset was last updated in Canva, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  updated_at: number;
  thumbnail?: Thumbnail;
};

/**
 * An object representing an asset with associated metadata.
 */
export type AssetSummary = {
  type: AssetType;
  /**
   * The ID of the asset.
   */
  id: string;
  /**
   * The name of the asset.
   */
  name: string;
  /**
   * The user-facing tags attached to the asset.
   * Users can add these tags to their uploaded assets, and they can search their uploaded
   * assets in the Canva UI by searching for these tags. For information on how users use
   * tags, see the
   * [Canva Help Center page on asset tags](https://www.canva.com/help/add-edit-tags/).
   */
  tags: Array<string>;
  /**
   * When the asset was added to Canva, as a Unix timestamp (in seconds since the Unix
   * Epoch).
   */
  created_at: number;
  /**
   * When the asset was last updated in Canva, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  updated_at: number;
  thumbnail?: Thumbnail;
};

/**
 * Type of an asset. Support for `video` assets is currently provided as a [preview](https://www.canva.dev/docs/connect/api-reference/assets/#videos).
 */
export type AssetType = "image" | "video";

/**
 * Type of an asset. Support for `video` assets is currently provided as a [preview](https://www.canva.dev/docs/connect/api-reference/assets/#videos).
 */
export const AssetType = {
  IMAGE: "image",
  VIDEO: "video",
} as const;

/**
 * The import status of the asset.
 * @deprecated
 */
export type ImportStatus = {
  state: ImportStatusState;
  error?: ImportError;
};

/**
 * State of the import job for an uploaded asset.
 * @deprecated
 */
export type ImportStatusState = "failed" | "in_progress" | "success";

/**
 * State of the import job for an uploaded asset.
 * @deprecated
 */
export const ImportStatusState = {
  FAILED: "failed",
  IN_PROGRESS: "in_progress",
  SUCCESS: "success",
} as const;

/**
 * If the import fails, this object provides details about the error.
 * @deprecated
 */
export type ImportError = {
  code: ImportErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

/**
 * A short string indicating why the upload failed. This field can be used to handle errors programmatically.
 *
 * @deprecated
 */
export type ImportErrorCode = "file_too_big" | "import_failed";

/**
 * A short string indicating why the upload failed. This field can be used to handle errors programmatically.
 *
 * @deprecated
 */
export const ImportErrorCode = {
  FILE_TOO_BIG: "file_too_big",
  IMPORT_FAILED: "import_failed",
} as const;

export type CreateAssetUploadJobResponse = {
  job: AssetUploadJob;
};

export type GetAssetUploadJobResponse = {
  job: AssetUploadJob;
};

/**
 * The status of the asset upload job.
 */
export type AssetUploadJob = {
  /**
   * The ID of the asset upload job.
   */
  id: string;
  status: AssetUploadStatus;
  error?: AssetUploadError;
  asset?: Asset;
};

/**
 * Status of the asset upload job.
 */
export type AssetUploadStatus = "failed" | "in_progress" | "success";

/**
 * Status of the asset upload job.
 */
export const AssetUploadStatus = {
  FAILED: "failed",
  IN_PROGRESS: "in_progress",
  SUCCESS: "success",
} as const;

/**
 * If the upload fails, this object provides details about the error.
 */
export type AssetUploadError = {
  code: AssetUploadErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

/**
 * A short string indicating why the upload failed. This field can be used to handle errors
 * programmatically.
 */
export type AssetUploadErrorCode =
  | "file_too_big"
  | "import_failed"
  | "fetch_failed";

/**
 * A short string indicating why the upload failed. This field can be used to handle errors
 * programmatically.
 */
export const AssetUploadErrorCode = {
  FILE_TOO_BIG: "file_too_big",
  IMPORT_FAILED: "import_failed",
  FETCH_FAILED: "fetch_failed",
} as const;

/**
 * Metadata for the asset being uploaded.
 */
export type AssetUploadMetadata = {
  /**
   * The asset's name, encoded in Base64.
   *
   * The maximum length of an asset name in Canva (unencoded) is 50 characters.
   *
   * Base64 encoding allows names containing emojis and other special
   * characters to be sent using HTTP headers.
   * For example, "My Awesome Upload 🚀" Base64 encoded
   * is `TXkgQXdlc29tZSBVcGxvYWQg8J+agA==`.
   */
  name_base64: string;
};

export type CreateDesignAutofillJobRequest = {
  /**
   * ID of the input brand template.
   */
  brand_template_id: string;
  /**
   * Title to use for the autofilled design.
   *
   * If no design title is provided, the autofilled design will have the same title as the brand template.
   */
  title?: string;
  /**
   * Data object containing the data fields and values to autofill.
   */
  data: {
    [key: string]: DatasetValue;
  };
};

export type CreateDesignAutofillJobResponse = {
  job: DesignAutofillJob;
};

/**
 * The data field to autofill.
 */
export type DatasetValue =
  | ({
      type?: "image";
    } & DatasetImageValue)
  | ({
      type?: "text";
    } & DatasetTextValue)
  | ({
      type?: "chart";
    } & DatasetChartValue);

/**
 * Data object containing the data fields and values to autofill.
 */
export type Dataset = {
  [key: string]: DatasetValue;
};

/**
 * If the data field is an image field.
 */
export type DatasetImageValue = {
  type: "image";
  /**
   * `asset_id` of the image to insert into the template element.
   */
  asset_id: string;
};

/**
 * If the data field is a text field.
 */
export type DatasetTextValue = {
  type: "text";
  /**
   * Text to insert into the template element.
   */
  text: string;
};

/**
 * If the data field is a chart.
 *
 * WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
 */
export type DatasetChartValue = {
  type: "chart";
  chart_data: DataTable;
};

export type GetDesignAutofillJobResponse = {
  job: DesignAutofillJob;
};

/**
 * Details about the autofill job.
 */
export type DesignAutofillJob = {
  /**
   * ID of the asynchronous job that is creating the design using the provided data.
   */
  id: string;
  status: DesignAutofillStatus;
  result?: DesignAutofillJobResult;
  error?: AutofillError;
};

/**
 * Result of the design autofill job. Only present if job status is `success`.
 */
export type DesignAutofillJobResult = {
  type?: "create_design";
} & CreateDesignAutofillJobResult;

/**
 * Design has been created and saved to user's root folder.
 */
export type CreateDesignAutofillJobResult = {
  type: "create_design";
  design: DesignSummary;
};

/**
 * Status of the design autofill job.
 */
export type DesignAutofillStatus = "in_progress" | "success" | "failed";

/**
 * Status of the design autofill job.
 */
export const DesignAutofillStatus = {
  IN_PROGRESS: "in_progress",
  SUCCESS: "success",
  FAILED: "failed",
} as const;

export type AutofillErrorCode =
  | "autofill_error"
  | "thumbnail_generation_error"
  | "create_design_error";

export const AutofillErrorCode = {
  AUTOFILL_ERROR: "autofill_error",
  THUMBNAIL_GENERATION_ERROR: "thumbnail_generation_error",
  CREATE_DESIGN_ERROR: "create_design_error",
} as const;

/**
 * If the autofill job fails, this object provides details about the error.
 */
export type AutofillError = {
  code: AutofillErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

export type DatasetFilter = "any" | "non_empty" | "empty";

export const DatasetFilter = {
  /**
   * Brand templates with and without dataset definitions.
   */
  ANY: "any",
  /**
   * Brand templates with one or more data fields defined.
   */
  NON_EMPTY: "non_empty",
  /**
   * Brand templates with no data fields defined.
   */
  EMPTY: "empty",
} as const;

export type ListBrandTemplatesResponse = {
  /**
   * If the success response contains a continuation token, the user has access to more
   * brand templates you can list. You can use this token as a query parameter and retrieve
   * more templates from the list, for example
   * `/v1/brand-templates?continuation={continuation}`.
   * To retrieve all the brand templates available to the user, you might need to make
   * multiple requests.
   */
  continuation?: string;
  /**
   * The list of brand templates.
   */
  items: Array<BrandTemplate>;
};

/**
 * Successful response from a `getBrandTemplate` request.
 */
export type GetBrandTemplateResponse = {
  brand_template: BrandTemplate;
};

/**
 * An object representing a brand template with associated metadata.
 */
export type BrandTemplate = {
  /**
   * The brand template ID.
   */
  id: string;
  /**
   * The brand template title, as shown in the Canva UI.
   */
  title: string;
  /**
   * A URL Canva users can visit to view the brand template.
   */
  view_url: string;
  /**
   * A URL Canva users can visit to create a new design from the template.
   */
  create_url: string;
  thumbnail?: Thumbnail;
  /**
   * When the brand template was created, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  created_at: number;
  /**
   * When the brand template was last updated, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  updated_at: number;
};

/**
 * Successful response from a `getBrandTemplateDataset` request.
 */
export type GetBrandTemplateDatasetResponse = {
  /**
   * The dataset definition for the brand template. The dataset definition contains the data inputs available for use with the
   * [Create design autofill job API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).
   */
  dataset?: {
    [key: string]: DataField;
  };
};

/**
 * The dataset definition for the brand template. The dataset definition contains the data inputs available for use with the
 * [Create design autofill job API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).
 */
export type DatasetDefinition = {
  [key: string]: DataField;
};

/**
 * A named data field that can be autofilled in the brand template.
 */
export type DataField =
  | ({
      type?: "image";
    } & ImageDataField)
  | ({
      type?: "text";
    } & TextDataField)
  | ({
      type?: "chart";
    } & ChartDataField);

/**
 * An image for a brand template. You can autofill the brand template with an image by providing its `asset_id`.
 */
export type ImageDataField = {
  type: "image";
};

/**
 * Some text for a brand template. You can autofill the brand template with this value.
 */
export type TextDataField = {
  type: "text";
};

/**
 * Chart data for a brand template. You can autofill the brand template with tabular data.
 *
 * WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
 */
export type ChartDataField = {
  type: "chart";
};

/**
 * Some APIs are annotated with required capabilities. These endpoints require the user to
 * possess the required capabilities in order to be called successfully.
 */
export type Capability = "autofill" | "brand_template" | "resize";

/**
 * Some APIs are annotated with required capabilities. These endpoints require the user to
 * possess the required capabilities in order to be called successfully.
 */
export const Capability = {
  /**
   * Capability required to call autofill APIs Users that are members of a [Canva Enterprise](https://www.canva.com/enterprise/) organization have this capability.
   */
  AUTOFILL: "autofill",
  /**
   * Capability required to use brand template APIs. Users that are members of a [Canva Enterprise](https://www.canva.com/enterprise/) organization have this capability.
   */
  BRAND_TEMPLATE: "brand_template",
  /**
   * Capability required to create design resize jobs. Users on a Canva plan with premium features (such as Canva Pro) have this capability.
   */
  RESIZE: "resize",
} as const;

export type CreateCommentRequest = {
  attached_to: CommentObjectInput;
  /**
   * The comment message. This is the comment body shown in the Canva UI.
   *
   * You can also mention users in your message by specifying their User ID and Team ID
   * using the format `[user_id:team_id]`. If the `assignee_id` parameter is specified, you
   * must mention the assignee in the message.
   */
  message: string;
  /**
   * Lets you assign the comment to a Canva user using their User ID. You _must_ mention the
   * assigned user in the `message`.
   */
  assignee_id?: string;
};

export type CreateReplyRequest = {
  attached_to: CommentObjectInput;
  /**
   * The reply comment message. This is the reply comment body shown in the Canva UI.
   *
   * You can also mention users in your message by specifying their User ID and Team ID
   * using the format `[user_id:team_id]`.
   */
  message: string;
};

export type CreateReplyV2Request = {
  /**
   * The comment message of the reply in plaintext. This is the reply comment shown in the Canva UI.
   *
   * You can also mention users in your message by specifying their User ID and Team ID
   * using the format `[user_id:team_id]`.
   */
  message_plaintext: string;
};

export type CreateCommentResponse = {
  comment: ParentComment;
};

export type CreateThreadResponse = {
  thread: Thread;
};

export type CreateReplyResponse = {
  comment: ReplyComment;
};

export type CreateThreadRequest = {
  /**
   * The comment message in plaintext. This is the comment body shown in the Canva UI.
   *
   * You can also mention users in your message by specifying their User ID and Team ID
   * using the format `[user_id:team_id]`. If the `assignee_id` parameter is specified, you
   * must mention the assignee in the message.
   */
  message_plaintext: string;
  /**
   * Lets you assign the comment to a Canva user using their User ID. You _must_ mention the
   * assigned user in the `message`.
   */
  assignee_id?: string;
};

export type CreateReplyV2Response = {
  reply: Reply;
};

/**
 * Successful response from a `getThread` request.
 *
 * The `comment` property is deprecated.
 * For details of a comment thread, please use the `thread` property.
 */
export type GetThreadResponse = {
  comment?: Comment;
  thread?: Thread;
};

/**
 * Successful response from a `getReply` request.
 */
export type GetReplyResponse = {
  reply: Reply;
};

/**
 * The comment object, which contains metadata about the comment.
 * Deprecated in favor of the new `thread` object.
 * @deprecated
 */
export type Comment =
  | ({
      type?: "parent";
    } & ParentComment)
  | ({
      type?: "reply";
    } & ReplyComment);

/**
 * Data about the comment, including the message, author, and
 * the object (such as a design) the comment is attached to.
 * @deprecated
 */
export type ParentComment = {
  type: "parent";
  /**
   * The ID of the comment.
   *
   * You can use this ID to create replies to the comment using the [Create reply API](https://www.canva.dev/docs/connect/api-reference/comments/create-reply/).
   */
  id: string;
  attached_to?: CommentObject;
  /**
   * The comment message. This is the comment body shown in the Canva UI.
   * User mentions are shown here in the format `[user_id:team_id]`.
   */
  message: string;
  author: User;
  /**
   * When the comment or reply was created, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  created_at?: number;
  /**
   * When the comment or reply was last updated, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  updated_at?: number;
  /**
   * The Canva users mentioned in the comment.
   * @deprecated
   */
  mentions: {
    [key: string]: TeamUser;
  };
  assignee?: User;
  resolver?: User;
};

/**
 * Data about the reply comment, including the message, author, and
 * the object (such as a design) the comment is attached to.
 * @deprecated
 */
export type ReplyComment = {
  type: "reply";
  /**
   * The ID of the comment.
   */
  id: string;
  attached_to?: CommentObject;
  /**
   * The comment message. This is the comment body shown in the Canva UI.
   * User mentions are shown here in the format `[user_id:team_id]`.
   */
  message: string;
  author: User;
  /**
   * When the comment or reply was created, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  created_at?: number;
  /**
   * When the comment or reply was last updated, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  updated_at?: number;
  /**
   * The Canva users mentioned in the comment.
   * @deprecated
   */
  mentions: {
    [key: string]: TeamUser;
  };
  /**
   * The ID of the comment thread this reply is in. This ID is the same as the `id` of the
   * parent comment.
   */
  thread_id: string;
};

/**
 * Identifying information about the object (such as a design) that the comment is attached to.
 * @deprecated
 */
export type CommentObject = {
  type?: "design";
} & DesignCommentObject;

/**
 * If the comment is attached to a Canva Design.
 * @deprecated
 */
export type DesignCommentObject = {
  type: "design";
  /**
   * The ID of the design this comment is attached to.
   */
  design_id: string;
};

/**
 * An object containing identifying information for the design or other object you want to
 * attach the comment to.
 */
export type CommentObjectInput = {
  type?: "design";
} & DesignCommentObjectInput;

/**
 * If the comment is attached to a Canva Design.
 */
export type DesignCommentObjectInput = {
  type: "design";
  /**
   * The ID of the design you want to attach this comment to.
   */
  design_id: string;
};

/**
 * Basic details about the comment.
 *
 * The `comment` property is deprecated.
 * For details of the comment event, use the `comment_event` property instead.
 * @deprecated
 */
export type CommentEventDeprecated = {
  type: CommentEventTypeEnum;
  data: Comment;
};

/**
 * The Canva users mentioned in the comment.
 * @deprecated
 */
export type Mentions = {
  [key: string]: TeamUser;
};

/**
 * The type of comment event.
 * @deprecated
 */
export type CommentEventTypeEnum =
  | "comment"
  | "reply"
  | "mention"
  | "assign"
  | "resolve";

/**
 * The type of comment event.
 * @deprecated
 */
export const CommentEventTypeEnum = {
  COMMENT: "comment",
  REPLY: "reply",
  MENTION: "mention",
  ASSIGN: "assign",
  RESOLVE: "resolve",
} as const;

/**
 * The type of comment event, including additional type-specific properties.
 */
export type CommentEvent =
  | ({
      type?: "new";
    } & NewCommentEvent)
  | ({
      type?: "assigned";
    } & AssignedCommentEvent)
  | ({
      type?: "resolved";
    } & ResolvedCommentEvent)
  | ({
      type?: "reply";
    } & ReplyCommentEvent)
  | ({
      type?: "mention";
    } & MentionCommentEvent);

/**
 * Event type for a new comment thread.
 */
export type NewCommentEvent = {
  type: "new";
  /**
   * A URL to the design, focused on the comment thread.
   */
  comment_url: string;
  comment: Thread;
};

/**
 * Event type for a comment thread that has been assigned.
 */
export type AssignedCommentEvent = {
  type: "assigned";
  /**
   * A URL to the design, focused on the comment thread.
   */
  comment_url: string;
  comment: Thread;
};

/**
 * Event type for a comment thread that has been resolved.
 */
export type ResolvedCommentEvent = {
  type: "resolved";
  /**
   * A URL to the design, focused on the comment thread.
   */
  comment_url: string;
  comment: Thread;
};

/**
 * Event type for a reply to a comment thread.
 */
export type ReplyCommentEvent = {
  type: "reply";
  /**
   * A URL to the design, focused on the comment reply.
   */
  reply_url: string;
  reply: Reply;
};

/**
 * Event type for a mention in a comment thread or reply.
 */
export type MentionCommentEvent = {
  type: "mention";
  content: MentionEventContent;
};

/**
 * The type of mention event content, along with additional type-specific properties.
 */
export type MentionEventContent =
  | ({
      type?: "thread";
    } & ThreadMentionEventContent)
  | ({
      type?: "reply";
    } & ReplyMentionEventContent);

/**
 * Content for a mention in a comment thread.
 */
export type ThreadMentionEventContent = {
  type: "thread";
  /**
   * A URL to the design, focused on the comment thread.
   */
  comment_url: string;
  comment: Thread;
};

/**
 * Content for a mention in a comment reply.
 */
export type ReplyMentionEventContent = {
  type: "reply";
  /**
   * A URL to the design, focused on the comment reply.
   */
  reply_url: string;
  reply: Reply;
};

/**
 * Successful response from a `listReplies` request.
 */
export type ListRepliesResponse = {
  /**
   * If the success response contains a continuation token, the list contains more items
   * you can list. You can use this token as a query parameter and retrieve more items
   * from the list, for example `?continuation={continuation}`.
   *
   * To retrieve all items, you might need to make multiple requests.
   */
  continuation?: string;
  items: Array<Reply>;
};

/**
 * A discussion thread on a design.
 *
 * The `type` of the thread can be found in the `thread_type` object, along with additional type-specific properties.
 * The `author` of the thread might be missing if that user account no longer exists.
 */
export type Thread = {
  /**
   * The ID of the thread.
   *
   * You can use this ID to create replies to the thread using the [Create reply API](https://www.canva.dev/docs/connect/api-reference/comments/create-reply/).
   */
  id: string;
  /**
   * The ID of the design that the discussion thread is on.
   */
  design_id: string;
  thread_type: ThreadType;
  author?: User;
  /**
   * When the thread was created, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  created_at: number;
  /**
   * When the thread was last updated, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  updated_at: number;
};

/**
 * The type of the discussion thread, along with additional type-specific properties.
 */
export type ThreadType =
  | ({
      type?: "comment";
    } & CommentThreadType)
  | ({
      type?: "suggestion";
    } & SuggestionThreadType);

/**
 * A comment thread.
 */
export type CommentThreadType = {
  type: "comment";
  content: CommentContent;
  /**
   * The Canva users mentioned in the comment thread or reply.
   */
  mentions: {
    [key: string]: UserMention;
  };
  assignee?: User;
  resolver?: User;
};

/**
 * A suggestion thread.
 */
export type SuggestionThreadType = {
  type: "suggestion";
  suggested_edits: Array<SuggestedEdit>;
  status: SuggestionStatus;
};

/**
 * The type of the suggested edit, along with additional type-specific properties.
 */
export type SuggestedEdit =
  | ({
      type?: "add";
    } & AddSuggestedEdit)
  | ({
      type?: "delete";
    } & DeleteSuggestedEdit)
  | ({
      type?: "format";
    } & FormatSuggestedEdit);

/**
 * A suggestion to add some text.
 */
export type AddSuggestedEdit = {
  type: "add";
  text: string;
};

/**
 * A suggestion to delete some text.
 */
export type DeleteSuggestedEdit = {
  type: "delete";
  text: string;
};

/**
 * A suggestion to format some text.
 */
export type FormatSuggestedEdit = {
  type: "format";
  format: SuggestionFormat;
};

/**
 * The current status of the suggestion.
 */
export type SuggestionStatus = "open" | "accepted" | "rejected";

/**
 * The current status of the suggestion.
 */
export const SuggestionStatus = {
  /**
   * A suggestion was made, but it hasn't been accepted or rejected yet.
   */
  OPEN: "open",
  /**
   * A suggestion was accepted and applied to the design.
   */
  ACCEPTED: "accepted",
  /**
   * A suggestion was rejected and not applied to the design.
   */
  REJECTED: "rejected",
} as const;

/**
 * The suggested format change.
 */
export type SuggestionFormat =
  | "font_family"
  | "font_size"
  | "font_weight"
  | "font_style"
  | "color"
  | "background_color"
  | "decoration"
  | "strikethrough"
  | "link"
  | "letter_spacing"
  | "line_height"
  | "direction"
  | "text_align"
  | "list_marker"
  | "list_level"
  | "margin_inline_start"
  | "text_indent"
  | "font_size_modifier"
  | "vertical_align";

/**
 * The suggested format change.
 */
export const SuggestionFormat = {
  FONT_FAMILY: "font_family",
  FONT_SIZE: "font_size",
  FONT_WEIGHT: "font_weight",
  FONT_STYLE: "font_style",
  COLOR: "color",
  BACKGROUND_COLOR: "background_color",
  DECORATION: "decoration",
  STRIKETHROUGH: "strikethrough",
  LINK: "link",
  LETTER_SPACING: "letter_spacing",
  LINE_HEIGHT: "line_height",
  DIRECTION: "direction",
  TEXT_ALIGN: "text_align",
  LIST_MARKER: "list_marker",
  LIST_LEVEL: "list_level",
  MARGIN_INLINE_START: "margin_inline_start",
  TEXT_INDENT: "text_indent",
  FONT_SIZE_MODIFIER: "font_size_modifier",
  VERTICAL_ALIGN: "vertical_align",
} as const;

/**
 * A reply to a thread.
 *
 * The `author` of the reply might be missing if that user account no longer exists.
 */
export type Reply = {
  /**
   * The ID of the reply.
   */
  id: string;
  /**
   * The ID of the design that the thread for this reply is attached to.
   */
  design_id: string;
  /**
   * The ID of the thread this reply is in.
   */
  thread_id: string;
  author?: User;
  content: CommentContent;
  /**
   * The Canva users mentioned in the comment thread or reply.
   */
  mentions: {
    [key: string]: UserMention;
  };
  /**
   * When the reply was created, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  created_at: number;
  /**
   * When the reply was last updated, as a Unix timestamp
   * (in seconds since the Unix Epoch).
   */
  updated_at: number;
};

/**
 * The content of a comment thread or reply.
 */
export type CommentContent = {
  /**
   * The content in plaintext.
   * Any user mention tags are shown in the format `[user_id:team_id]`.
   */
  plaintext: string;
  /**
   * The content in markdown.
   * Any user mention tags are shown in the format `[user_id:team_id]`
   */
  markdown?: string;
};

/**
 * The type of suggestion event, along with additional type-specific properties.
 */
export type SuggestionEventType =
  | ({
      type?: "new";
    } & NewSuggestionEventType)
  | ({
      type?: "accepted";
    } & AcceptedSuggestionEventType)
  | ({
      type?: "rejected";
    } & RejectedSuggestionEventType)
  | ({
      type?: "reply";
    } & ReplySuggestionEventType)
  | ({
      type?: "mention";
    } & MentionSuggestionEventType);

/**
 * Event type for a new suggestion.
 */
export type NewSuggestionEventType = {
  type: "new";
  /**
   * A URL to the design, focused on the suggestion.
   */
  suggestion_url: string;
  suggestion: Thread;
};

/**
 * Event type for a suggestion that has been accepted.
 */
export type AcceptedSuggestionEventType = {
  type: "accepted";
  /**
   * A URL to the design, focused on the suggestion.
   */
  suggestion_url: string;
  suggestion: Thread;
};

/**
 * Event type for a suggestion that has been rejected.
 */
export type RejectedSuggestionEventType = {
  type: "rejected";
  /**
   * A URL to the design, focused on the suggestion.
   */
  suggestion_url: string;
  suggestion: Thread;
};

/**
 * Event type for a reply to a suggestion.
 */
export type ReplySuggestionEventType = {
  type: "reply";
  /**
   * A URL to the design, focused on the suggestion reply.
   */
  reply_url: string;
  reply: Reply;
};

/**
 * Event type for a mention in a reply to a suggestion.
 */
export type MentionSuggestionEventType = {
  type: "mention";
  /**
   * A URL to the design, focused on the suggestion reply.
   */
  reply_url: string;
  reply: Reply;
};

/**
 * The Canva users mentioned in the comment thread or reply.
 */
export type UserMentions = {
  [key: string]: UserMention;
};

/**
 * Information about the user mentioned in a comment thread or reply. Each user mention is keyed using the user's user ID and team ID separated by a colon (`user_id:team_id`).
 */
export type UserMention = {
  /**
   * The mention tag for the user mentioned in the comment thread or reply content. This has the format of the user's user ID and team ID separated by a colon (`user_id:team_id`).
   */
  tag: string;
  user: TeamUser;
};

export type GetSigningPublicKeysResponse = {
  /**
   * A Json Web Key Set (JWKS) with public keys used for signing webhooks. You can use this JWKS
   * to verify that a webhook was sent from Canva.
   */
  keys: Array<EdDsaJwk>;
};

/**
 * A JSON Web Key Set (JWKS) using the Edwards-curve Digital Signature Algorithm (EdDSA), as
 * described in [RFC-8037](https://www.rfc-editor.org/rfc/rfc8037.html#appendix-A).
 */
export type EdDsaJwk = {
  /**
   * The `kid` (key ID) is a unique identifier for a public key. When the keys used
   * to sign webhooks are rotated, you can use this ID to select the correct key
   * within a JWK Set during the key rollover. The `kid` value is case-sensitive.
   */
  kid: string;
  /**
   * The `kty` (key type) identifies the cryptographic algorithm family used with
   * the key, such as "RSA" or "EC". Only Octet Key Pairs
   * (`OKPs`) are supported.
   * The `kty` value is case-sensitive. For more information on the `kty` property
   * and OKPs, see [RFC-8037 — "kty" (Key Type)
   * Parameter](https://www.rfc-editor.org/rfc/rfc8037.html#section-2).
   */
  kty: string;
  /**
   * The `crv` (curve) property identifies the curve used for elliptical curve
   * encryptions. Only "Ed25519" is supported. For more information on the `crv`
   * property, see [RFC-8037 — Key Type
   * "OKP"](https://www.rfc-editor.org/rfc/rfc8037.html#section-2).
   */
  crv: string;
  /**
   * The `x` property is the public key of an elliptical curve encryption. The key
   * is Base64urlUInt-encoded. For more information on the `x` property, see
   * [RFC-8037 — "x" (X Coordinate)
   * Parameter](https://www.rfc-editor.org/rfc/rfc8037#section-2).
   */
  x: string;
};

/**
 * Tabular data, structured in rows of cells.
 *
 * - The first row usually contains column headers.
 * - Each cell must have a data type configured.
 * - All rows must have the same number of cells.
 * - Maximum of 100 rows and 20 columns.
 *
 * WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
 */
export type DataTable = {
  /**
   * Rows of data.
   *
   * The first row usually contains column headers.
   */
  rows: Array<DataTableRow>;
};

/**
 * A single row of tabular data.
 */
export type DataTableRow = {
  /**
   * Cells of data in row.
   *
   * All rows must have the same number of cells.
   */
  cells: Array<DataTableCell>;
};

/**
 * A single tabular data cell.
 */
export type DataTableCell =
  | ({
      type?: "string";
    } & StringDataTableCell)
  | ({
      type?: "number";
    } & NumberDataTableCell)
  | ({
      type?: "boolean";
    } & BooleanDataTableCell)
  | ({
      type?: "date";
    } & DateDataTableCell);

/**
 * A string tabular data cell.
 */
export type StringDataTableCell = {
  type: "string";
  value?: string;
};

/**
 * A number tabular data cell.
 */
export type NumberDataTableCell = {
  type: "number";
  value?: number;
};

/**
 * A boolean tabular data cell.
 */
export type BooleanDataTableCell = {
  type: "boolean";
  value?: boolean;
};

/**
 * A date tabular data cell.
 *
 * Specified as a Unix timestamp (in seconds since the Unix Epoch).
 */
export type DateDataTableCell = {
  type: "date";
  value?: number;
};

export type SortByType =
  | "relevance"
  | "modified_descending"
  | "modified_ascending"
  | "title_descending"
  | "title_ascending";

export const SortByType = {
  /**
   * Sort results using a relevance algorithm.
   */
  RELEVANCE: "relevance",
  /**
   * Sort results by the date last modified in descending order.
   */
  MODIFIED_DESCENDING: "modified_descending",
  /**
   * Sort results by the date last modified in ascending order.
   */
  MODIFIED_ASCENDING: "modified_ascending",
  /**
   * Sort results by title in descending order.
   */
  TITLE_DESCENDING: "title_descending",
  /**
   * Sort results by title in ascending order
   */
  TITLE_ASCENDING: "title_ascending",
} as const;

export type OwnershipType = "any" | "owned" | "shared";

export const OwnershipType = {
  /**
   * Owned by and shared with the user.
   */
  ANY: "any",
  /**
   * Owned by the user.
   */
  OWNED: "owned",
  /**
   * Shared with the user.
   */
  SHARED: "shared",
} as const;

export type GetListDesignResponse = {
  /**
   * A continuation token.
   * If the success response contains a continuation token, the list contains more designs
   * you can list. You can use this token as a query parameter and retrieve more
   * designs from the list, for example
   * `/v1/designs?continuation={continuation}`.
   *
   * To retrieve all of a user's designs, you might need to make multiple requests.
   */
  continuation?: string;
  /**
   * The list of designs.
   */
  items: Array<Design>;
};

/**
 * Body parameters for creating a new design.
 * At least one of `design_type` or `asset_id` must be defined
 * to create a new design.
 */
export type CreateDesignRequest = {
  design_type?: DesignTypeInput;
  /**
   * The ID of an asset to insert into the created design. Currently, this only supports image assets.
   */
  asset_id?: string;
  /**
   * The name of the design.
   */
  title?: string;
};

/**
 * Details about the new design.
 */
export type CreateDesignResponse = {
  design: Design;
};

/**
 * Successful response from a `getDesign` request.
 */
export type GetDesignResponse = {
  design: Design;
};

/**
 * Successful response from a `getDesignPages` request.
 */
export type GetDesignPagesResponse = {
  /**
   * The list of pages.
   */
  items: Array<DesignPage>;
};

/**
 * Successful response from a `getDesignExportFormats` request.
 */
export type GetDesignExportFormatsResponse = {
  formats: ExportFormatOptions;
};

/**
 * The design object, which contains metadata about the design.
 */
export type Design = {
  /**
   * The design ID.
   */
  id: string;
  /**
   * The design title.
   */
  title?: string;
  owner: TeamUserSummary;
  thumbnail?: Thumbnail;
  urls: DesignLinks;
  /**
   * When the design was created in Canva, as a Unix timestamp (in seconds since the Unix
   * Epoch).
   */
  created_at: number;
  /**
   * When the design was last updated in Canva, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  updated_at: number;
  /**
   * The total number of pages in the design. Some design types don't have pages (for example, Canva docs).
   */
  page_count?: number;
};

/**
 * The index of the page in the design. The first page in a design has the index value `1`.
 */
export type PageIndex = number;

/**
 * A temporary set of URLs for viewing or editing the design.
 */
export type DesignLinks = {
  /**
   * A temporary editing URL for the design. This URL is only accessible to the user that made the API request, and is designed to support [return navigation](https://www.canva.dev/docs/connect/return-navigation-guide/) workflows.
   *
   * NOTE: This is not a permanent URL, it is only valid for 30 days.
   */
  edit_url: string;
  /**
   * A temporary viewing URL for the design. This URL is only accessible to the user that made the API request, and is designed to support [return navigation](https://www.canva.dev/docs/connect/return-navigation-guide/) workflows.
   *
   * NOTE: This is not a permanent URL, it is only valid for 30 days.
   *
   */
  view_url: string;
};

/**
 * Basic details about the design, such as the design's ID, title, and URL.
 */
export type DesignSummary = {
  /**
   * The design ID.
   */
  id: string;
  /**
   * The design title.
   */
  title?: string;
  /**
   * URL of the design.
   */
  url?: string;
  thumbnail?: Thumbnail;
  urls: DesignLinks;
  /**
   * When the design was created in Canva, as a Unix timestamp (in seconds since the Unix
   * Epoch).
   */
  created_at: number;
  /**
   * When the design was last updated in Canva, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  updated_at: number;
  /**
   * The total number of pages in the design. Some design types don't have pages (for example, Canva docs).
   */
  page_count?: number;
};

/**
 * Basic details about a page in a design, such as the page's index and thumbnail.
 */
export type DesignPage = {
  /**
   * The index of the page in the design. The first page in a design has the index value `1`.
   */
  index: number;
  thumbnail?: Thumbnail;
};

/**
 * Metadata about the design that you include as a header parameter when importing a design.
 */
export type DesignImportMetadata = {
  /**
   * The design's title, encoded in Base64.
   *
   * The maximum length of a design title in Canva (unencoded) is 50 characters.
   *
   * Base64 encoding allows titles containing emojis and other special
   * characters to be sent using HTTP headers.
   * For example, "My Awesome Design 😍" Base64 encoded
   * is `TXkgQXdlc29tZSBEZXNpZ24g8J+YjQ==`.
   */
  title_base64: string;
  /**
   * The MIME type of the file being imported. If not provided, Canva attempts to automatically detect the type of the file.
   */
  mime_type?: string;
};

export type CreateDesignImportJobResponse = {
  job: DesignImportJob;
};

/**
 * The status of the design import job.
 */
export type DesignImportJob = {
  /**
   * The ID of the design import job.
   */
  id: string;
  status: DesignImportStatus;
  result?: DesignImportJobResult;
  error?: DesignImportError;
};

/**
 * The status of the design import job.
 */
export type DesignImportStatus = "failed" | "in_progress" | "success";

/**
 * The status of the design import job.
 */
export const DesignImportStatus = {
  FAILED: "failed",
  IN_PROGRESS: "in_progress",
  SUCCESS: "success",
} as const;

/**
 * If the import job fails, this object provides details about the error.
 */
export type DesignImportError = {
  code: DesignImportErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

/**
 * A short string about why the import failed. This field can be used to handle errors
 * programmatically.
 */
export type DesignImportErrorCode =
  | "design_creation_throttled"
  | "design_import_throttled"
  | "duplicate_import"
  | "internal_error"
  | "invalid_file"
  | "fetch_failed";

/**
 * A short string about why the import failed. This field can be used to handle errors
 * programmatically.
 */
export const DesignImportErrorCode = {
  DESIGN_CREATION_THROTTLED: "design_creation_throttled",
  DESIGN_IMPORT_THROTTLED: "design_import_throttled",
  DUPLICATE_IMPORT: "duplicate_import",
  INTERNAL_ERROR: "internal_error",
  INVALID_FILE: "invalid_file",
  FETCH_FAILED: "fetch_failed",
} as const;

export type DesignImportJobResult = {
  /**
   * A list of designs imported from the external file. It usually contains one item.
   * Imports with a large number of pages or assets are split into multiple designs.
   */
  designs: Array<DesignSummary>;
};

export type GetDesignImportJobResponse = {
  job: DesignImportJob;
};

export type CreateUrlImportJobRequest = {
  /**
   * A title for the design.
   */
  title: string;
  /**
   * The URL of the file to import. This URL must be accessible from the internet and be publicly available.
   */
  url: string;
  /**
   * The MIME type of the file being imported. If not provided, Canva attempts to automatically detect the type of the file.
   */
  mime_type?: string;
};

export type CreateUrlImportJobResponse = {
  job: DesignImportJob;
};

export type GetUrlImportJobResponse = {
  job: DesignImportJob;
};

/**
 * The desired design type.
 */
export type DesignTypeInput =
  | ({
      type?: "preset";
    } & PresetDesignTypeInput)
  | ({
      type?: "custom";
    } & CustomDesignTypeInput);

/**
 * Provide the common design type.
 */
export type PresetDesignTypeInput = {
  type: "preset";
  name: PresetDesignTypeName;
};

/**
 * The name of the design type.
 */
export type PresetDesignTypeName = "doc" | "whiteboard" | "presentation";

/**
 * The name of the design type.
 */
export const PresetDesignTypeName = {
  /**
   * A [Canva doc](https://www.canva.com/docs/); a document for Canva's online text editor.
   */
  DOC: "doc",
  /**
   * A [whiteboard](https://www.canva.com/online-whiteboard/); a design which gives you infinite space to collaborate.
   */
  WHITEBOARD: "whiteboard",
  /**
   * A [presentation](https://www.canva.com/presentations/); lets you create and collaborate for presenting to an audience.
   */
  PRESENTATION: "presentation",
} as const;

/**
 * Provide the width and height to define a custom design type.
 */
export type CustomDesignTypeInput = {
  type: "custom";
  /**
   * The width of the design, in pixels.
   */
  width: number;
  /**
   * The height of the design, in pixels.
   */
  height: number;
};

export type Error = {
  code: ErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

export type OauthError = {
  error: ErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  error_description: string;
};

/**
 * A short string indicating what failed. This field can be used to handle errors programmatically.
 *
 */
export type ErrorCode =
  | "internal_error"
  | "invalid_field"
  | "invalid_header_value"
  | "permission_denied"
  | "too_many_requests"
  | "not_found"
  | "bad_request_body"
  | "bad_http_method"
  | "bad_request_params"
  | "bad_query_params"
  | "endpoint_not_found"
  | "unsupported_version"
  | "invalid_access_token"
  | "revoked_access_token"
  | "missing_field"
  | "missing_scope"
  | "invalid_grant"
  | "invalid_request"
  | "invalid_client"
  | "unauthorized_client"
  | "unsupported_grant_type"
  | "invalid_scope"
  | "invalid_basic_header"
  | "invalid_file_format"
  | "quota_exceeded"
  | "unsupported_content_type"
  | "request_too_large"
  | "folder_not_found"
  | "item_in_multiple_folders"
  | "asset_not_found"
  | "max_limit_reached"
  | "permission_not_found"
  | "permission_exists"
  | "unauthorized_user"
  | "user_not_found"
  | "group_not_found"
  | "app_not_found"
  | "content_not_found"
  | "doctype_not_found"
  | "design_not_found"
  | "offset_too_large"
  | "page_not_found"
  | "design_or_comment_not_found"
  | "design_or_thread_not_found"
  | "design_type_not_found"
  | "team_not_found"
  | "comment_not_found"
  | "too_many_comments"
  | "too_many_replies"
  | "message_too_long"
  | "thread_not_found"
  | "reply_not_found"
  | "design_not_fillable"
  | "autofill_data_invalid"
  | "feature_not_available"
  | "license_required";

/**
 * A short string indicating what failed. This field can be used to handle errors programmatically.
 *
 */
export const ErrorCode = {
  INTERNAL_ERROR: "internal_error",
  INVALID_FIELD: "invalid_field",
  INVALID_HEADER_VALUE: "invalid_header_value",
  PERMISSION_DENIED: "permission_denied",
  TOO_MANY_REQUESTS: "too_many_requests",
  NOT_FOUND: "not_found",
  BAD_REQUEST_BODY: "bad_request_body",
  BAD_HTTP_METHOD: "bad_http_method",
  BAD_REQUEST_PARAMS: "bad_request_params",
  BAD_QUERY_PARAMS: "bad_query_params",
  ENDPOINT_NOT_FOUND: "endpoint_not_found",
  UNSUPPORTED_VERSION: "unsupported_version",
  INVALID_ACCESS_TOKEN: "invalid_access_token",
  REVOKED_ACCESS_TOKEN: "revoked_access_token",
  MISSING_FIELD: "missing_field",
  MISSING_SCOPE: "missing_scope",
  INVALID_GRANT: "invalid_grant",
  INVALID_REQUEST: "invalid_request",
  INVALID_CLIENT: "invalid_client",
  UNAUTHORIZED_CLIENT: "unauthorized_client",
  UNSUPPORTED_GRANT_TYPE: "unsupported_grant_type",
  INVALID_SCOPE: "invalid_scope",
  INVALID_BASIC_HEADER: "invalid_basic_header",
  INVALID_FILE_FORMAT: "invalid_file_format",
  QUOTA_EXCEEDED: "quota_exceeded",
  UNSUPPORTED_CONTENT_TYPE: "unsupported_content_type",
  REQUEST_TOO_LARGE: "request_too_large",
  FOLDER_NOT_FOUND: "folder_not_found",
  ITEM_IN_MULTIPLE_FOLDERS: "item_in_multiple_folders",
  ASSET_NOT_FOUND: "asset_not_found",
  MAX_LIMIT_REACHED: "max_limit_reached",
  PERMISSION_NOT_FOUND: "permission_not_found",
  PERMISSION_EXISTS: "permission_exists",
  UNAUTHORIZED_USER: "unauthorized_user",
  USER_NOT_FOUND: "user_not_found",
  GROUP_NOT_FOUND: "group_not_found",
  APP_NOT_FOUND: "app_not_found",
  CONTENT_NOT_FOUND: "content_not_found",
  DOCTYPE_NOT_FOUND: "doctype_not_found",
  DESIGN_NOT_FOUND: "design_not_found",
  OFFSET_TOO_LARGE: "offset_too_large",
  PAGE_NOT_FOUND: "page_not_found",
  DESIGN_OR_COMMENT_NOT_FOUND: "design_or_comment_not_found",
  DESIGN_OR_THREAD_NOT_FOUND: "design_or_thread_not_found",
  DESIGN_TYPE_NOT_FOUND: "design_type_not_found",
  TEAM_NOT_FOUND: "team_not_found",
  COMMENT_NOT_FOUND: "comment_not_found",
  TOO_MANY_COMMENTS: "too_many_comments",
  TOO_MANY_REPLIES: "too_many_replies",
  MESSAGE_TOO_LONG: "message_too_long",
  THREAD_NOT_FOUND: "thread_not_found",
  REPLY_NOT_FOUND: "reply_not_found",
  DESIGN_NOT_FILLABLE: "design_not_fillable",
  AUTOFILL_DATA_INVALID: "autofill_data_invalid",
  FEATURE_NOT_AVAILABLE: "feature_not_available",
  LICENSE_REQUIRED: "license_required",
} as const;

/**
 * Body parameters for starting an export job for a design.
 * It must include a design ID, and one of the supported export formats.
 */
export type CreateDesignExportJobRequest = {
  /**
   * The design ID.
   */
  design_id: string;
  format: ExportFormat;
};

/**
 * Details about the desired export format.
 */
export type ExportFormat =
  | ({
      type?: "pdf";
    } & PdfExportFormat)
  | ({
      type?: "jpg";
    } & JpgExportFormat)
  | ({
      type?: "png";
    } & PngExportFormat)
  | ({
      type?: "pptx";
    } & PptxExportFormat)
  | ({
      type?: "gif";
    } & GifExportFormat)
  | ({
      type?: "mp4";
    } & Mp4ExportFormat);

/**
 * Export the design as a PDF. Providing a paper size is optional.
 */
export type PdfExportFormat = {
  type: "pdf";
  export_quality?: ExportQuality;
  size?: ExportPageSize;
  /**
   * To specify which pages to export in a multi-page design, provide the page numbers as
   * an array. The first page in a design is page `1`.
   * If `pages` isn't specified, all the pages are exported.
   */
  pages?: Array<number>;
};

/**
 * Export the design as a GIF. Height or width (or both) may be specified, otherwise the file
 * will be exported at it's default size. Large designs will be scaled down, and aspect ratio
 * will always be maintained.
 */
export type GifExportFormat = {
  type: "gif";
  export_quality?: ExportQuality;
  /**
   * Specify the height in pixels of the exported image. Note the following behavior:
   *
   * - If no height or width is specified, the image is exported using the dimensions of the design.
   * - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
   * - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
   */
  height?: number;
  /**
   * Specify the width in pixels of the exported image. Note the following behavior:
   *
   * - If no width or height is specified, the image is exported using the dimensions of the design.
   * - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
   * - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
   */
  width?: number;
  /**
   * To specify which pages to export in a multi-page design, provide the page numbers as
   * an array. The first page in a design is page `1`.
   * If `pages` isn't specified, all the pages are exported.
   */
  pages?: Array<number>;
};

/**
 * Export the design as a JPEG. Compression quality must be provided. Height or width (or both)
 * may be specified, otherwise the file will be exported at it's default size.
 *
 * If the user is on the Canva Free plan, the export height and width for a fixed-dimension design can't be upscaled by more than a factor of `1.125`.
 */
export type JpgExportFormat = {
  type: "jpg";
  export_quality?: ExportQuality;
  /**
   * For the `jpg` type, the `quality` of the exported JPEG determines how compressed the exported file should be. A _low_ `quality` value will create a file with a smaller file size, but the resulting file will have pixelated artifacts when compared to a file created with a _high_ `quality` value.
   */
  quality: number;
  /**
   * Specify the height in pixels of the exported image. Note the following behavior:
   *
   * - If no height or width is specified, the image is exported using the dimensions of the design.
   * - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
   * - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
   */
  height?: number;
  /**
   * Specify the width in pixels of the exported image. Note the following behavior:
   *
   * - If no width or height is specified, the image is exported using the dimensions of the design.
   * - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
   * - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
   */
  width?: number;
  /**
   * To specify which pages to export in a multi-page design, provide the page numbers as
   * an array. The first page in a design is page `1`.
   * If `pages` isn't specified, all the pages are exported.
   */
  pages?: Array<number>;
};

/**
 * Export the design as a PNG. Height or width (or both) may be specified, otherwise
 * the file will be exported at it's default size. You may also specify whether to export the
 * file losslessly, and whether to export a multi-page design as a single image.
 *
 * If the user is on the Canva Free plan, the export height and width for a fixed-dimension design can't be upscaled by more than a factor of `1.125`.
 */
export type PngExportFormat = {
  type: "png";
  export_quality?: ExportQuality;
  /**
   * Specify the height in pixels of the exported image. Note the following behavior:
   *
   * - If no height or width is specified, the image is exported using the dimensions of the design.
   * - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
   * - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
   */
  height?: number;
  /**
   * Specify the width in pixels of the exported image. Note the following behavior:
   *
   * - If no width or height is specified, the image is exported using the dimensions of the design.
   * - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
   * - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
   */
  width?: number;
  /**
   * If set to `true` (default), the PNG is exported without compression.
   * If set to `false`, the PNG is compressed using a lossy compression algorithm. Lossy PNG compression is only available to users on a Canva plan that has premium features, such as Canva Pro. If the user is on the Canva Free plan and this parameter is set to `false`, the export operation will fail.
   */
  lossless?: boolean;
  /**
   * If set to `true`, the PNG is exported with a transparent background.
   * This option is only available to users on a Canva plan that has premium features, such as Canva Pro. If the user is on the Canva Free plan and this parameter is set to `true`, the export operation will fail.
   */
  transparent_background?: boolean;
  /**
   * When `true`, multi-page designs are merged into a single image.
   * When `false` (default), each page is exported as a separate image.
   */
  as_single_image?: boolean;
  /**
   * To specify which pages to export in a multi-page design, provide the page numbers as
   * an array. The first page in a design is page `1`.
   * If `pages` isn't specified, all the pages are exported.
   */
  pages?: Array<number>;
};

/**
 * Export the design as a PPTX.
 */
export type PptxExportFormat = {
  type: "pptx";
  /**
   * To specify which pages to export in a multi-page design, provide the page numbers as
   * an array. The first page in a design is page `1`.
   * If `pages` isn't specified, all the pages are exported.
   */
  pages?: Array<number>;
};

/**
 * Export the design as an MP4. You must specify the quality of the exported video.
 */
export type Mp4ExportFormat = {
  type: "mp4";
  export_quality?: ExportQuality;
  quality: Mp4ExportQuality;
  /**
   * To specify which pages to export in a multi-page design, provide the page numbers as
   * an array. The first page in a design is page `1`.
   * If `pages` isn't specified, all the pages are exported.
   */
  pages?: Array<number>;
};

export type CreateDesignExportJobResponse = {
  job: ExportJob;
};

export type GetDesignExportJobResponse = {
  job: ExportJob;
};

/**
 * The status of the export job.
 */
export type ExportJob = {
  /**
   * The export job ID.
   */
  id: string;
  status: DesignExportStatus;
  /**
   * Download URL(s) for the completed export job. These URLs expire after 24 hours.
   *
   * Depending on the design type and export format, there is a download URL for each page in the design. The list is sorted by page order.
   */
  urls?: Array<string>;
  error?: ExportError;
};

/**
 * The paper size of the export PDF file. The `size` attribute is only supported for Documents (Canva Docs).
 */
export type ExportPageSize = "a4" | "a3" | "letter" | "legal";

/**
 * The paper size of the export PDF file. The `size` attribute is only supported for Documents (Canva Docs).
 */
export const ExportPageSize = {
  A4: "a4",
  A3: "a3",
  LETTER: "letter",
  LEGAL: "legal",
} as const;

/**
 * The export status of the job. A newly created job will be `in_progress` and will eventually
 * become `success` or `failed`.
 */
export type DesignExportStatus = "failed" | "in_progress" | "success";

/**
 * The export status of the job. A newly created job will be `in_progress` and will eventually
 * become `success` or `failed`.
 */
export const DesignExportStatus = {
  FAILED: "failed",
  IN_PROGRESS: "in_progress",
  SUCCESS: "success",
} as const;

/**
 * Specify the height in pixels of the exported image. Note the following behavior:
 *
 * - If no height or width is specified, the image is exported using the dimensions of the design.
 * - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
 * - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
 */
export type ExportHeight = number;

/**
 * Specify the width in pixels of the exported image. Note the following behavior:
 *
 * - If no width or height is specified, the image is exported using the dimensions of the design.
 * - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
 * - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
 */
export type ExportWidth = number;

/**
 * The orientation and resolution of the exported video. Orientation is either `horizontal` or
 * `vertical`, and resolution is one of `480p`, `720p`, `1080p` or `4k`.
 */
export type Mp4ExportQuality =
  | "horizontal_480p"
  | "horizontal_720p"
  | "horizontal_1080p"
  | "horizontal_4k"
  | "vertical_480p"
  | "vertical_720p"
  | "vertical_1080p"
  | "vertical_4k";

/**
 * The orientation and resolution of the exported video. Orientation is either `horizontal` or
 * `vertical`, and resolution is one of `480p`, `720p`, `1080p` or `4k`.
 */
export const Mp4ExportQuality = {
  HORIZONTAL_480P: "horizontal_480p",
  HORIZONTAL_720P: "horizontal_720p",
  HORIZONTAL_1080P: "horizontal_1080p",
  HORIZONTAL_4K: "horizontal_4k",
  VERTICAL_480P: "vertical_480p",
  VERTICAL_720P: "vertical_720p",
  VERTICAL_1080P: "vertical_1080p",
  VERTICAL_4K: "vertical_4k",
} as const;

/**
 * The available file formats for exporting the design.
 */
export type ExportFormatOptions = {
  pdf?: PdfExportFormatOption;
  jpg?: JpgExportFormatOption;
  png?: PngExportFormatOption;
  svg?: SvgExportFormatOption;
  pptx?: PptxExportFormatOption;
  gif?: GifExportFormatOption;
  mp4?: Mp4ExportFormatOption;
};

/**
 * Whether the design can be exported as a PDF.
 */
export type PdfExportFormatOption = {};

/**
 * Whether the design can be exported as a GIF.
 */
export type GifExportFormatOption = {};

/**
 * Whether the design can be exported as a JPEG.
 */
export type JpgExportFormatOption = {};

/**
 * Whether the design can be exported as a PNG.
 */
export type PngExportFormatOption = {};

/**
 * Whether the design can be exported as an SVG.
 */
export type SvgExportFormatOption = {};

/**
 * Whether the design can be exported as a PPTX.
 */
export type PptxExportFormatOption = {};

/**
 * Whether the design can be exported as an MP4.
 */
export type Mp4ExportFormatOption = {};

/**
 * If the export fails, this object provides details about the error.
 */
export type ExportError = {
  code: ExportErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

/**
 * If the export failed, this specifies the reason why it failed.
 *
 * - `license_required`: The design contains [premium elements](https://www.canva.com/help/premium-elements/) that haven't been purchased. You can either buy the elements or upgrade to a Canva plan (such as Canva Pro) that has premium features, then try again. Alternatively, you can set `export_quality` to `regular` to export your document in regular quality.
 * - `approval_required`: The design requires [reviewer approval](https://www.canva.com/en_au/help/design-approval/) before it can be exported.
 * - `internal_failure`: The service encountered an error when exporting your design.
 */
export type ExportErrorCode =
  | "license_required"
  | "approval_required"
  | "internal_failure";

/**
 * If the export failed, this specifies the reason why it failed.
 *
 * - `license_required`: The design contains [premium elements](https://www.canva.com/help/premium-elements/) that haven't been purchased. You can either buy the elements or upgrade to a Canva plan (such as Canva Pro) that has premium features, then try again. Alternatively, you can set `export_quality` to `regular` to export your document in regular quality.
 * - `approval_required`: The design requires [reviewer approval](https://www.canva.com/en_au/help/design-approval/) before it can be exported.
 * - `internal_failure`: The service encountered an error when exporting your design.
 */
export const ExportErrorCode = {
  /**
   * The design contains [premium elements](https://www.canva.com/help/premium-elements/) that haven't been purchased. You can either buy the elements or upgrade to a Canva plan (such as Canva Pro) that has premium features, then try again. Alternatively, you can set `export_quality` to `regular` to export your document in regular quality.
   */
  LICENSE_REQUIRED: "license_required",
  /**
   * The design requires [reviewer approval](https://www.canva.com/en_au/help/design-approval/) before it can be exported.
   */
  APPROVAL_REQUIRED: "approval_required",
  /**
   * The service encountered an error when exporting your design.
   */
  INTERNAL_FAILURE: "internal_failure",
} as const;

/**
 * Specifies the export quality of the design.
 */
export type ExportQuality = "regular" | "pro";

/**
 * Specifies the export quality of the design.
 */
export const ExportQuality = {
  /**
   * Regular quality export.
   */
  REGULAR: "regular",
  /**
   * Premium quality export.
   *
   * NOTE: A `pro` export might fail if the design contains [premium elements](https://www.canva.com/help/premium-elements/) and the calling user either hasn't purchased the elements or isn't on a Canva plan (such as Canva Pro) that has premium features.
   */
  PRO: "pro",
} as const;

export type FolderItemSortBy =
  | "created_ascending"
  | "created_descending"
  | "modified_ascending"
  | "modified_descending"
  | "title_ascending"
  | "title_descending";

export const FolderItemSortBy = {
  /**
   * Sort results by creation date, in ascending order.
   */
  CREATED_ASCENDING: "created_ascending",
  /**
   * Sort results by creation date, in descending order.
   */
  CREATED_DESCENDING: "created_descending",
  /**
   * Sort results by the last modified date, in ascending order.
   */
  MODIFIED_ASCENDING: "modified_ascending",
  /**
   * Sort results by the last modified date, in descending order.
   */
  MODIFIED_DESCENDING: "modified_descending",
  /**
   * Sort results by title, in ascending order. The title is either the `name` field for a folder or asset, or the `title` field for a design.
   */
  TITLE_ASCENDING: "title_ascending",
  /**
   * Sort results by title, in descending order. The title is either the `name` field for a folder or asset, or the `title` field for a design.
   */
  TITLE_DESCENDING: "title_descending",
} as const;

export type FolderItemType = "design" | "folder" | "image";

export const FolderItemType = {
  DESIGN: "design",
  FOLDER: "folder",
  IMAGE: "image",
} as const;

/**
 * The folder ID.
 */
export type GetFolderResponse = {
  folder: Folder;
};

/**
 * Body parameters for creating a new folder.
 */
export type CreateFolderRequest = {
  /**
   * The name of the folder.
   */
  name: string;
  /**
   * The folder ID of the parent folder. To create a new folder at the top level of a user's
   * [projects](https://www.canva.com/help/find-designs-and-folders/), use the ID `root`.
   */
  parent_folder_id: string;
};

/**
 * Details about the new folder.
 */
export type CreateFolderResponse = {
  folder?: Folder;
};

/**
 * Body parameters for updating the folder's details.
 */
export type UpdateFolderRequest = {
  /**
   * The folder name, as shown in the Canva UI.
   */
  name: string;
};

/**
 * Details about the updated folder.
 */
export type UpdateFolderResponse = {
  folder?: Folder;
};

/**
 * A list of the items in a folder.
 * If the success response contains a continuation token, the folder contains more items
 * you can list. You can use this token as a query parameter and retrieve more
 * items from the list, for example
 * `/v1/folders/{folderId}/items?continuation={continuation}`.
 *
 * To retrieve all the items in a folder, you might need to make multiple requests.
 */
export type ListFolderItemsResponse = {
  /**
   * An array of items in the folder.
   */
  items: Array<FolderItemSummary>;
  /**
   * If the success response contains a continuation token, the folder contains more items
   * you can list. You can use this token as a query parameter and retrieve more
   * items from the list, for example
   * `/v1/folders/{folderId}/items?continuation={continuation}`.
   *
   * To retrieve all the items in a folder, you might need to make multiple requests.
   */
  continuation?: string;
};

/**
 * Details about the folder item.
 */
export type FolderItemSummary =
  | ({
      type?: "folder";
    } & FolderItem)
  | ({
      type?: "design";
    } & DesignItem)
  | ({
      type?: "image";
    } & ImageItem);

/**
 * Details about the folder.
 */
export type FolderItem = {
  type: "folder";
  folder: Folder;
};

/**
 * Details about the design.
 */
export type DesignItem = {
  type: "design";
  design: DesignSummary;
};

/**
 * Details about the image asset.
 */
export type ImageItem = {
  type: "image";
  image: AssetSummary;
};

/**
 * Body parameters for moving the folder.
 */
export type MoveFolderItemRequest = {
  /**
   * The ID of the folder you want to move the item to (the destination folder).
   * If you want to move the item to the top level of a Canva user's
   * [projects](https://www.canva.com/help/find-designs-and-folders/), use the ID `root`.
   */
  to_folder_id: string;
  /**
   * The ID of the item you want to move. Currently, video assets are not supported.
   */
  item_id: string;
};

/**
 * The folder object, which contains metadata about the folder.
 */
export type Folder = {
  /**
   * The folder ID.
   */
  id: string;
  /**
   * The folder name.
   */
  name: string;
  /**
   * When the folder was created, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  created_at: number;
  /**
   * When the folder was last updated, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  updated_at: number;
  thumbnail?: Thumbnail;
};

/**
 * This object contains some folder metadata. You can retrieve additional metadata
 * using the folder ID and the `/v1/folders/{folderId}` endpoint.
 */
export type FolderSummary = {
  /**
   * The folder ID.
   */
  id: string;
  /**
   * The folder name, as shown in the Canva UI. This property is deprecated, so you should
   * use the `name` property instead.
   * @deprecated
   */
  title?: string;
  /**
   * The folder name, as shown in the Canva UI.
   */
  name: string;
  /**
   * When the folder was created, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  created_at: number;
  /**
   * When the folder was last updated, as a Unix timestamp (in seconds since the
   * Unix Epoch).
   */
  updated_at: number;
  /**
   * The folder URL.
   */
  url?: string;
};

/**
 * Metadata for the Canva Group, consisting of the Group ID,
 * display name, and whether it's an external Canva Group.
 */
export type Group = {
  /**
   * The ID of the group with permissions to access the design.
   */
  id: string;
  /**
   * The display name of the group.
   */
  display_name?: string;
  /**
   * Is the user making the API call (the authenticated user) and the Canva Group
   * from different Canva Teams?
   *
   * - When `true`, the user and the group aren't in the same Canva Team.
   * - When `false`, the user and the group are in the same Canva Team.
   */
  external: boolean;
};

export type ExchangeAccessTokenRequest =
  | ({
      grant_type?: "authorization_code";
    } & ExchangeAuthCodeRequest)
  | ({
      grant_type?: "refresh_token";
    } & ExchangeRefreshTokenRequest);

export type ExchangeAuthCodeRequest = {
  /**
   * For exchanging an authorization code for an access token.
   */
  grant_type: "authorization_code";
  /**
   * The `code_verifier` value that you generated when creating the user authorization URL.
   */
  code_verifier: string;
  /**
   * The authorization code you received after the user authorized the integration.
   */
  code: string;
  /**
   * Your integration's unique ID, for authenticating the request.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_id?: string;
  /**
   * Your integration's client secret, for authenticating the request. Begins with `cnvca`.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_secret?: string;
  /**
   * Only required if a redirect URL was supplied when you [created the user authorization URL](https://www.canva.dev/docs/connect/authentication/#create-the-authorization-url).
   *
   * Must be one of those already specified by the client. If not supplied, the first redirect_uri defined for the client will be used by default.
   *
   */
  redirect_uri?: string;
};

export type ExchangeRefreshTokenRequest = {
  /**
   * For generating an access token using a refresh token.
   */
  grant_type: "refresh_token";
  /**
   * Your integration's unique ID, for authenticating the request.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_id?: string;
  /**
   * Your integration's client secret, for authenticating the request. Begins with `cnvca`.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_secret?: string;
  /**
   * The refresh token to be exchanged. You can copy this value from the successful response received when generating an access token.
   */
  refresh_token: string;
  /**
   * Optional scope value when refreshing an access token. Separate multiple [scopes](https://www.canva.dev/docs/connect/appendix/scopes/) with a single space between each scope.
   *
   * The requested scope cannot include any permissions not already granted, so this parameter allows you to limit the scope when refreshing a token. If omitted, the scope for the token remains unchanged.
   *
   */
  scope?: string;
};

/**
 * Exchange auth token to access token.
 */
export type ExchangeAccessTokenResponse = {
  /**
   * The bearer access token to use to authenticate to Canva Connect API endpoints. If requested using a `authorization_code` or `refresh_token`, this allows you to act on behalf of a user.
   */
  access_token: string;
  /**
   * The token that you can use to refresh the access token.
   */
  refresh_token: string;
  /**
   * The token type returned. This is always `Bearer`.
   */
  token_type: string;
  /**
   * The expiry time (in seconds) for the token.
   */
  expires_in: number;
  /**
   * The [scopes](https://www.canva.dev/docs/connect/appendix/scopes/) that the token has been granted.
   */
  scope?: string;
};

export type IntrospectTokenRequest = {
  /**
   * The token to introspect.
   */
  token: string;
  /**
   * Your integration's unique ID, for authenticating the request.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_id?: string;
  /**
   * Your integration's client secret, for authenticating the request. Begins with `cnvca`.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_secret?: string;
};

/**
 * Introspection result of access or refresh tokens
 */
export type IntrospectTokenResponse = {
  /**
   * Whether the access token is active.
   *
   * If `true`, the access token is valid and active. If `false`, the access token is invalid.
   *
   */
  active: boolean;
  /**
   * The [scopes](https://www.canva.dev/docs/connect/appendix/scopes/) that the token has been granted.
   */
  scope?: string;
  /**
   * The ID of the client that requested the token.
   */
  client?: string;
  /**
   * The expiration time of the token, as a [Unix timestamp](https://en.wikipedia.org/wiki/Unix_time) in seconds.
   */
  exp?: number;
  /**
   * When the token was issued, as a [Unix timestamp](https://en.wikipedia.org/wiki/Unix_time) in seconds.
   */
  iat?: number;
  /**
   * The "not before" time of the token, which specifies the time before which the access token most not be accepted, as a [Unix timestamp](https://en.wikipedia.org/wiki/Unix_time) in seconds.
   */
  nbf?: number;
  /**
   * A unique ID for the access token.
   */
  jti?: string;
  /**
   * The subject of the claim. This is the ID of the Canva user that the access token acts on behalf of.
   *
   * This is an obfuscated value, so a single user has a unique ID for each integration. If the same user authorizes another integration, their ID in that other integration is different.
   *
   */
  sub?: string;
};

/**
 * Supply an access token or refresh token to have its lineage revoked.
 */
export type RevokeTokensRequest = {
  /**
   * The token to revoke.
   */
  token: string;
  /**
   * Your integration's unique ID, for authenticating the request.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_id?: string;
  /**
   * Your integration's client secret, for authenticating the request. Begins with `cnvca`.
   *
   * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
   *
   */
  client_secret?: string;
};

/**
 * The response on a successful token revocation.
 */
export type RevokeTokensResponse = {};

/**
 * The [scopes](https://www.canva.dev/docs/connect/appendix/scopes/) that the token has been granted.
 */
export type scope_response = string;

/**
 * Your integration's unique ID, for authenticating the request.
 *
 * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
 *
 */
export type client_id = string;

/**
 * Your integration's client secret, for authenticating the request. Begins with `cnvca`.
 *
 * NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
 *
 */
export type client_secret = string;

/**
 * Body parameters for starting a resize job for a design.
 * It must include a design ID, and one of the supported design type.
 */
export type CreateDesignResizeJobRequest = {
  /**
   * The design ID.
   */
  design_id: string;
  design_type: DesignTypeInput;
};

export type CreateDesignResizeJobResponse = {
  job: DesignResizeJob;
};

export type GetDesignResizeJobResponse = {
  job: DesignResizeJob;
};

/**
 * Details about the design resize job.
 */
export type DesignResizeJob = {
  /**
   * The design resize job ID.
   */
  id: string;
  status: DesignResizeStatus;
  result?: DesignResizeJobResult;
  error?: DesignResizeError;
};

/**
 * Design has been created and saved to user's root
 * ([projects](https://www.canva.com/help/find-designs-and-folders/)) folder.
 */
export type DesignResizeJobResult = {
  design: DesignSummary;
};

/**
 * Status of the design resize job.
 */
export type DesignResizeStatus = "in_progress" | "success" | "failed";

/**
 * Status of the design resize job.
 */
export const DesignResizeStatus = {
  IN_PROGRESS: "in_progress",
  SUCCESS: "success",
  FAILED: "failed",
} as const;

export type DesignResizeErrorCode =
  | "thumbnail_generation_error"
  | "design_resize_error"
  | "create_design_error";

export const DesignResizeErrorCode = {
  THUMBNAIL_GENERATION_ERROR: "thumbnail_generation_error",
  DESIGN_RESIZE_ERROR: "design_resize_error",
  CREATE_DESIGN_ERROR: "create_design_error",
} as const;

/**
 * If the design resize job fails, this object provides details about the error.
 */
export type DesignResizeError = {
  code: DesignResizeErrorCode;
  /**
   * A human-readable description of what went wrong.
   */
  message: string;
};

/**
 * Metadata for the Canva Team, consisting of the Team ID,
 * display name, and whether it's an external Canva Team.
 */
export type Team = {
  /**
   * The ID of the Canva Team.
   */
  id: string;
  /**
   * The name of the Canva Team as shown in the Canva UI.
   */
  display_name: string;
  /**
   * Is the user making the API call (the authenticated user) from the Canva Team shown?
   *
   * - When `true`, the user isn't in the Canva Team shown.
   * - When `false`, the user is in the Canva Team shown.
   */
  external: boolean;
};

/**
 * A thumbnail image representing the object.
 */
export type Thumbnail = {
  /**
   * The width of the thumbnail image in pixels.
   */
  width: number;
  /**
   * The height of the thumbnail image in pixels.
   */
  height: number;
  /**
   * A URL for retrieving the thumbnail image.
   * This URL expires after 15 minutes. This URL includes a query string
   * that's required for retrieving the thumbnail.
   */
  url: string;
};

/**
 * Metadata for the user, consisting of the User ID and display name.
 */
export type User = {
  /**
   * The ID of the user.
   */
  id: string;
  /**
   * The name of the user as shown in the Canva UI.
   */
  display_name?: string;
};

/**
 * Metadata for the user, consisting of the User ID and Team ID.
 */
export type TeamUserSummary = {
  /**
   * The ID of the user.
   */
  user_id: string;
  /**
   * The ID of the user's Canva Team.
   */
  team_id: string;
};

/**
 * Metadata for the user, consisting of the User ID, Team ID, and display name.
 */
export type TeamUser = {
  /**
   * The ID of the user.
   */
  user_id?: string;
  /**
   * The ID of the user's Canva Team.
   */
  team_id?: string;
  /**
   * The name of the user as shown in the Canva UI.
   */
  display_name?: string;
};

/**
 * Profile for the user, consisting of the display name and other attributes.
 */
export type UserProfile = {
  /**
   * The name of the user as shown in the Canva UI.
   */
  display_name?: string;
};

export type UsersMeResponse = {
  team_user: TeamUserSummary;
};

export type UserProfileResponse = {
  profile: UserProfile;
};

export type GetUserCapabilitiesResponse = {
  capabilities?: Array<Capability>;
};

export type Notification = {
  /**
   * The unique identifier for the notification.
   */
  id: string;
  /**
   * When the notification was created, as a UNIX timestamp (in seconds
   * since the UNIX epoch).
   */
  created_at: number;
  content: NotificationContent;
};

/**
 * The notification content object, which contains metadata about the event.
 */
export type NotificationContent =
  | ({
      type?: "share_design";
    } & ShareDesignNotificationContent)
  | ({
      type?: "share_folder";
    } & ShareFolderNotificationContent)
  | ({
      type?: "comment";
    } & CommentNotificationContent)
  | ({
      type?: "design_access_requested";
    } & DesignAccessRequestedNotificationContent)
  | ({
      type?: "design_approval_requested";
    } & DesignApprovalRequestedNotificationContent)
  | ({
      type?: "design_approval_response";
    } & DesignApprovalResponseNotificationContent)
  | ({
      type?: "design_approval_reviewer_invalidated";
    } & DesignApprovalReviewerInvalidatedNotificationContent)
  | ({
      type?: "design_mention";
    } & DesignMentionNotificationContent)
  | ({
      type?: "team_invite";
    } & TeamInviteNotificationContent)
  | ({
      type?: "folder_access_requested";
    } & FolderAccessRequestedNotificationContent)
  | ({
      type?: "suggestion";
    } & SuggestionNotificationContent);

/**
 * The notification content for when someone shares a design.
 */
export type ShareDesignNotificationContent = {
  type: "share_design";
  triggering_user: User;
  receiving_team_user: TeamUser;
  design: DesignSummary;
  /**
   * A URL that the user who receives the notification can use to access the shared design.
   */
  share_url: string;
  share?: ShareAction;
};

/**
 * The notification content for when someone shares a folder.
 */
export type ShareFolderNotificationContent = {
  type: "share_folder";
  triggering_user: User;
  receiving_team_user: TeamUser;
  folder: FolderSummary;
  share?: ShareAction;
};

/**
 * The notification content for when someone comments on a design.
 */
export type CommentNotificationContent = {
  type: "comment";
  triggering_user: User;
  receiving_team_user: TeamUser;
  design: DesignSummary;
  /**
   * A URL to the design, focused on the new comment.
   *
   * The `comment_url` property is deprecated.
   * For details of the comment event, use the `comment_event` property instead.
   * @deprecated
   */
  comment_url?: string;
  comment?: CommentEventDeprecated;
  comment_event?: CommentEvent;
};

/**
 * The notification content for when someone requests access to a design.
 */
export type DesignAccessRequestedNotificationContent = {
  type: "design_access_requested";
  triggering_user: TeamUser;
  receiving_team_user: TeamUser;
  design: DesignSummary;
  /**
   * A URL, which is scoped only to the user that can grant the requested access to the
   * design, that approves the requested access.
   */
  grant_access_url: string;
};

/**
 * The notification content for when someone requests a user to
 * [approve a design](https://www.canva.com/help/get-approval/).
 */
export type DesignApprovalRequestedNotificationContent = {
  type: "design_approval_requested";
  triggering_user: User;
  initial_requesting_user: TeamUser;
  receiving_team_user: TeamUser;
  requested_groups: Array<Group>;
  design: DesignSummary;
  /**
   * A URL, which is scoped only to the user requested to review the design, that links to
   * the design with the approval UI opened.
   */
  approve_url: string;
  approval_request: ApprovalRequestAction;
};

/**
 * The notification content for when someone approves a design or gives feedback.
 */
export type DesignApprovalResponseNotificationContent = {
  type: "design_approval_response";
  triggering_user: User;
  receiving_team_user: TeamUser;
  initial_requesting_user: TeamUser;
  responding_groups: Array<Group>;
  design: DesignSummary;
  approval_response: ApprovalResponseAction;
};

/**
 * The notification content for when a reviewer in a design is invalidated.
 */
export type DesignApprovalReviewerInvalidatedNotificationContent = {
  type: "design_approval_reviewer_invalidated";
  receiving_team_user: TeamUserSummary;
  design: DesignSummary;
};

/**
 * The notification content for when someone mentions a user in a design.
 *
 * The link to the design in this notification is valid for 30 days, and can only be opened by
 * the recipient of the notification.
 */
export type DesignMentionNotificationContent = {
  type: "design_mention";
  triggering_user: User;
  receiving_team_user: TeamUser;
  design: DesignSummary;
};

/**
 * The notification content for when someone is invited to a
 * [Canva team](https://www.canva.com/help/about-canva-for-teams/).
 */
export type TeamInviteNotificationContent = {
  type: "team_invite";
  triggering_user: User;
  receiving_user: User;
  inviting_team: Team;
};

/**
 * The notification content for when someone requests access to a folder.
 */
export type FolderAccessRequestedNotificationContent = {
  type: "folder_access_requested";
  triggering_user: TeamUser;
  receiving_team_user: TeamUser;
  folder: FolderSummary;
};

/**
 * The notification content when someone does one of the following actions:
 *
 * - Suggests edits to a design.
 * - Applies or rejects a suggestion.
 * - Replies to a suggestion.
 * - Mentions a user in a reply to a suggestion.
 */
export type SuggestionNotificationContent = {
  type: "suggestion";
  triggering_user: User;
  receiving_team_user: TeamUser;
  design: DesignSummary;
  suggestion_event_type: SuggestionEventType;
};

/**
 * Metadata about the share event.
 */
export type ShareAction = {
  /**
   * The optional message users can include when sharing something with another
   * user using the Canva UI.
   */
  message: string;
};

/**
 * Metadata about the design approval request.
 */
export type ApprovalRequestAction = {
  /**
   * The message included by the user when requesting a design approval.
   */
  message?: string;
};

/**
 * Metadata about the design approval response.
 */
export type ApprovalResponseAction = {
  /**
   * Whether the design was approved. When `true`, the reviewer has approved
   * the design.
   */
  approved: boolean;
  /**
   * Whether the design is ready to publish. When `true`, the design has been approved
   * by all reviewers and can be published.
   */
  ready_to_publish?: boolean;
  /**
   * The message included by a user responding to a design approval request.
   */
  message?: string;
};

/**
 * The brand template ID.
 */
export type brandTemplateId = string;

/**
 * The ID of the comment.
 */
export type commentId = string;

/**
 * The ID of the thread.
 */
export type threadId = string;

/**
 * The ID of the reply.
 */
export type replyId = string;

/**
 * The design ID.
 */
export type designId = string;

/**
 * The export job ID.
 */
export type exportId = string;

/**
 * The folder ID.
 */
export type folderIdParameter = string;

export type GetAppJwksData = {
  body?: never;
  path: {
    /**
     * The app id
     */
    appId: string;
  };
  query?: never;
  url: "/v1/apps/{appId}/jwks";
};

export type GetAppJwksErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetAppJwksError = GetAppJwksErrors[keyof GetAppJwksErrors];

export type GetAppJwksResponses = {
  /**
   * OK
   */
  200: GetAppJwksResponse;
};

export type GetAppJwksResponse2 =
  GetAppJwksResponses[keyof GetAppJwksResponses];

export type DeleteAssetData = {
  body?: never;
  path: {
    /**
     * The ID of the asset.
     */
    assetId: string;
  };
  query?: never;
  url: "/v1/assets/{assetId}";
};

export type DeleteAssetErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type DeleteAssetError = DeleteAssetErrors[keyof DeleteAssetErrors];

export type DeleteAssetResponses = {
  /**
   * OK
   */
  204: void;
};

export type DeleteAssetResponse =
  DeleteAssetResponses[keyof DeleteAssetResponses];

export type GetAssetData = {
  body?: never;
  path: {
    /**
     * The ID of the asset.
     */
    assetId: string;
  };
  query?: never;
  url: "/v1/assets/{assetId}";
};

export type GetAssetErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetAssetError = GetAssetErrors[keyof GetAssetErrors];

export type GetAssetResponses = {
  /**
   * OK
   */
  200: GetAssetResponse;
};

export type GetAssetResponse2 = GetAssetResponses[keyof GetAssetResponses];

export type UpdateAssetData = {
  body?: UpdateAssetRequest;
  path: {
    /**
     * The ID of the asset.
     */
    assetId: string;
  };
  query?: never;
  url: "/v1/assets/{assetId}";
};

export type UpdateAssetErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type UpdateAssetError = UpdateAssetErrors[keyof UpdateAssetErrors];

export type UpdateAssetResponses = {
  /**
   * OK
   */
  200: UpdateAssetResponse;
};

export type UpdateAssetResponse2 =
  UpdateAssetResponses[keyof UpdateAssetResponses];

export type CreateAssetUploadJobData = {
  /**
   * Binary of the asset to upload.
   */
  body: Blob | File;
  headers: {
    "Asset-Upload-Metadata": AssetUploadMetadata;
  };
  path?: never;
  query?: never;
  url: "/v1/asset-uploads";
};

export type CreateAssetUploadJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateAssetUploadJobError =
  CreateAssetUploadJobErrors[keyof CreateAssetUploadJobErrors];

export type CreateAssetUploadJobResponses = {
  /**
   * OK
   */
  200: CreateAssetUploadJobResponse;
};

export type CreateAssetUploadJobResponse2 =
  CreateAssetUploadJobResponses[keyof CreateAssetUploadJobResponses];

export type GetAssetUploadJobData = {
  body?: never;
  path: {
    /**
     * The asset upload job ID.
     */
    jobId: string;
  };
  query?: never;
  url: "/v1/asset-uploads/{jobId}";
};

export type GetAssetUploadJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetAssetUploadJobError =
  GetAssetUploadJobErrors[keyof GetAssetUploadJobErrors];

export type GetAssetUploadJobResponses = {
  /**
   * OK
   */
  200: GetAssetUploadJobResponse;
};

export type GetAssetUploadJobResponse2 =
  GetAssetUploadJobResponses[keyof GetAssetUploadJobResponses];

export type CreateDesignAutofillJobData = {
  body?: CreateDesignAutofillJobRequest;
  url: "/v1/autofills";
};

export type CreateDesignAutofillJobErrors = {
  /**
   * Bad Request
   */
  400: Error;
  /**
   * Forbidden
   */
  403: Error;
  /**
   * Not Found
   */
  404: Error;
  /**
   * Error Response
   */
  default: Error;
};

export type CreateDesignAutofillJobError =
  CreateDesignAutofillJobErrors[keyof CreateDesignAutofillJobErrors];

export type CreateDesignAutofillJobResponses = {
  /**
   * OK
   */
  200: CreateDesignAutofillJobResponse;
};

export type CreateDesignAutofillJobResponse2 =
  CreateDesignAutofillJobResponses[keyof CreateDesignAutofillJobResponses];

export type GetDesignAutofillJobData = {
  body?: never;
  path: {
    /**
     * The design autofill job ID.
     */
    jobId: string;
  };
  query?: never;
  url: "/v1/autofills/{jobId}";
};

export type GetDesignAutofillJobErrors = {
  /**
   * Forbidden
   */
  403: Error;
  /**
   * Not Found
   */
  404: Error;
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignAutofillJobError =
  GetDesignAutofillJobErrors[keyof GetDesignAutofillJobErrors];

export type GetDesignAutofillJobResponses = {
  /**
   * OK
   */
  200: GetDesignAutofillJobResponse;
};

export type GetDesignAutofillJobResponse2 =
  GetDesignAutofillJobResponses[keyof GetDesignAutofillJobResponses];

export type ListBrandTemplatesData = {
  body?: never;
  path?: never;
  query?: {
    /**
     * Lets you search the brand templates available to the user using a search term or terms.
     */
    query?: string;
    /**
     * If the success response contains a continuation token, the user has access to more
     * brand templates you can list. You can use this token as a query parameter and retrieve
     * more templates from the list, for example
     * `/v1/brand-templates?continuation={continuation}`.
     * To retrieve all the brand templates available to the user, you might need to make
     * multiple requests.
     */
    continuation?: string;
    /**
     * Filter the list of brand templates based on the user's ownership of the brand templates.
     */
    ownership?: OwnershipType;
    /**
     * Sort the list of brand templates.
     */
    sort_by?: SortByType;
    /**
     * Filter the list of brand templates based on the brand templates' dataset definitions.
     * Brand templates with dataset definitions are mainly used with the [Autofill APIs](https://www.canva.dev/docs/connect/api-reference/autofills/).
     */
    dataset?: DatasetFilter;
  };
  url: "/v1/brand-templates";
};

export type ListBrandTemplatesErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type ListBrandTemplatesError =
  ListBrandTemplatesErrors[keyof ListBrandTemplatesErrors];

export type ListBrandTemplatesResponses = {
  /**
   * OK
   */
  200: ListBrandTemplatesResponse;
};

export type ListBrandTemplatesResponse2 =
  ListBrandTemplatesResponses[keyof ListBrandTemplatesResponses];

export type GetBrandTemplateData = {
  body?: never;
  path: {
    /**
     * The brand template ID.
     */
    brandTemplateId: string;
  };
  query?: never;
  url: "/v1/brand-templates/{brandTemplateId}";
};

export type GetBrandTemplateErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetBrandTemplateError =
  GetBrandTemplateErrors[keyof GetBrandTemplateErrors];

export type GetBrandTemplateResponses = {
  /**
   * OK
   */
  200: GetBrandTemplateResponse;
};

export type GetBrandTemplateResponse2 =
  GetBrandTemplateResponses[keyof GetBrandTemplateResponses];

export type GetBrandTemplateDatasetData = {
  body?: never;
  path: {
    /**
     * The brand template ID.
     */
    brandTemplateId: string;
  };
  query?: never;
  url: "/v1/brand-templates/{brandTemplateId}/dataset";
};

export type GetBrandTemplateDatasetErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetBrandTemplateDatasetError =
  GetBrandTemplateDatasetErrors[keyof GetBrandTemplateDatasetErrors];

export type GetBrandTemplateDatasetResponses = {
  /**
   * OK
   */
  200: GetBrandTemplateDatasetResponse;
};

export type GetBrandTemplateDatasetResponse2 =
  GetBrandTemplateDatasetResponses[keyof GetBrandTemplateDatasetResponses];

export type CreateCommentData = {
  body: CreateCommentRequest;
  url: "/v1/comments";
};

export type CreateCommentErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateCommentError = CreateCommentErrors[keyof CreateCommentErrors];

export type CreateCommentResponses = {
  /**
   * OK
   */
  200: CreateCommentResponse;
};

export type CreateCommentResponse2 =
  CreateCommentResponses[keyof CreateCommentResponses];

export type CreateReplyDeprecatedData = {
  body: CreateReplyRequest;
  path: {
    /**
     * The ID of the comment.
     */
    commentId: string;
  };
  query?: never;
  url: "/v1/comments/{commentId}/replies";
};

export type CreateReplyDeprecatedErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateReplyDeprecatedError =
  CreateReplyDeprecatedErrors[keyof CreateReplyDeprecatedErrors];

export type CreateReplyDeprecatedResponses = {
  /**
   * OK
   */
  200: CreateReplyResponse;
};

export type CreateReplyDeprecatedResponse =
  CreateReplyDeprecatedResponses[keyof CreateReplyDeprecatedResponses];

export type ListRepliesData = {
  body?: never;
  path: {
    /**
     * The design ID.
     */
    designId: string;
    /**
     * The ID of the thread.
     */
    threadId: string;
  };
  query?: {
    /**
     * The number of replies to return.
     */
    limit?: number;
    /**
     * If the success response contains a continuation token, the list contains more items you can list. You can use this token as a query parameter and retrieve more items from the list, for example `?continuation={continuation}`.
     *
     * To retrieve all items, you might need to make multiple requests.
     */
    continuation?: string;
  };
  url: "/v1/designs/{designId}/comments/{threadId}/replies";
};

export type ListRepliesErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type ListRepliesError = ListRepliesErrors[keyof ListRepliesErrors];

export type ListRepliesResponses = {
  /**
   * OK
   */
  200: ListRepliesResponse;
};

export type ListRepliesResponse2 =
  ListRepliesResponses[keyof ListRepliesResponses];

export type CreateReplyData = {
  body: CreateReplyV2Request;
  path: {
    /**
     * The design ID.
     */
    designId: string;
    /**
     * The ID of the thread.
     */
    threadId: string;
  };
  query?: never;
  url: "/v1/designs/{designId}/comments/{threadId}/replies";
};

export type CreateReplyErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateReplyError = CreateReplyErrors[keyof CreateReplyErrors];

export type CreateReplyResponses = {
  /**
   * OK
   */
  200: CreateReplyV2Response;
};

export type CreateReplyResponse2 =
  CreateReplyResponses[keyof CreateReplyResponses];

export type GetThreadData = {
  body?: never;
  path: {
    /**
     * The design ID.
     */
    designId: string;
    /**
     * The ID of the thread.
     */
    threadId: string;
  };
  query?: never;
  url: "/v1/designs/{designId}/comments/{threadId}";
};

export type GetThreadErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetThreadError = GetThreadErrors[keyof GetThreadErrors];

export type GetThreadResponses = {
  /**
   * OK
   */
  200: GetThreadResponse;
};

export type GetThreadResponse2 = GetThreadResponses[keyof GetThreadResponses];

export type GetReplyData = {
  body?: never;
  path: {
    /**
     * The design ID.
     */
    designId: string;
    /**
     * The ID of the thread.
     */
    threadId: string;
    /**
     * The ID of the reply.
     */
    replyId: string;
  };
  query?: never;
  url: "/v1/designs/{designId}/comments/{threadId}/replies/{replyId}";
};

export type GetReplyErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetReplyError = GetReplyErrors[keyof GetReplyErrors];

export type GetReplyResponses = {
  /**
   * OK
   */
  200: GetReplyResponse;
};

export type GetReplyResponse2 = GetReplyResponses[keyof GetReplyResponses];

export type CreateThreadData = {
  body: CreateThreadRequest;
  path: {
    /**
     * The design ID.
     */
    designId: string;
  };
  query?: never;
  url: "/v1/designs/{designId}/comments";
};

export type CreateThreadErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateThreadError = CreateThreadErrors[keyof CreateThreadErrors];

export type CreateThreadResponses = {
  /**
   * OK
   */
  200: CreateThreadResponse;
};

export type CreateThreadResponse2 =
  CreateThreadResponses[keyof CreateThreadResponses];

export type GetSigningPublicKeysData = {
  body?: never;
  url: "/v1/connect/keys";
};

export type GetSigningPublicKeysErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetSigningPublicKeysError =
  GetSigningPublicKeysErrors[keyof GetSigningPublicKeysErrors];

export type GetSigningPublicKeysResponses = {
  /**
   * OK
   */
  200: GetSigningPublicKeysResponse;
};

export type GetSigningPublicKeysResponse2 =
  GetSigningPublicKeysResponses[keyof GetSigningPublicKeysResponses];

export type ListDesignsData = {
  body?: never;
  path?: never;
  query?: {
    /**
     * Lets you search the user's designs, and designs shared with the user, using a search term or terms.
     */
    query?: string;
    /**
     * If the success response contains a continuation token, the list contains more designs
     * you can list. You can use this token as a query parameter and retrieve more
     * designs from the list, for example
     * `/v1/designs?continuation={continuation}`.
     *
     * To retrieve all of a user's designs, you might need to make multiple requests.
     */
    continuation?: string;
    /**
     * Filter the list of designs based on the user's ownership of the designs.
     */
    ownership?: OwnershipType;
    /**
     * Sort the list of designs.
     */
    sort_by?: SortByType;
  };
  url: "/v1/designs";
};

export type ListDesignsErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type ListDesignsError = ListDesignsErrors[keyof ListDesignsErrors];

export type ListDesignsResponses = {
  /**
   * OK
   */
  200: GetListDesignResponse;
};

export type ListDesignsResponse =
  ListDesignsResponses[keyof ListDesignsResponses];

export type CreateDesignData = {
  body?: CreateDesignRequest;
  url: "/v1/designs";
};

export type CreateDesignErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateDesignError = CreateDesignErrors[keyof CreateDesignErrors];

export type CreateDesignResponses = {
  /**
   * OK
   */
  200: CreateDesignResponse;
};

export type CreateDesignResponse2 =
  CreateDesignResponses[keyof CreateDesignResponses];

export type GetDesignData = {
  body?: never;
  path: {
    /**
     * The design ID.
     */
    designId: string;
  };
  query?: never;
  url: "/v1/designs/{designId}";
};

export type GetDesignErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignError = GetDesignErrors[keyof GetDesignErrors];

export type GetDesignResponses = {
  /**
   * OK
   */
  200: GetDesignResponse;
};

export type GetDesignResponse2 = GetDesignResponses[keyof GetDesignResponses];

export type GetDesignPagesData = {
  body?: never;
  path: {
    /**
     * The design ID.
     */
    designId: string;
  };
  query?: {
    /**
     * The page index to start the range of pages to return.
     *
     * Pages are indexed using one-based numbering, so the first page in a design has the index value `1`.
     *
     */
    offset?: number;
    /**
     * The number of pages to return, starting at the page index specified using the `offset` parameter.
     */
    limit?: number;
  };
  url: "/v1/designs/{designId}/pages";
};

export type GetDesignPagesErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignPagesError =
  GetDesignPagesErrors[keyof GetDesignPagesErrors];

export type GetDesignPagesResponses = {
  /**
   * OK
   */
  200: GetDesignPagesResponse;
};

export type GetDesignPagesResponse2 =
  GetDesignPagesResponses[keyof GetDesignPagesResponses];

export type GetDesignExportFormatsData = {
  body?: never;
  path: {
    /**
     * The design ID.
     */
    designId: string;
  };
  query?: never;
  url: "/v1/designs/{designId}/export-formats";
};

export type GetDesignExportFormatsErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignExportFormatsError =
  GetDesignExportFormatsErrors[keyof GetDesignExportFormatsErrors];

export type GetDesignExportFormatsResponses = {
  /**
   * OK
   */
  200: GetDesignExportFormatsResponse;
};

export type GetDesignExportFormatsResponse2 =
  GetDesignExportFormatsResponses[keyof GetDesignExportFormatsResponses];

export type CreateDesignImportJobData = {
  /**
   * Binary of the file to import.
   */
  body: Blob | File;
  headers: {
    "Import-Metadata": DesignImportMetadata;
  };
  path?: never;
  query?: never;
  url: "/v1/imports";
};

export type CreateDesignImportJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateDesignImportJobError =
  CreateDesignImportJobErrors[keyof CreateDesignImportJobErrors];

export type CreateDesignImportJobResponses = {
  /**
   * OK
   */
  200: CreateDesignImportJobResponse;
};

export type CreateDesignImportJobResponse2 =
  CreateDesignImportJobResponses[keyof CreateDesignImportJobResponses];

export type GetDesignImportJobData = {
  body?: never;
  path: {
    /**
     * The design import job ID.
     */
    jobId: string;
  };
  query?: never;
  url: "/v1/imports/{jobId}";
};

export type GetDesignImportJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignImportJobError =
  GetDesignImportJobErrors[keyof GetDesignImportJobErrors];

export type GetDesignImportJobResponses = {
  /**
   * OK
   */
  200: GetDesignImportJobResponse;
};

export type GetDesignImportJobResponse2 =
  GetDesignImportJobResponses[keyof GetDesignImportJobResponses];

export type CreateUrlImportJobData = {
  body: CreateUrlImportJobRequest;
  url: "/v1/url-imports";
};

export type CreateUrlImportJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateUrlImportJobError =
  CreateUrlImportJobErrors[keyof CreateUrlImportJobErrors];

export type CreateUrlImportJobResponses = {
  /**
   * OK
   */
  200: CreateUrlImportJobResponse;
};

export type CreateUrlImportJobResponse2 =
  CreateUrlImportJobResponses[keyof CreateUrlImportJobResponses];

export type GetUrlImportJobData = {
  body?: never;
  path: {
    /**
     * The ID of the URL import job.
     */
    jobId: string;
  };
  query?: never;
  url: "/v1/url-imports/{jobId}";
};

export type GetUrlImportJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetUrlImportJobError =
  GetUrlImportJobErrors[keyof GetUrlImportJobErrors];

export type GetUrlImportJobResponses = {
  /**
   * OK
   */
  200: GetUrlImportJobResponse;
};

export type GetUrlImportJobResponse2 =
  GetUrlImportJobResponses[keyof GetUrlImportJobResponses];

export type CreateDesignExportJobData = {
  body?: CreateDesignExportJobRequest;
  url: "/v1/exports";
};

export type CreateDesignExportJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateDesignExportJobError =
  CreateDesignExportJobErrors[keyof CreateDesignExportJobErrors];

export type CreateDesignExportJobResponses = {
  /**
   * OK
   */
  200: CreateDesignExportJobResponse;
};

export type CreateDesignExportJobResponse2 =
  CreateDesignExportJobResponses[keyof CreateDesignExportJobResponses];

export type GetDesignExportJobData = {
  body?: never;
  path: {
    /**
     * The export job ID.
     */
    exportId: string;
  };
  query?: never;
  url: "/v1/exports/{exportId}";
};

export type GetDesignExportJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignExportJobError =
  GetDesignExportJobErrors[keyof GetDesignExportJobErrors];

export type GetDesignExportJobResponses = {
  /**
   * OK
   */
  200: GetDesignExportJobResponse;
};

export type GetDesignExportJobResponse2 =
  GetDesignExportJobResponses[keyof GetDesignExportJobResponses];

export type DeleteFolderData = {
  body?: never;
  path: {
    /**
     * The folder ID.
     */
    folderId: string;
  };
  query?: never;
  url: "/v1/folders/{folderId}";
};

export type DeleteFolderErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type DeleteFolderError = DeleteFolderErrors[keyof DeleteFolderErrors];

export type DeleteFolderResponses = {
  /**
   * OK
   */
  204: void;
};

export type DeleteFolderResponse =
  DeleteFolderResponses[keyof DeleteFolderResponses];

export type GetFolderData = {
  body?: never;
  path: {
    /**
     * The folder ID.
     */
    folderId: string;
  };
  query?: never;
  url: "/v1/folders/{folderId}";
};

export type GetFolderErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetFolderError = GetFolderErrors[keyof GetFolderErrors];

export type GetFolderResponses = {
  /**
   * OK
   */
  200: GetFolderResponse;
};

export type GetFolderResponse2 = GetFolderResponses[keyof GetFolderResponses];

export type UpdateFolderData = {
  body: UpdateFolderRequest;
  path: {
    /**
     * The folder ID.
     */
    folderId: string;
  };
  query?: never;
  url: "/v1/folders/{folderId}";
};

export type UpdateFolderErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type UpdateFolderError = UpdateFolderErrors[keyof UpdateFolderErrors];

export type UpdateFolderResponses = {
  /**
   * OK
   */
  200: UpdateFolderResponse;
};

export type UpdateFolderResponse2 =
  UpdateFolderResponses[keyof UpdateFolderResponses];

export type ListFolderItemsData = {
  body?: never;
  path: {
    /**
     * The folder ID.
     */
    folderId: string;
  };
  query?: {
    /**
     * If the success response contains a continuation token, the folder contains more items
     * you can list. You can use this token as a query parameter and retrieve more
     * items from the list, for example
     * `/v1/folders/{folderId}/items?continuation={continuation}`.
     *
     * To retrieve all the items in a folder, you might need to make multiple requests.
     */
    continuation?: string;
    /**
     * Filter the folder items to only return specified types. The available types are:
     * `design`, `folder`, and `image`. To filter for more than one item type, provide a comma-
     * delimited list.
     */
    item_types?: Array<FolderItemType>;
    /**
     * Sort the list of folder items.
     */
    sort_by?: FolderItemSortBy;
  };
  url: "/v1/folders/{folderId}/items";
};

export type ListFolderItemsErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type ListFolderItemsError =
  ListFolderItemsErrors[keyof ListFolderItemsErrors];

export type ListFolderItemsResponses = {
  /**
   * OK
   */
  200: ListFolderItemsResponse;
};

export type ListFolderItemsResponse2 =
  ListFolderItemsResponses[keyof ListFolderItemsResponses];

export type MoveFolderItemData = {
  body?: MoveFolderItemRequest;
  url: "/v1/folders/move";
};

export type MoveFolderItemErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type MoveFolderItemError =
  MoveFolderItemErrors[keyof MoveFolderItemErrors];

export type MoveFolderItemResponses = {
  /**
   * OK
   */
  204: void;
};

export type MoveFolderItemResponse =
  MoveFolderItemResponses[keyof MoveFolderItemResponses];

export type CreateFolderData = {
  body: CreateFolderRequest;
  url: "/v1/folders";
};

export type CreateFolderErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateFolderError = CreateFolderErrors[keyof CreateFolderErrors];

export type CreateFolderResponses = {
  /**
   * OK
   */
  200: CreateFolderResponse;
};

export type CreateFolderResponse2 =
  CreateFolderResponses[keyof CreateFolderResponses];

export type ExchangeAccessTokenData = {
  body: ExchangeAccessTokenRequest;
  url: "/v1/oauth/token";
};

export type ExchangeAccessTokenErrors = {
  /**
   * Error Response
   */
  default: OauthError;
};

export type ExchangeAccessTokenError =
  ExchangeAccessTokenErrors[keyof ExchangeAccessTokenErrors];

export type ExchangeAccessTokenResponses = {
  /**
   * OK
   */
  200: ExchangeAccessTokenResponse;
};

export type ExchangeAccessTokenResponse2 =
  ExchangeAccessTokenResponses[keyof ExchangeAccessTokenResponses];

export type IntrospectTokenData = {
  body: IntrospectTokenRequest;
  url: "/v1/oauth/introspect";
};

export type IntrospectTokenErrors = {
  /**
   * Error Response
   */
  default: OauthError;
};

export type IntrospectTokenError =
  IntrospectTokenErrors[keyof IntrospectTokenErrors];

export type IntrospectTokenResponses = {
  /**
   * OK
   */
  200: IntrospectTokenResponse;
};

export type IntrospectTokenResponse2 =
  IntrospectTokenResponses[keyof IntrospectTokenResponses];

export type RevokeTokensData = {
  body: RevokeTokensRequest;
  url: "/v1/oauth/revoke";
};

export type RevokeTokensErrors = {
  /**
   * Error Response
   */
  default: OauthError;
};

export type RevokeTokensError = RevokeTokensErrors[keyof RevokeTokensErrors];

export type RevokeTokensResponses = {
  /**
   * OK
   */
  200: RevokeTokensResponse;
};

export type RevokeTokensResponse2 =
  RevokeTokensResponses[keyof RevokeTokensResponses];

export type CreateDesignResizeJobData = {
  body?: CreateDesignResizeJobRequest;
  url: "/v1/resizes";
};

export type CreateDesignResizeJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type CreateDesignResizeJobError =
  CreateDesignResizeJobErrors[keyof CreateDesignResizeJobErrors];

export type CreateDesignResizeJobResponses = {
  /**
   * OK
   */
  200: CreateDesignResizeJobResponse;
};

export type CreateDesignResizeJobResponse2 =
  CreateDesignResizeJobResponses[keyof CreateDesignResizeJobResponses];

export type GetDesignResizeJobData = {
  body?: never;
  path: {
    /**
     * The design resize job ID.
     */
    jobId: string;
  };
  query?: never;
  url: "/v1/resizes/{jobId}";
};

export type GetDesignResizeJobErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetDesignResizeJobError =
  GetDesignResizeJobErrors[keyof GetDesignResizeJobErrors];

export type GetDesignResizeJobResponses = {
  /**
   * OK
   */
  200: GetDesignResizeJobResponse;
};

export type GetDesignResizeJobResponse2 =
  GetDesignResizeJobResponses[keyof GetDesignResizeJobResponses];

export type UsersMeData = {
  body?: never;
  url: "/v1/users/me";
};

export type UsersMeErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type UsersMeError = UsersMeErrors[keyof UsersMeErrors];

export type UsersMeResponses = {
  /**
   * OK
   */
  200: UsersMeResponse;
};

export type UsersMeResponse2 = UsersMeResponses[keyof UsersMeResponses];

export type GetUserCapabilitiesData = {
  body?: never;
  url: "/v1/users/me/capabilities";
};

export type GetUserCapabilitiesErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetUserCapabilitiesError =
  GetUserCapabilitiesErrors[keyof GetUserCapabilitiesErrors];

export type GetUserCapabilitiesResponses = {
  /**
   * OK
   */
  200: GetUserCapabilitiesResponse;
};

export type GetUserCapabilitiesResponse2 =
  GetUserCapabilitiesResponses[keyof GetUserCapabilitiesResponses];

export type GetUserProfileData = {
  body?: never;
  url: "/v1/users/me/profile";
};

export type GetUserProfileErrors = {
  /**
   * Error Response
   */
  default: Error;
};

export type GetUserProfileError =
  GetUserProfileErrors[keyof GetUserProfileErrors];

export type GetUserProfileResponses = {
  /**
   * OK
   */
  200: UserProfileResponse;
};

export type GetUserProfileResponse =
  GetUserProfileResponses[keyof GetUserProfileResponses];
