{"name": "ecommerce-shop-demo", "description": "A demo integration showcasing how an ecommerce platform can leverage the Canva Connect API", "scripts": {"start": "ts-node ./scripts/start.ts", "lint:types": "cd backend && npm run lint:types && cd ../frontend && npm run lint:types && tsc", "lint": "cd backend && npm run lint && cd ../frontend && npm run lint", "lint:fix": "cd backend && npm run lint:fix && cd ../frontend && npm run lint:fix", "format:scripts": "prettier 'scripts/**/*.{css,ts,tsx}' --no-config --write", "format": "npm run format:scripts && cd backend && npm run format && cd ../frontend && npm run format", "format:check:scripts": "prettier 'scripts/**/*.{css,ts,tsx}' --no-config --check --ignore-path", "format:check": "npm run format:check:scripts && cd backend && npm run format:check && cd ../frontend && npm run format:check", "generate:db-key": "ts-node ../common/scripts/generate-key.ts --save", "generate:dotenv": "ts-node ../common/scripts/setup-env.ts", "products:read": "cd backend && npm run products:read", "products:reset": "cd backend && npm run products:reset", "postinstall": "npm run generate:dotenv && npm run generate:db-key", "test": "cd backend && npm run test"}, "engines": {"node": ">=20.14.0"}, "engineStrict": true, "author": "Canva Pty Ltd.", "license": "SEE LICENSE IN LICENSE.md in root directory", "private": true, "type": "commonjs", "dependencies": {"@hey-api/client-fetch": "0.5.0", "cookie-parser": "1.4.7", "dotenv": "^16.5.0", "jose": "5.9.6"}, "devDependencies": {"@eslint/js": "9.23.0", "@ngrok/ngrok": "1.4.1", "@types/cookie-parser": "1.4.8", "@types/jest": "29.5.14", "@types/node": "20.10.0", "@types/nodemon": "1.19.6", "@types/yargs": "17.0.33", "@typescript-eslint/eslint-plugin": "8.27.0", "@typescript-eslint/parser": "8.27.0", "chalk": "4.1.2", "cli-table3": "0.6.5", "envfile": "7.1.0", "eslint": "9.23.0", "eslint-plugin-jest": "28.11.0", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-unicorn": "57.0.0", "jest": "29.7.0", "nodemon": "3.0.1", "prettier": "3.4.2", "ts-jest": "29.2.5", "ts-node": "^10.9.2", "typescript": "^5.5.4", "typescript-eslint": "8.27.0", "webpack-cli": "5.1.4", "webpack-dev-server": "5.2.0", "yargs": "17.7.2"}}