{"compilerOptions": {"jsx": "react", "lib": ["dom", "dom.iterable", "es2018", "es2019.array", "es2019.object", "es2019.string", "es2020.promise", "es2020.string"], "types": ["node", "jest"], "composite": false, "declaration": false, "declarationMap": false, "experimentalDecorators": true, "importHelpers": true, "noImplicitOverride": true, "rootDir": "..", "outDir": "dist", "noEmit": true, "strict": true, "skipLibCheck": true, "target": "ES2019", "sourceMap": true, "inlineSources": true, "moduleResolution": "node", "noImplicitAny": false, "removeComments": true, "preserveConstEnums": true, "allowSyntheticDefaultImports": true, "baseUrl": "./"}, "include": ["./node_modules/@types/**/*", "./scripts/*", "../common"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}