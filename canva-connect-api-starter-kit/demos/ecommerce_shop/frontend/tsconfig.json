{"compilerOptions": {"jsx": "react-jsx", "lib": ["dom", "dom.iterable", "es2018", "es2019.array", "es2019.object", "es2019.string", "es2020.promise", "es2020.string"], "types": ["node"], "composite": false, "declaration": true, "declarationMap": false, "experimentalDecorators": true, "importHelpers": true, "noImplicitOverride": true, "moduleResolution": "node", "rootDir": "../..", "outDir": "dist", "noEmit": true, "strict": true, "skipLibCheck": true, "target": "ES2019", "sourceMap": true, "inlineSources": true, "module": "ESNext", "noImplicitAny": false, "removeComments": true, "preserveConstEnums": true, "allowSyntheticDefaultImports": true, "baseUrl": "./"}, "include": ["./**/*", "../node_modules/@types/**/*", "../../common/utils"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}