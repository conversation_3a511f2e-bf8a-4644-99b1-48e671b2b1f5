{"name": "connect-api-playground", "description": "A skeleton integration with auth setup to bootstrap your integration exploration and experimentation!", "scripts": {"start": "ts-node ./scripts/start.ts", "lint:types": "cd backend && npm run lint:types && cd ../frontend && npm run lint:types && tsc", "lint": "cd backend && npm run lint && cd ../frontend && npm run lint", "lint:fix": "cd backend && npm run lint:fix && cd ../frontend && npm run lint:fix", "format:scripts": "prettier 'scripts/**/*.{css,ts,tsx}' --no-config --write", "format": "npm run format:scripts && cd backend && npm run format && cd ../frontend && npm run format", "format:check:scripts": "prettier 'scripts/**/*.{css,ts,tsx}' --no-config --check --ignore-path", "format:check": "npm run format:check:scripts && cd backend && npm run format:check && cd ../frontend && npm run format:check", "generate:db-key": "ts-node ../common/scripts/generate-key.ts --save", "generate:dotenv": "ts-node ../common/scripts/setup-env.ts", "postinstall": "npm run generate:dotenv && npm run generate:db-key"}, "engines": {"node": ">=20.14.0"}, "engineStrict": true, "author": "Canva Pty Ltd.", "license": "SEE LICENSE IN LICENSE.md in root directory", "private": true, "type": "commonjs", "dependencies": {"@hey-api/client-fetch": "0.5.0", "cookie-parser": "1.4.7", "jose": "5.9.6"}, "devDependencies": {"@eslint/js": "9.23.0", "@types/cookie-parser": "1.4.8", "@types/node": "20.10.0", "@types/yargs": "17.0.33", "@typescript-eslint/eslint-plugin": "8.27.0", "@typescript-eslint/parser": "8.27.0", "eslint": "9.23.0", "eslint-plugin-jest": "28.11.0", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-unicorn": "57.0.0", "prettier": "3.4.2", "ts-node": "10.9.2", "typescript": "5.5.4", "typescript-eslint": "8.27.0", "yargs": "17.7.2"}}