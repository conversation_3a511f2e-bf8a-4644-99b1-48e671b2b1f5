html
  head
    title Authorization Success
  body
    p Successfully authorized! This window will close automatically in #{countdownSecs} seconds.
    script.
      const AUTHORIZATION_SUCCESS_MESSAGE = "#{message}";
      const postMessageAndClose = () => {
        window.opener.postMessage(AUTHORIZATION_SUCCESS_MESSAGE, "*");
        window.close();
      };

      setTimeout(postMessageAndClose, #{countdownSecs * 1000});

      // Listen for the window being closed manually by the user
      window.addEventListener('beforeunload', postMessageAndClose);
