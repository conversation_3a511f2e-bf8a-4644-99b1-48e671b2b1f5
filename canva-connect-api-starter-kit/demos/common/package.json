{"name": "demo-common-modules", "description": "A collection of helpers and modules used in our various demo integrations.", "engines": {"node": ">=20.14.0"}, "scripts": {"lint:types": "tsc", "format": "prettier '**/*.{css,ts,tsx}' --no-config --write", "format:check": "prettier '**/*.{css,ts,tsx}' --no-config --check --ignore-path", "test": "jest --config=./jest.config.js"}, "engineStrict": true, "author": "Canva Pty Ltd.", "license": "SEE LICENSE IN LICENSE.md in root directory", "private": true, "type": "commonjs", "dependencies": {"@canva/connect-api-ts": "file:../../client/ts", "@ngrok/ngrok": "1.4.1", "@types/jest": "29.5.14", "chalk": "4.1.2", "cli-table3": "0.6.5", "cookie-parser": "1.4.7", "cors": "2.8.5", "dotenv": "16.4.7", "jest": "29.7.0", "ts-jest": "29.2.5", "envfile": "7.1.0", "express": "4.21.2", "jose": "5.9.6", "nodemon": "3.0.1", "open": "10.1.0", "pug": "3.0.3", "webpack-cli": "5.1.4", "webpack-dev-server": "5.2.0", "yargs": "17.7.2"}, "devDependencies": {"@types/cors": "2.8.17", "@types/express": "4.17.21", "@types/multer": "1.4.12", "@types/node": "20.10.0", "@types/nodemon": "1.19.6", "@types/yargs": "17.0.33", "prettier": "3.4.2", "typescript": "5.5.4"}}