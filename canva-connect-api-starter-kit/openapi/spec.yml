openapi: 3.0.0
info:
  contact:
    name: Canva Developer Community
    url: https://community.canva.dev/
  description: API for building integrations with Canva via a REST api
  license:
    name: ©2023 All Rights Reserved
  termsOfService: https://www.canva.com/trust/legal/
  title: Canva Connect API
  version: latest
servers:
  - description: Canva Connect API
    url: https://api.canva.com/rest
tags:
  - name: app
  - name: asset
  - name: autofill
  - name: brand_template
  - name: comment
  - name: connect
  - name: design
  - name: design_import
  - name: export
  - name: folder
  - name: oauth
  - name: resize
  - name: user
paths:
  /v1/apps/{appId}/jwks:
    get:
      description: |-
        Returns the Json Web Key Set (public keys) of an app. These keys are used to
        verify JWTs sent to app backends.
      operationId: getAppJwks
      parameters:
        - description: The app id
          explode: false
          in: path
          name: appId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAppJwksResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      tags:
        - app
  /v1/assets/{assetId}:
    delete:
      description: |-
        You can delete an asset by specifying its `assetId`. This operation mirrors the behavior
        in the Canva UI. Deleting an item moves it to the trash.
        Deleting an asset doesn't remove it from designs that already use it.
      operationId: deleteAsset
      parameters:
        - description: The ID of the asset.
          explode: false
          in: path
          name: assetId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "204":
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - asset:write
      tags:
        - asset
      x-rate-limit-per-client-user: 30
    get:
      description: You can retrieve the metadata of an asset by specifying its `assetId`.
      operationId: getAsset
      parameters:
        - description: The ID of the asset.
          explode: false
          in: path
          name: assetId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAssetResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - asset:read
      tags:
        - asset
      x-rate-limit-per-client-user: 100
    patch:
      description: |-
        You can update the name and tags of an asset by specifying its `assetId`. Updating the tags
        replaces all existing tags of the asset.
      operationId: updateAsset
      parameters:
        - description: The ID of the asset.
          explode: false
          in: path
          name: assetId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAssetRequest"
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateAssetResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - asset:write
      tags:
        - asset
      x-rate-limit-per-client-user: 30
  /v1/asset-uploads:
    post:
      description: |-
        Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to upload an asset to the user's content library. Supported file types for assets are listed in the [Assets API overview](https://www.canva.dev/docs/connect/api-reference/assets/).

        The request format for this endpoint is an `application/octet-stream` body of bytes. Attach
        information about the upload using an `Asset-Upload-Metadata` header.


        <Note>

        For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of asset upload jobs created with this API using the [Get asset upload job API](https://www.canva.dev/docs/connect/api-reference/assets/get-asset-upload-job/).

        </Note>
      operationId: CreateAssetUploadJob
      parameters:
        - content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssetUploadMetadata"
          in: header
          name: Asset-Upload-Metadata
          required: true
      requestBody:
        content:
          application/octet-stream:
            schema:
              format: binary
              type: string
        description: Binary of the asset to upload.
        required: true
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressAssetUploadJobExample"
                success:
                  $ref: "#/components/examples/SuccessAssetUploadJobExample"
                failed:
                  $ref: "#/components/examples/FailedAssetUploadJobExample"
              schema:
                $ref: "#/components/schemas/CreateAssetUploadJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - asset:write
      tags:
        - asset
      x-rate-limit-per-client-user: 30
  /v1/asset-uploads/{jobId}:
    get:
      description: |-
        Get the result of an asset upload job that was created using the [Create asset upload job API](https://www.canva.dev/docs/connect/api-reference/assets/create-asset-upload-job/).

        You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
      operationId: GetAssetUploadJob
      parameters:
        - description: The asset upload job ID.
          explode: false
          in: path
          name: jobId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressAssetUploadJobExample"
                success:
                  $ref: "#/components/examples/SuccessAssetUploadJobExample"
                failed:
                  $ref: "#/components/examples/FailedAssetUploadJobExample"
              schema:
                $ref: "#/components/schemas/GetAssetUploadJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - asset:read
      tags:
        - asset
      x-rate-limit-per-client-user: 180
  /v1/autofills:
    post:
      description: |-
        <Warning>

        Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.

        </Warning>

        <Note>

        To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.

        </Note>

        Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to autofill a Canva design using a brand template and input data.

        To get a list of input data fields, use the [Get brand template dataset
        API](https://www.canva.dev/docs/connect/api-reference/brand-templates/get-brand-template-dataset/).

        Available data field types to autofill include:

        - Images
        - Text
        - Charts

          WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.

        <Note>

        For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of autofill jobs created with this API using the [Get design autofill job API](https://www.canva.dev/docs/connect/api-reference/autofills/get-design-autofill-job/).

        </Note>
      operationId: createDesignAutofillJob
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateDesignAutofillJobRequest"
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressAutofillJobExample"
                success:
                  $ref: "#/components/examples/SuccessAutofillJobExample"
                failed:
                  $ref: "#/components/examples/FailedAutofillJobExample"
              schema:
                $ref: "#/components/schemas/CreateDesignAutofillJobResponse"
          description: OK
        "400":
          content:
            application/json:
              examples:
                design_title_invalid:
                  $ref: "#/components/examples/DesignTitleInvalidError"
              schema:
                $ref: "#/components/schemas/Error"
          description: Bad Request
        "403":
          content:
            application/json:
              examples:
                brand_template_forbidden:
                  $ref: "#/components/examples/BrandTemplateForbiddenError"
              schema:
                $ref: "#/components/schemas/Error"
          description: Forbidden
        "404":
          content:
            application/json:
              examples:
                brand_template_not_found:
                  $ref: "#/components/examples/BrandTemplateNotFoundError"
              schema:
                $ref: "#/components/schemas/Error"
          description: Not Found
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:write
      tags:
        - autofill
      x-rate-limit-per-client-user: 10
  /v1/autofills/{jobId}:
    get:
      description: |-
        <Note>

        To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.

        </Note>

        Get the result of a design autofill job that was created using the [Create design autofill job
        API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).

        You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
      operationId: getDesignAutofillJob
      parameters:
        - description: The design autofill job ID.
          explode: false
          in: path
          name: jobId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressAutofillJobExample"
                success:
                  $ref: "#/components/examples/SuccessAutofillJobExample"
                failed:
                  $ref: "#/components/examples/FailedAutofillJobExample"
              schema:
                $ref: "#/components/schemas/GetDesignAutofillJobResponse"
          description: OK
        "404":
          content:
            application/json:
              examples:
                design_autofill_job_not_found:
                  $ref: "#/components/examples/DesignAutofillJobNotFoundError"
              schema:
                $ref: "#/components/schemas/Error"
          description: Not Found
        "403":
          content:
            application/json:
              examples:
                design_autofill_job_forbidden:
                  $ref: "#/components/examples/DesignAutofillJobForbiddenError"
              schema:
                $ref: "#/components/schemas/Error"
          description: Forbidden
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:meta:read
      tags:
        - autofill
      x-rate-limit-per-client-user: 60
  /v1/brand-templates:
    get:
      description: |-
        <Warning>

        Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.

        </Warning>

        <Note>

        To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.

        </Note>

        Get a list of the [brand templates](https://www.canva.com/help/publish-team-template/) the user has access to.
      operationId: listBrandTemplates
      parameters:
        - description:
            Lets you search the brand templates available to the user using
            a search term or terms.
          explode: true
          in: query
          name: query
          required: false
          schema:
            type: string
          style: form
        - description: |-
            If the success response contains a continuation token, the user has access to more
            brand templates you can list. You can use this token as a query parameter and retrieve
            more templates from the list, for example
            `/v1/brand-templates?continuation={continuation}`.
            To retrieve all the brand templates available to the user, you might need to make
            multiple requests.
          explode: true
          in: query
          name: continuation
          required: false
          schema:
            type: string
          style: form
        - description:
            Filter the list of brand templates based on the user's ownership
            of the brand templates.
          explode: true
          in: query
          name: ownership
          required: false
          schema:
            $ref: "#/components/schemas/OwnershipType"
          style: form
        - description: Sort the list of brand templates.
          explode: true
          in: query
          name: sort_by
          required: false
          schema:
            $ref: "#/components/schemas/SortByType"
          style: form
        - description: |-
            Filter the list of brand templates based on the brand templates' dataset definitions.
            Brand templates with dataset definitions are mainly used with the [Autofill APIs](https://www.canva.dev/docs/connect/api-reference/autofills/).
          explode: true
          in: query
          name: dataset
          required: false
          schema:
            $ref: "#/components/schemas/DatasetFilter"
          style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListBrandTemplatesResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - brandtemplate:meta:read
      tags:
        - brand_template
      x-rate-limit-per-client-user: 100
  /v1/brand-templates/{brandTemplateId}:
    get:
      description: |-
        <Warning>

        Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.

        </Warning>

        <Note>

        To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.

        </Note>

        Retrieves the metadata for a brand template.
      operationId: getBrandTemplate
      parameters:
        - description: The brand template ID.
          explode: false
          in: path
          name: brandTemplateId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBrandTemplateResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - brandtemplate:meta:read
      tags:
        - brand_template
      x-rate-limit-per-client-user: 100
  /v1/brand-templates/{brandTemplateId}/dataset:
    get:
      description: |-
        <Warning>

        Soon, all brand template IDs will be updated to a new format. If your integration stores brand template IDs, you'll need to migrate to use the new IDs. After we implement this change, you'll have 6 months to migrate before the old IDs are removed.

        </Warning>

        <Note>

        To use this API, your integration must act on behalf of a user that's a member of a [Canva Enterprise](https://www.canva.com/enterprise/) organization.

        </Note>

        Gets the dataset definition of a brand template. If the brand
        template contains autofill data fields, this API returns an object with the data field
        names and the type of data they accept.

        Available data field types include:

        - Images
        - Text
        - Charts

        You can autofill a brand template using the [Create a design autofill job
        API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).

        WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
      operationId: getBrandTemplateDataset
      parameters:
        - description: The brand template ID.
          explode: false
          in: path
          name: brandTemplateId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetBrandTemplateDatasetResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - brandtemplate:content:read
      tags:
        - brand_template
      x-rate-limit-per-client-user: 100
  /v1/comments:
    post:
      deprecated: true
      description: |-
        <Warning>

        This API is deprecated, so you should use the [Create thread](https://www.canva.dev/docs/connect/api-reference/comments/create-thread/) API instead.

        </Warning>

        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Create a new top-level comment on a design.
        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/). A design can have a maximum
        of 1000 comments.
      operationId: createComment
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCommentRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateCommentResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:write
      tags:
        - comment
      x-rate-limit-per-client-user: 20
  /v1/comments/{commentId}/replies:
    post:
      deprecated: true
      description: |-
        <Warning>

        This API is deprecated, so you should use the [Create reply](https://www.canva.dev/docs/connect/api-reference/comments/create-reply/) API instead.

        </Warning>

        <Warning>

        This API is currently provided as a preview. Be aware of the following:
        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Creates a reply to a comment in a design.
        To reply to an existing thread of comments, you can use either the `id` of the parent
        (original) comment, or the `thread_id` of a comment in the thread. Each comment can
        have a maximum of 100 replies created for it.

        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/).
      operationId: createReplyDeprecated
      parameters:
        - description: The ID of the comment.
          example: KeAZEAjijEb
          explode: false
          in: path
          name: commentId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateReplyRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateReplyResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:write
      tags:
        - comment
      x-rate-limit-per-client-user: 20
  /v1/designs/{designId}/comments/{threadId}/replies:
    get:
      description: |-
        <Warning>
        This API is currently provided as a preview. Be aware of the following:
        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
        </Warning>

        Retrieves a list of replies for a comment or suggestion thread on a design.

        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/).
      operationId: listReplies
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: The ID of the thread.
          example: KeAbiEAjZEj
          explode: false
          in: path
          name: threadId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - explode: true
          in: query
          name: limit
          required: false
          schema:
            default: 50
            description: The number of replies to return.
            maximum: 100
            minimum: 1
            type: integer
          style: form
        - description: |-
            If the success response contains a continuation token, the list contains more items you can list. You can use this token as a query parameter and retrieve more items from the list, for example `?continuation={continuation}`.

            To retrieve all items, you might need to make multiple requests.
          explode: true
          in: query
          name: continuation
          required: false
          schema:
            type: string
          style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListRepliesResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:read
      tags:
        - comment
      x-rate-limit-per-client-user: 100
    post:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Creates a reply to a comment or suggestion thread on a design.
        To reply to an existing thread, you must provide the ID of the thread
        which is returned when a thread is created, or from the `thread_id` value
        of an existing reply in the thread. Each thread can
        have a maximum of 100 replies created for it.

        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/).
      operationId: createReply
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: The ID of the thread.
          example: KeAbiEAjZEj
          explode: false
          in: path
          name: threadId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateReplyV2Request"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateReplyV2Response"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:write
      tags:
        - comment
      x-rate-limit-per-client-user: 20
  /v1/designs/{designId}/comments/{threadId}:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Gets a comment or suggestion thread on a design.
        To retrieve a reply to a comment thread, use the [Get reply](https://www.canva.dev/docs/connect/api-reference/comments/get-reply/) API.
        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/).
      operationId: getThread
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: The ID of the thread.
          example: KeAbiEAjZEj
          explode: false
          in: path
          name: threadId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetThreadResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:read
      tags:
        - comment
      x-rate-limit-per-client-user: 100
  /v1/designs/{designId}/comments/{threadId}/replies/{replyId}:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Gets a reply to a comment or suggestion thread on a design.
        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/).
      operationId: getReply
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: The ID of the thread.
          example: KeAbiEAjZEj
          explode: false
          in: path
          name: threadId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: The ID of the reply.
          example: KeAZEAjijEb
          explode: false
          in: path
          name: replyId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetReplyResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:read
      tags:
        - comment
      x-rate-limit-per-client-user: 100
  /v1/designs/{designId}/comments:
    post:
      description: |-
        <Warning>
        This API is currently provided as a preview. Be aware of the following:
        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.
        </Warning>

        Creates a new comment thread on a design.
        For information on comments and how they're used in the Canva UI, see the
        [Canva Help Center](https://www.canva.com/help/comments/).
      operationId: createThread
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateThreadRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateThreadResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - comment:write
      tags:
        - comment
      x-rate-limit-per-client-user: 100
  /v1/connect/keys:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        The Keys API (`connect/keys`) is a security measure you can use to verify the authenticity
        of webhooks you receive from Canva Connect. The Keys API returns a
        [JSON Web Key (JWK)](https://www.rfc-editor.org/rfc/rfc7517#section-2), which you can use to
        decrypt the webhook signature and verify it came from Canva and not a potentially malicious
        actor. This helps to protect your systems from
        [Replay attacks](https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures/).

        The keys returned by the Keys API can rotate. We recommend you cache the keys you receive
        from this API where possible, and only access this API when you receive a webhook signed
        with an unrecognized key. This allows you to verify webhooks quicker than accessing this API
        every time you receive a webhook.
      operationId: getSigningPublicKeys
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetSigningPublicKeysResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      tags:
        - connect
  /v1/designs:
    get:
      description: |-
        Lists metadata for all the designs in a Canva user's
        [projects](https://www.canva.com/help/find-designs-and-folders/). You can also:

        - Use search terms to filter the listed designs.
        - Show designs either created by, or shared with the user.
        - Sort the results.
      operationId: listDesigns
      parameters:
        - description:
            "Lets you search the user's designs, and designs shared with\
            \ the user, using a search term or terms."
          example: party invites
          explode: true
          in: query
          name: query
          required: false
          schema:
            maxLength: 255
            type: string
          style: form
        - description: |-
            If the success response contains a continuation token, the list contains more designs
            you can list. You can use this token as a query parameter and retrieve more
            designs from the list, for example
            `/v1/designs?continuation={continuation}`.

            To retrieve all of a user's designs, you might need to make multiple requests.
          example: RkFGMgXlsVTDbMd:MR3L0QjiaUzycIAjx0yMyuNiV0OildoiOwL0x32G4NjNu4FwtAQNxowUQNMMYN
          explode: true
          in: query
          name: continuation
          required: false
          schema:
            type: string
          style: form
        - description:
            Filter the list of designs based on the user's ownership of the
            designs.
          example: any
          explode: true
          in: query
          name: ownership
          required: false
          schema:
            $ref: "#/components/schemas/OwnershipType"
          style: form
        - description: Sort the list of designs.
          example: relevance
          explode: true
          in: query
          name: sort_by
          required: false
          schema:
            $ref: "#/components/schemas/SortByType"
          style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetListDesignResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:meta:read
      tags:
        - design
      x-rate-limit-per-client-user: 100
    post:
      description: |-
        Creates a new Canva design. To create a new design, you can either:

        - Use a preset design type.
        - Set height and width dimensions for a custom design.

        Additionally, you can also provide the `asset_id` of an asset in the user's [projects](https://www.canva.com/help/find-designs-and-folders/) to add to the new design. Currently, this only supports image assets. To list the assets in a folder in the user's projects, use the [List folder items API](https://www.canva.dev/docs/connect/api-reference/folders/list-folder-items/).
      operationId: createDesign
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateDesignRequest"
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateDesignResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:write
      tags:
        - design
      x-rate-limit-per-client-user: 20
  /v1/designs/{designId}:
    get:
      description:
        "Gets the metadata for a design. This includes owner information,\
        \ URLs for editing and viewing, and thumbnail information."
      operationId: getDesign
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDesignResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:meta:read
      tags:
        - design
      x-rate-limit-per-client-user: 100
  /v1/designs/{designId}/pages:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Lists metadata for pages in a design, such as page-specific thumbnails.

        For the specified design, you can provide `offset` and `limit` values to specify the range of pages to return.

        NOTE: Some design types don't have pages (for example, Canva docs).
      operationId: getDesignPages
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: |
            The page index to start the range of pages to return.

            Pages are indexed using one-based numbering, so the first page in a design has the index value `1`.
          explode: true
          in: query
          name: offset
          required: false
          schema:
            default: 1
            format: int32
            maximum: 500
            minimum: 1
            type: integer
          style: form
        - description:
            "The number of pages to return, starting at the page index specified\
            \ using the `offset` parameter."
          explode: true
          in: query
          name: limit
          required: false
          schema:
            default: 50
            format: int32
            maximum: 200
            minimum: 1
            type: integer
          style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDesignPagesResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:read
      tags:
        - design
      x-rate-limit-per-client-user: 100
  /v1/designs/{designId}/export-formats:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Lists the available file formats for [exporting a design](https://www.canva.dev/docs/connect/api-reference/exports/create-design-export-job/).
      operationId: getDesignExportFormats
      parameters:
        - description: The design ID.
          explode: false
          in: path
          name: designId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDesignExportFormatsResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:read
      tags:
        - design
      x-rate-limit-per-client-user: 100
  /v1/imports:
    post:
      description: |-
        Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to import an external file as a new design in Canva.

        The request format for this endpoint has an `application/octet-stream` body of bytes,
        and the information about the import is provided using an `Import-Metadata` header.

        Supported file types for imports are listed in [Design imports overview](https://www.canva.dev/docs/connect/api-reference/design-imports/#supported-file-types).

        <Note>

        For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of design import jobs created with this API using the [Get design import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/get-design-import-job/).

        </Note>
      operationId: createDesignImportJob
      parameters:
        - content:
            application/json:
              schema:
                $ref: "#/components/schemas/DesignImportMetadata"
          in: header
          name: Import-Metadata
          required: true
      requestBody:
        content:
          application/octet-stream:
            schema:
              format: binary
              type: string
        description: Binary of the file to import.
        required: true
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressDesignImportJobExample"
                success:
                  $ref: "#/components/examples/SuccessDesignImportJobExample"
                failed:
                  $ref: "#/components/examples/FailedDesignImportJobExample"
              schema:
                $ref: "#/components/schemas/CreateDesignImportJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:write
      tags:
        - design_import
      x-rate-limit-per-client-user: 20
  /v1/imports/{jobId}:
    get:
      description: |-
        Gets the result of a design import job created using the [Create design import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/create-design-import-job/).

        You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
      operationId: getDesignImportJob
      parameters:
        - description: The design import job ID.
          example: f81b26fd-a33d-4c2d-9e8c-4a7aca798b17
          explode: false
          in: path
          name: jobId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressDesignImportJobExample"
                success:
                  $ref: "#/components/examples/SuccessDesignImportJobExample"
                failed:
                  $ref: "#/components/examples/FailedDesignImportJobExample"
              schema:
                $ref: "#/components/schemas/GetDesignImportJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:write
      tags:
        - design_import
      x-rate-limit-per-client-user: 120
  /v1/url-imports:
    post:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to import an external file from a URL as a new design in Canva.

        Supported file types for imports are listed in [Design imports overview](https://www.canva.dev/docs/connect/api-reference/design-imports/#supported-file-types).

        <Note>

        For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of design import jobs created with this API using the [Get URL import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/get-url-import-job/).

        </Note>
      operationId: createUrlImportJob
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateUrlImportJobRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressDesignImportJobExample"
                success:
                  $ref: "#/components/examples/SuccessDesignImportJobExample"
                failed:
                  $ref: "#/components/examples/FailedDesignImportJobExample"
              schema:
                $ref: "#/components/schemas/CreateUrlImportJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:write
      tags:
        - design_import
      x-rate-limit-per-client-user: 20
  /v1/url-imports/{jobId}:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Gets the result of a URL import job created using the [Create URL import job API](https://www.canva.dev/docs/connect/api-reference/design-imports/create-url-import-job/).

        You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
      operationId: getUrlImportJob
      parameters:
        - description: The ID of the URL import job.
          example: f81b26fd-a33d-4c2d-9e8c-4a7aca798b17
          explode: false
          in: path
          name: jobId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressDesignImportJobExample"
                success:
                  $ref: "#/components/examples/SuccessDesignImportJobExample"
                failed:
                  $ref: "#/components/examples/FailedDesignImportJobExample"
              schema:
                $ref: "#/components/schemas/GetUrlImportJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:write
      tags:
        - design_import
      x-rate-limit-per-client-user: 120
  /v1/exports:
    post:
      description: |-
        Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints) to export a file from Canva. Once the exported file is generated, you can download
        it using the URL(s) provided. The download URLs are only valid for 24 hours.

        The request requires the design ID and the exported file format type.

        Supported file formats (and export file type values): PDF (`pdf`), JPG (`jpg`), PNG (`png`), GIF (`gif`), Microsoft PowerPoint (`pptx`), and MP4 (`mp4`).

        <Note>

        For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints). You can check the status and get the results of export jobs created with this API using the [Get design export job API](https://www.canva.dev/docs/connect/api-reference/exports/get-design-export-job/).

        </Note>
      operationId: createDesignExportJob
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateDesignExportJobRequest"
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressExportJobExample"
                success:
                  $ref: "#/components/examples/SuccessExportJobExample"
                failed:
                  $ref: "#/components/examples/FailedExportJobExample"
              schema:
                $ref: "#/components/schemas/CreateDesignExportJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:read
      tags:
        - export
      x-rate-limit-per-client-user: 20
  /v1/exports/{exportId}:
    get:
      description: |-
        Gets the result of a design export job that was created using the [Create design export job API](https://www.canva.dev/docs/connect/api-reference/exports/create-design-export-job/).

        If the job is successful, the response includes an array
        of download URLs. Depending on the design type and export format, there is a download URL for each page in the design. The download URLs are only valid for 24 hours.

        You might need to make multiple requests to this endpoint until you get a `success` or `failed` status. For more information on the workflow for using asynchronous jobs, see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
      operationId: getDesignExportJob
      parameters:
        - description: The export job ID.
          explode: false
          in: path
          name: exportId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressExportJobExample"
                success:
                  $ref: "#/components/examples/SuccessExportJobExample"
                failed:
                  $ref: "#/components/examples/FailedExportJobExample"
              schema:
                $ref: "#/components/schemas/GetDesignExportJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:read
      tags:
        - export
      x-rate-limit-per-client-user: 120
  /v1/folders/{folderId}:
    delete:
      description: |-
        Deletes a folder with the specified `folderID`.
        Deleting a folder moves the user's content in the folder to the
        [Trash](https://www.canva.com/help/deleted-designs/) and content owned by
        other users is moved to the top level of the owner's
        [projects](https://www.canva.com/help/find-designs-and-folders/).
      operationId: deleteFolder
      parameters:
        - description: The folder ID.
          example: FAF2lZtloor
          explode: false
          in: path
          name: folderId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "204":
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - folder:write
      tags:
        - folder
      x-rate-limit-per-client-user: 20
    get:
      description: Gets the name and other details of a folder using a folder's `folderID`.
      operationId: getFolder
      parameters:
        - description: The folder ID.
          example: FAF2lZtloor
          explode: false
          in: path
          name: folderId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetFolderResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - folder:read
      tags:
        - folder
      x-rate-limit-per-client-user: 100
    patch:
      description: |-
        Updates a folder's details using its `folderID`.
        Currently, you can only update a folder's name.
      operationId: updateFolder
      parameters:
        - description: The folder ID.
          example: FAF2lZtloor
          explode: false
          in: path
          name: folderId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateFolderRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateFolderResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - folder:write
      tags:
        - folder
      x-rate-limit-per-client-user: 20
  /v1/folders/{folderId}/items:
    get:
      description: |-
        Lists the items in a folder, including each item's `type`.

        Folders can contain:

        - Other folders.
        - Designs, such as Instagram posts, Presentations, and Documents ([Canva Docs](https://www.canva.com/create/documents/)).
        - Image assets.

        Currently, video assets are not returned in the response.
      operationId: listFolderItems
      parameters:
        - description: The folder ID.
          example: FAF2lZtloor
          explode: false
          in: path
          name: folderId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
        - description: |-
            If the success response contains a continuation token, the folder contains more items
            you can list. You can use this token as a query parameter and retrieve more
            items from the list, for example
            `/v1/folders/{folderId}/items?continuation={continuation}`.

            To retrieve all the items in a folder, you might need to make multiple requests.
          example: RkFGMgXlsVTDbMd:MR3L0QjiaUzycIAjx0yMyuNiV0OildoiOwL0x32G4NjNu4FwtAQNxowUQNMMYN
          explode: true
          in: query
          name: continuation
          required: false
          schema:
            type: string
          style: form
        - description: |-
            Filter the folder items to only return specified types. The available types are:
            `design`, `folder`, and `image`. To filter for more than one item type, provide a comma-
            delimited list.
          explode: false
          in: query
          name: item_types
          required: false
          schema:
            items:
              $ref: "#/components/schemas/FolderItemType"
            type: array
          style: form
        - description: Sort the list of folder items.
          example: title_ascending
          explode: true
          in: query
          name: sort_by
          required: false
          schema:
            $ref: "#/components/schemas/FolderItemSortBy"
          style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListFolderItemsResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - folder:read
      tags:
        - folder
      x-rate-limit-per-client-user: 100
  /v1/folders/move:
    post:
      description: |-
        Moves an item to another folder. You must specify the folder ID of the destination folder, as well as the ID of the item you want to move.

        NOTE: In some situations, a single item can exist in multiple folders. If you attempt to move an item that exists in multiple folders, the API returns an `item_in_multiple_folders` error. In this case, you must use the Canva UI to move the item to another folder.
      operationId: moveFolderItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MoveFolderItemRequest"
      responses:
        "204":
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - folder:write
      tags:
        - folder
      x-rate-limit-per-client-user: 100
  /v1/folders:
    post:
      description: |-
        Creates a folder in either the top level of a Canva user's
        [projects](https://www.canva.com/help/find-designs-and-folders/) (using the ID `root`), or
        another folder (using the parent folder's ID). When a folder is successfully created, the
        endpoint returns its folder ID, along with other information.
      operationId: createFolder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateFolderRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateFolderResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - folder:write
      tags:
        - folder
      x-rate-limit-per-client-user: 20
  /v1/oauth/token:
    post:
      description: |-
        This endpoint implements the OAuth 2.0 `token` endpoint, as part of the Authorization Code flow with Proof Key for Code Exchange (PKCE). For more information, see [Authentication](https://www.canva.dev/docs/connect/authentication/).

        To generate an access token, you must provide one of the following:

        - An authorization code
        - A refresh token

        Generating a token using either an authorization code or a refresh token allows your integration to act on behalf of a user. You must first [obtain user authorization and get an authorization code](https://www.canva.dev/docs/connect/authentication/#obtain-user-authorization).

        Access tokens may be up to 4 KB in size, and are only valid for a specified period of time. The expiry time (currently 4 hours) is shown in the endpoint response and is subject to change.

        **Endpoint authentication**

        Requests to this endpoint require authentication with your client ID and client secret, using _one_ of the following methods:

        - **Basic access authentication** (Recommended): For [basic access authentication](https://en.wikipedia.org/wiki/Basic_access_authentication), the `{credentials}` string must be a Base64 encoded value of `{client id}:{client secret}`.
        - **Body parameters**: Provide your integration's credentials using the `client_id` and `client_secret` body parameters.

        This endpoint can't be called from a user's web-browser client because it uses client authentication with client secrets. Requests must come from your integration's backend, otherwise they'll be blocked by Canva's [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) policy.

        **Generate an access token using an authorization code**

        To generate an access token with an authorization code, you must:

        - Set `grant_type` to `authorization_code`.
        - Provide the `code_verifier` value that you generated when creating the user authorization URL.
        - Provide the authorization code you received after the user authorized the integration.

        **Generate an access token using a refresh token**

        Using the `refresh_token` value from a previous user token request, you can get a new access token with the same or smaller scope as the previous one, but with a refreshed expiry time. You will also receive a new refresh token that you can use to refresh the access token again.

        To refresh an existing access token, you must:

        - Set `grant_type` to `refresh_token`.
        - Provide the `refresh_token` from a previous token request.
      operationId: exchangeAccessToken
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: "#/components/schemas/ExchangeAccessTokenRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ExchangeAccessTokenResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OauthError"
          description: Error Response
      security:
        - {}
        - basicAuth: []
      tags:
        - oauth
  /v1/oauth/introspect:
    post:
      description: |-
        Introspect an access token to see whether it is valid and active. You can also verify some token properties, such as its claims, scopes, and validity times.

        Requests to this endpoint require authentication with your client ID and client secret, using _one_ of the following methods:

        - **Basic access authentication** (Recommended): For [basic access authentication](https://en.wikipedia.org/wiki/Basic_access_authentication), the `{credentials}` string must be a Base64 encoded value of `{client id}:{client secret}`.
        - **Body parameters**: Provide your integration's credentials using the `client_id` and `client_secret` body parameters.

        This endpoint can't be called from a user's web-browser client because it uses client authentication with client secrets. Requests must come from your integration's backend, otherwise they'll be blocked by Canva's [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) policy.
      operationId: introspectToken
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: "#/components/schemas/IntrospectTokenRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IntrospectTokenResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OauthError"
          description: Error Response
      security:
        - {}
        - basicAuth: []
      tags:
        - oauth
  /v1/oauth/revoke:
    post:
      description: |-
        Revoke an access token or a refresh token.

        If you revoke a _refresh token_, be aware that:

        - The refresh token's lineage is also revoked. This means that access tokens created from that refresh token are also revoked.
        - The user's consent for your integration is also revoked. This means that the user must go through the OAuth process again to use your integration.

        Requests to this endpoint require authentication with your client ID and client secret, using _one_ of the following methods:

        - **Basic access authentication** (Recommended): For [basic access authentication](https://en.wikipedia.org/wiki/Basic_access_authentication), the `{credentials}` string must be a Base64 encoded value of `{client id}:{client secret}`.
        - **Body parameters**: Provide your integration's credentials using the `client_id` and `client_secret` body parameters.

        This endpoint can't be called from a user's web-browser client because it uses client authentication with client secrets. Requests must come from your integration's backend, otherwise they'll be blocked by Canva's [Cross-Origin Resource Sharing (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) policy.
      operationId: revokeTokens
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: "#/components/schemas/RevokeTokensRequest"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RevokeTokensResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OauthError"
          description: Error Response
      security:
        - {}
        - basicAuth: []
      tags:
        - oauth
  /v1/resizes:
    post:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        <Note>

        To use this API, your integration must act on behalf of a user that's on a Canva plan with premium features (such as Canva Pro).

        </Note>

        Starts a new [asynchronous job](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints)
        to create a resized copy of a design. The new resized design is
        added to the top level of the user's
        [projects](https://www.canva.com/help/find-designs-and-folders/) (`root` folder).

        To resize a design into a new design, you can either:

          - Use a preset design type.
          - Set height and width dimensions for a custom design.

        NOTE: [Canva docs](https://www.canva.com/create/documents/) can't be resized, and other design types can't be resized to a Canva doc.

        <Note>
        For more information on the workflow for using asynchronous jobs,
        see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
        You can check the status and get the results of resize jobs created with this API using the
        [Get design resize job API](https://www.canva.dev/docs/connect/api-reference/resizes/get-design-resize-job/).
        </Note>
      operationId: createDesignResizeJob
      requestBody:
        content:
          application/json:
            example:
              design_id: DAGirp_1ZUA
              design_type:
                type: custom
                width: 1000
                height: 1500
            schema:
              $ref: "#/components/schemas/CreateDesignResizeJobRequest"
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressResizeJobExample"
                success:
                  $ref: "#/components/examples/SuccessResizeJobExample"
                failed:
                  $ref: "#/components/examples/FailedResizeJobExample"
              schema:
                $ref: "#/components/schemas/CreateDesignResizeJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:read
            - design:content:write
      tags:
        - resize
      x-required-capabilities:
        - resize
      x-rate-limit-per-client-user: 20
  /v1/resizes/{jobId}:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        <Note>

        To use this API, your integration must act on behalf of a user that's on a Canva plan with premium features (such as Canva Pro).

        </Note>

        Gets the result of a design resize job that was created using the [Create design resize
        job API](https://www.canva.dev/docs/connect/api-reference/resizes/create-design-resize-job/).

        If the job is successful, the response includes a summary of the new resized design, including its metadata.

        You might need to make multiple requests to this endpoint until you get a `success` or `failed` status.
        For more information on the workflow for using asynchronous jobs,
        see [API requests and responses](https://www.canva.dev/docs/connect/api-requests-responses/#asynchronous-job-endpoints).
      operationId: getDesignResizeJob
      parameters:
        - description: The design resize job ID.
          explode: false
          in: path
          name: jobId
          required: true
          schema:
            pattern: "^[a-zA-Z0-9_-]{1,50}$"
            type: string
          style: simple
      responses:
        "200":
          content:
            application/json:
              examples:
                in_progress:
                  $ref: "#/components/examples/InProgressResizeJobExample"
                success:
                  $ref: "#/components/examples/SuccessResizeJobExample"
                failed:
                  $ref: "#/components/examples/FailedResizeJobExample"
              schema:
                $ref: "#/components/schemas/GetDesignResizeJobResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - design:content:read
            - design:content:write
      tags:
        - resize
      x-required-capabilities:
        - resize
      x-rate-limit-per-client-user: 120
  /v1/users/me:
    get:
      description: |-
        Returns the User ID and Team ID of the user
        account associated with the provided access token.
      operationId: usersMe
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UsersMeResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode: []
      tags:
        - user
      x-rate-limit-per-client-user: 10
  /v1/users/me/capabilities:
    get:
      description: |-
        <Warning>

        This API is currently provided as a preview. Be aware of the following:

        - There might be unannounced breaking changes.
        - Any breaking changes to preview APIs won't produce a new [API version](https://www.canva.dev/docs/connect/versions/).
        - Public integrations that use preview APIs will not pass the review process, and can't be made available to all Canva users.

        </Warning>

        Lists the API capabilities for the user account associated with the provided access token. For more information, see [Capabilities](https://www.canva.dev/docs/connect/capabilities/).
      operationId: getUserCapabilities
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserCapabilitiesResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - profile:read
      tags:
        - user
      x-rate-limit-per-client-user: 10
  /v1/users/me/profile:
    get:
      description:
        "Currently, this returns the display name of the user account associated\
        \ with the provided access token. More user information is expected to be\
        \ included in the future."
      operationId: getUserProfile
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserProfileResponse"
          description: OK
        default:
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
          description: Error Response
      security:
        - oauthAuthCode:
            - profile:read
      tags:
        - user
      x-rate-limit-per-client-user: 10
components:
  examples:
    InProgressAssetUploadJobExample:
      summary: In progress job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: in_progress
    SuccessAssetUploadJobExample:
      summary: Successfully completed job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: success
          asset:
            id: Msd59349ff
            name: My Awesome Upload
            tags:
              - image
              - holiday
              - best day ever
            created_at: 1377396000
            updated_at: **********
            thumbnail:
              width: 595
              height: 335
              url: https://document-export.canva.com/Vczz9/zF9vzVtdADc/2/thumbnail/0001.png?<query-string>
    FailedAssetUploadJobExample:
      summary: Failed job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: failed
          error:
            code: file_too_big
            message: Failed to import because the file is too big
    InProgressAutofillJobExample:
      summary: In progress job
      value:
        job:
          id: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          status: in_progress
    SuccessAutofillJobExample:
      summary: Successfully completed job
      value:
        job:
          id: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          status: success
          result:
            type: create_design
            design:
              id: DAFVztcvd9z
              title: My summer holiday
              url: https://www.canva.com/design/DAFVztcvd9z/edit
              thumbnail:
                width: 595
                height: 335
                url: https://document-export.canva.com/Vczz9/zF9vzVtdADc/2/thumbnail/0001.png?<query-string>
    FailedAutofillJobExample:
      summary: Failed job
      value:
        job:
          id: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          status: failed
          error:
            code: autofill_error
            message: Error autofilling design from brand template
    BrandTemplateNotFoundError:
      summary:
        A brand template with the ID specified in the request could not be
        found.
      value:
        code: not_found
        message: "Brand template with id '{brandTemplateId}' not found"
    BrandTemplateForbiddenError:
      summary: |-
        The user does not have permission to access the brand template with the ID
        specified in the request.
      value:
        code: permission_denied
        message: "Not allowed to access brand template with id '{brandTemplateId}'"
    DesignTitleInvalidError:
      summary: The design title specified in the request is not valid.
      value:
        code: bad_request_params
        message: Design title invalid
    DesignAutofillJobNotFoundError:
      summary:
        A design autofill job with the ID specified in the request could not
        be found.
      value:
        code: not_found
        message: "Job {jobId} not found"
    DesignAutofillJobForbiddenError:
      summary: |-
        The user does not have permission to access the design autofill job with the ID
        specified in the request.
      value:
        code: permission_denied
        message: Calling user was not the creator of the autofill job
    InProgressDesignImportJobExample:
      summary: In progress job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: in_progress
    SuccessDesignImportJobExample:
      summary: Successfully completed job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: success
          result:
            designs:
              - id: DAGQm2AkzOk
                title: My Awesome Design
                thumbnail:
                  width: 376
                  height: 531
                  url: https://document-export.canva.com/...
                urls:
                  edit_url: https://www.canva.com/api/design/...
                  view_url: https://www.canva.com/api/design/...
                created_at: 1726198998
                updated_at: 1726199000
    FailedDesignImportJobExample:
      summary: Failed job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: failed
          error:
            code: invalid_file
            message: Document couldn't be imported because the file is corrupt.
    InProgressExportJobExample:
      summary: In progress job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: in_progress
    SuccessExportJobExample:
      summary: Successfully completed job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: success
          urls:
            - https://export-download.canva.com/...
    FailedExportJobExample:
      summary: Failed job
      value:
        job:
          id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          status: failed
          error:
            code: license_required
            message: User doesn't have the required license to export in PRO quality.
    UpdateFolderPermissionsUserInputExample:
      description:
        "Example of an update folder permissions request, updating the\
        \ permissions for a user."
      summary: Update user permissions request
      value:
        role: editor
        type: user
        user_id: auDAbliZ2rQNNOsUl5OLu
        team_id: Oi2RJILTrKk0KRhRUZozX
    UpdateFolderPermissionsGroupInputExample:
      description:
        "Example of an update folder permissions request, updating the\
        \ permissions for a group."
      summary: Update group permissions request
      value:
        role: view
        type: group
        group_id: dl9n9SoWoExMsw6Ri1iTg
    UpdateFolderPermissionsTeamInputExample:
      description:
        "Example of an update folder permissions request, updating the\
        \ permissions for a team."
      summary: Updating team permissions request
      value:
        role: none
        type: team
        team_id: Oi2RJILTrKk0KRhRUZozX
    UpdateFolderPermissionsUserResponseExample:
      description:
        Example of the response returned when updating folder permissions
        for a user.
      summary: Updated user permissions response
      value:
        folder_permission:
          type: user
          role: editor
          inherited: false
          team_user:
            user_id: auDAbliZ2rQNNOsUl5OLu
            team_id: Oi2RJILTrKk0KRhRUZozX
    UpdateFolderPermissionsGroupResponseExample:
      description:
        Example of the response returned when updating folder permissions
        for a group.
      summary: Updated group permissions response
      value:
        role: view
        type: group
        inherited: false
        group:
          id: dl9n9SoWoExMsw6Ri1iTg
          external: false
          display_name: Sales team
    UpdateFolderPermissionsTeamResponseExample:
      description:
        Example of the response returned when updating folder permissions
        for a team.
      summary: Updated team permissions response
      value:
        role: none
        type: team
        inherited: false
        team:
          id: Oi2RJILTrKk0KRhRUZozX
          display_name: Acme Corporation
          external: false
    InProgressResizeJobExample:
      summary: In progress job
      value:
        job:
          id: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          status: in_progress
    SuccessResizeJobExample:
      summary: Successfully completed job
      value:
        job:
          id: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          status: success
          result:
            design:
              id: DAGirp_1ZUA
              title: My summer holiday
              thumbnail:
                url: https://document-export.canva.com/Vczz9/zF9vzVtdADc/2/thumbnail/0001.png?<query-string>
              urls:
                edit_url: https://www.canva.com/design/DAGhRehVa2c/0L_1s4UXSpZhls8EtPaRKw/edit
                view_url: https://www.canva.com/design/DAGhRehVa2c/0L_1s4UXSpZhls8EtPaRKw/view
              created_at: "1742856750,"
              updated_at: "1742856752,"
              page_count: 5
    FailedResizeJobExample:
      summary: Failed job
      value:
        job:
          id: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          status: failed
          error:
            code: design_resize_error
            message: Failed to resize the design
  parameters:
    brandTemplateId:
      description: The brand template ID.
      explode: false
      in: path
      name: brandTemplateId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
    commentId:
      description: The ID of the comment.
      example: KeAZEAjijEb
      explode: false
      in: path
      name: commentId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
    threadId:
      description: The ID of the thread.
      example: KeAbiEAjZEj
      explode: false
      in: path
      name: threadId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
    replyId:
      description: The ID of the reply.
      example: KeAZEAjijEb
      explode: false
      in: path
      name: replyId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
    designId:
      description: The design ID.
      explode: false
      in: path
      name: designId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
    exportId:
      description: The export job ID.
      explode: false
      in: path
      name: exportId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
    folderIdParameter:
      description: The folder ID.
      example: FAF2lZtloor
      explode: false
      in: path
      name: folderId
      required: true
      schema:
        pattern: "^[a-zA-Z0-9_-]{1,50}$"
        type: string
      style: simple
  schemas:
    GetAppJwksResponse:
      properties:
        keys:
          description: |-
            The value of the "keys" parameter is an array of JWK values. The order of keys has no
            meaning.
          items:
            $ref: "#/components/schemas/JsonWebKey"
          type: array
      required:
        - keys
      type: object
    JsonWebKey:
      description: |-
        Standard Json Web Key specification following https://www.rfc-editor.org/rfc/rfc7517 and
        https://www.rfc-editor.org/rfc/rfc7518.html.
      properties:
        alg:
          description: |-
            The "alg" (algorithm) parameter identifies the algorithm intended for
            use with the key.
            See https://www.rfc-editor.org/rfc/rfc7517#section-4
          type: string
        kid:
          description: |-
            The "kid" (key ID) parameter is used to match a specific key.  This
            is used, for instance, to choose among a set of keys within a JWK Set
            during key rollover. When "kid" values are used within a JWK Set,
            different keys within the JWK Set SHOULD use distinct "kid" values.
            The "kid" value is a case-sensitive string.
            See https://www.rfc-editor.org/rfc/rfc7517#section-4
          type: string
        kty:
          description: |-
            The "kty" (key type) parameter identifies the cryptographic algorithm
            family used with the key, such as "RSA" or "EC". The "kty" value is a
            case-sensitive string. At the moment, only "RSA" is supported.
            See https://www.rfc-editor.org/rfc/rfc7517#section-4
          type: string
        "n":
          description: |-
            The "n" (modulus) parameter contains the modulus value for the RSA
               public key.  It is represented as a Base64urlUInt-encoded value.
            See https://www.rfc-editor.org/rfc/rfc7518.html#section-6.3
          type: string
        e:
          description: |-
            The "e" (exponent) parameter contains the exponent value for the RSA
               public key.  It is represented as a Base64urlUInt-encoded value.
            See https://www.rfc-editor.org/rfc/rfc7518.html#section-6.3
          type: string
        use:
          description: |-
            The "use" (public key use) parameter identifies the intended use of
            the public key. The "use" parameter is employed to indicate whether
            a public key is used for encrypting data or verifying the signature
            on data. Values are commonly "sig" (signature) or "enc" (encryption).
            See https://www.rfc-editor.org/rfc/rfc7517#section-4
          type: string
      required:
        - e
        - kid
        - kty
        - "n"
      type: object
    GetAssetResponse:
      properties:
        asset:
          $ref: "#/components/schemas/Asset"
      required:
        - asset
      type: object
    UpdateAssetRequest:
      properties:
        name:
          description: |-
            The name of the asset. This is shown in the Canva UI.
            When this field is undefined or empty, nothing is updated.
          example: My Awesome Upload
          maxLength: 50
          type: string
        tags:
          description: |-
            The replacement tags for the asset.
            When this field is undefined, nothing is updated.
          example:
            - image
            - holiday
            - best day ever
          items:
            maxLength: 50
            type: string
          maxItems: 50
          type: array
      type: object
    UpdateAssetResponse:
      properties:
        asset:
          $ref: "#/components/schemas/Asset"
      required:
        - asset
      type: object
    Asset:
      description: "The asset object, which contains metadata about the asset."
      properties:
        type:
          $ref: "#/components/schemas/AssetType"
        id:
          description: The ID of the asset.
          example: Msd59349ff
          type: string
        name:
          description: The name of the asset.
          example: My Awesome Upload
          type: string
        tags:
          description: |-
            The user-facing tags attached to the asset.
            Users can add these tags to their uploaded assets, and they can search their uploaded
            assets in the Canva UI by searching for these tags. For information on how users use
            tags, see the
            [Canva Help Center page on asset tags](https://www.canva.com/help/add-edit-tags/).
          example:
            - image
            - holiday
            - best day ever
          items:
            type: string
          type: array
        import_status:
          $ref: "#/components/schemas/ImportStatus"
        created_at:
          description: |-
            When the asset was added to Canva, as a Unix timestamp (in seconds since the Unix
            Epoch).
          example: 1377396000
          format: int64
          type: integer
        updated_at:
          description: |-
            When the asset was last updated in Canva, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: **********
          format: int64
          type: integer
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
      required:
        - created_at
        - id
        - name
        - tags
        - type
        - updated_at
      type: object
    AssetSummary:
      description: An object representing an asset with associated metadata.
      properties:
        type:
          $ref: "#/components/schemas/AssetType"
        id:
          description: The ID of the asset.
          example: Msd59349ff
          type: string
        name:
          description: The name of the asset.
          example: My Awesome Upload
          type: string
        tags:
          description: |-
            The user-facing tags attached to the asset.
            Users can add these tags to their uploaded assets, and they can search their uploaded
            assets in the Canva UI by searching for these tags. For information on how users use
            tags, see the
            [Canva Help Center page on asset tags](https://www.canva.com/help/add-edit-tags/).
          example:
            - image
            - holiday
            - best day ever
          items:
            type: string
          type: array
        created_at:
          description: |-
            When the asset was added to Canva, as a Unix timestamp (in seconds since the Unix
            Epoch).
          example: 1377396000
          format: int64
          type: integer
        updated_at:
          description: |-
            When the asset was last updated in Canva, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: **********
          format: int64
          type: integer
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
      required:
        - created_at
        - id
        - name
        - tags
        - type
        - updated_at
      type: object
    AssetType:
      description:
        "Type of an asset. Support for `video` assets is currently provided\
        \ as a [preview](https://www.canva.dev/docs/connect/api-reference/assets/#videos)."
      enum:
        - image
        - video
      example: image
      type: string
    ImportStatus:
      deprecated: true
      description: The import status of the asset.
      properties:
        state:
          $ref: "#/components/schemas/ImportStatusState"
        error:
          $ref: "#/components/schemas/ImportError"
      required:
        - state
      type: object
    ImportStatusState:
      deprecated: true
      description: State of the import job for an uploaded asset.
      enum:
        - failed
        - in_progress
        - success
      example: success
      type: string
    ImportError:
      deprecated: true
      description: "If the import fails, this object provides details about the error."
      properties:
        code:
          $ref: "#/components/schemas/ImportErrorCode"
        message:
          description: A human-readable description of what went wrong.
          type: string
      required:
        - code
        - message
      type: object
    ImportErrorCode:
      deprecated: true
      description: |
        A short string indicating why the upload failed. This field can be used to handle errors programmatically.
      enum:
        - file_too_big
        - import_failed
      type: string
    CreateAssetUploadJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/AssetUploadJob"
      required:
        - job
      type: object
    GetAssetUploadJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/AssetUploadJob"
      required:
        - job
      type: object
    AssetUploadJob:
      description: The status of the asset upload job.
      example:
        id: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
        status: success
        asset:
          id: Msd59349ff
          name: My Awesome Upload
          tags:
            - image
            - holiday
            - best day ever
          created_at: 1377396000
          updated_at: **********
          thumbnail:
            width: 595
            height: 335
            url: https://document-export.canva.com/Vczz9/zF9vzVtdADc/2/thumbnail/0001.png?<query-string>
      properties:
        id:
          description: The ID of the asset upload job.
          example: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          type: string
        status:
          $ref: "#/components/schemas/AssetUploadStatus"
        error:
          $ref: "#/components/schemas/AssetUploadError"
        asset:
          $ref: "#/components/schemas/Asset"
      required:
        - id
        - status
      type: object
    AssetUploadStatus:
      description: Status of the asset upload job.
      enum:
        - failed
        - in_progress
        - success
      example: success
      type: string
    AssetUploadError:
      description: "If the upload fails, this object provides details about the error."
      properties:
        code:
          $ref: "#/components/schemas/AssetUploadErrorCode"
        message:
          description: A human-readable description of what went wrong.
          example: Failed to import because the file is too big.
          type: string
      required:
        - code
        - message
      type: object
    AssetUploadErrorCode:
      description: |-
        A short string indicating why the upload failed. This field can be used to handle errors
        programmatically.
      enum:
        - file_too_big
        - import_failed
        - fetch_failed
      example: file_too_big
      type: string
    AssetUploadMetadata:
      description: Metadata for the asset being uploaded.
      properties:
        name_base64:
          description: |-
            The asset's name, encoded in Base64.

            The maximum length of an asset name in Canva (unencoded) is 50 characters.

            Base64 encoding allows names containing emojis and other special
            characters to be sent using HTTP headers.
            For example, "My Awesome Upload 🚀" Base64 encoded
            is `TXkgQXdlc29tZSBVcGxvYWQg8J+agA==`.
          example: TXkgQXdlc29tZSBVcGxvYWQg8J+agA==
          minLength: 1
          type: string
      required:
        - name_base64
      type: object
    CreateDesignAutofillJobRequest:
      properties:
        brand_template_id:
          description: ID of the input brand template.
          example: DAFVztcvd9z
          type: string
        title:
          description: |-
            Title to use for the autofilled design.

            If no design title is provided, the autofilled design will have the same title as the brand template.
          maxLength: 255
          minLength: 1
          type: string
        data:
          additionalProperties:
            $ref: "#/components/schemas/DatasetValue"
          description: Data object containing the data fields and values to autofill.
          example:
            cute_pet_image_of_the_day:
              type: image
              asset_id: Msd59349ff
            cute_pet_witty_pet_says:
              type: text
              text: It was like this when I got here!
            cute_pet_sales_chart:
              type: chart
              chart_data:
                rows:
                  - cells:
                      - type: string
                        value: Geographic Region
                      - type: string
                        value: Sales (millions AUD)
                      - type: string
                        value: Target met?
                      - type: string
                        value: Date met
                  - cells:
                      - type: string
                        value: Asia Pacific
                      - type: number
                        value: 10.2
                      - type: boolean
                        value: true
                      - type: date
                        value: 1721944387
                  - cells:
                      - type: string
                        value: EMEA
                      - type: number
                        value: 13.8
                      - type: boolean
                        value: false
                      - type: date
          type: object
      required:
        - brand_template_id
        - data
      type: object
    CreateDesignAutofillJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignAutofillJob"
      required:
        - job
      type: object
    DatasetValue:
      description: The data field to autofill.
      discriminator:
        mapping:
          image: "#/components/schemas/DatasetImageValue"
          text: "#/components/schemas/DatasetTextValue"
          chart: "#/components/schemas/DatasetChartValue"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/DatasetImageValue"
        - $ref: "#/components/schemas/DatasetTextValue"
        - $ref: "#/components/schemas/DatasetChartValue"
      type: object
    Dataset:
      additionalProperties:
        $ref: "#/components/schemas/DatasetValue"
      description: Data object containing the data fields and values to autofill.
      example:
        cute_pet_image_of_the_day:
          type: image
          asset_id: Msd59349ff
        cute_pet_witty_pet_says:
          type: text
          text: It was like this when I got here!
        cute_pet_sales_chart:
          type: chart
          chart_data:
            rows:
              - cells:
                  - type: string
                    value: Geographic Region
                  - type: string
                    value: Sales (millions AUD)
                  - type: string
                    value: Target met?
                  - type: string
                    value: Date met
              - cells:
                  - type: string
                    value: Asia Pacific
                  - type: number
                    value: 10.2
                  - type: boolean
                    value: true
                  - type: date
                    value: 1721944387
              - cells:
                  - type: string
                    value: EMEA
                  - type: number
                    value: 13.8
                  - type: boolean
                    value: false
                  - type: date
      type: object
    DatasetImageValue:
      description: If the data field is an image field.
      properties:
        type:
          enum:
            - image
          type: string
        asset_id:
          description: "`asset_id` of the image to insert into the template element."
          example: Msd59349ff
          type: string
      required:
        - asset_id
        - type
      type: object
    DatasetTextValue:
      description: If the data field is a text field.
      properties:
        type:
          enum:
            - text
          type: string
        text:
          description: Text to insert into the template element.
          example: It was like this when I got here!
          type: string
      required:
        - text
        - type
      type: object
    DatasetChartValue:
      description: |-
        If the data field is a chart.

         WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
      properties:
        type:
          enum:
            - chart
          type: string
        chart_data:
          $ref: "#/components/schemas/DataTable"
      required:
        - chart_data
        - type
      type: object
    GetDesignAutofillJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignAutofillJob"
      required:
        - job
      type: object
    DesignAutofillJob:
      description: Details about the autofill job.
      properties:
        id:
          description:
            ID of the asynchronous job that is creating the design using
            the provided data.
          example: 450a76e7-f96f-43ae-9c37-0e1ce492ac72
          type: string
        status:
          $ref: "#/components/schemas/DesignAutofillStatus"
        result:
          $ref: "#/components/schemas/DesignAutofillJobResult"
        error:
          $ref: "#/components/schemas/AutofillError"
      required:
        - id
        - status
      type: object
    DesignAutofillJobResult:
      description:
        Result of the design autofill job. Only present if job status is
        `success`.
      discriminator:
        mapping:
          create_design: "#/components/schemas/CreateDesignAutofillJobResult"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/CreateDesignAutofillJobResult"
      type: object
    CreateDesignAutofillJobResult:
      description: Design has been created and saved to user's root folder.
      properties:
        type:
          enum:
            - create_design
          type: string
        design:
          $ref: "#/components/schemas/DesignSummary"
      required:
        - design
        - type
      type: object
    DesignAutofillStatus:
      description: Status of the design autofill job.
      enum:
        - in_progress
        - success
        - failed
      example: success
      type: string
    AutofillErrorCode:
      enum:
        - autofill_error
        - thumbnail_generation_error
        - create_design_error
      type: string
    AutofillError:
      description:
        "If the autofill job fails, this object provides details about\
        \ the error."
      properties:
        code:
          $ref: "#/components/schemas/AutofillErrorCode"
        message:
          description: A human-readable description of what went wrong.
          type: string
      required:
        - code
        - message
      type: object
    DatasetFilter:
      default: any
      enum:
        - any
        - non_empty
        - empty
      type: string
      x-enum-descriptions:
        - Brand templates with and without dataset definitions.
        - Brand templates with one or more data fields defined.
        - Brand templates with no data fields defined.
    ListBrandTemplatesResponse:
      properties:
        continuation:
          description: |-
            If the success response contains a continuation token, the user has access to more
            brand templates you can list. You can use this token as a query parameter and retrieve
            more templates from the list, for example
            `/v1/brand-templates?continuation={continuation}`.
            To retrieve all the brand templates available to the user, you might need to make
            multiple requests.
          example: RkFGMgXlsVTDbMd:MR3L0QjiaUzycIAjx0yMyuNiV0OildoiOwL0x32G4NjNu4FwtAQNxowUQNMMYN
          type: string
        items:
          description: The list of brand templates.
          items:
            $ref: "#/components/schemas/BrandTemplate"
          type: array
      required:
        - items
      type: object
    GetBrandTemplateResponse:
      description: Successful response from a `getBrandTemplate` request.
      properties:
        brand_template:
          $ref: "#/components/schemas/BrandTemplate"
      required:
        - brand_template
      type: object
    BrandTemplate:
      description: An object representing a brand template with associated metadata.
      properties:
        id:
          description: The brand template ID.
          example: DEMzWSwy3BI
          type: string
        title:
          description: "The brand template title, as shown in the Canva UI."
          example: Advertisement Template
          type: string
        view_url:
          description: A URL Canva users can visit to view the brand template.
          example: https://www.canva.com/design/DAE35hE8FA4/view
          type: string
        create_url:
          description:
            A URL Canva users can visit to create a new design from the
            template.
          example: https://www.canva.com/design/DAE35hE8FA4/remix
          type: string
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
        created_at:
          description: |-
            When the brand template was created, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1704110400
          format: int64
          type: integer
        updated_at:
          description: |-
            When the brand template was last updated, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1719835200
          format: int64
          type: integer
      required:
        - create_url
        - created_at
        - id
        - title
        - updated_at
        - view_url
      type: object
    GetBrandTemplateDatasetResponse:
      description: Successful response from a `getBrandTemplateDataset` request.
      properties:
        dataset:
          additionalProperties:
            $ref: "#/components/schemas/DataField"
          description: |-
            The dataset definition for the brand template. The dataset definition contains the data inputs available for use with the
            [Create design autofill job API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).
          example:
            cute_pet_image_of_the_day:
              type: image
            cute_pet_witty_pet_says:
              type: text
            cute_pet_sales_chart:
              type: chart
          type: object
      type: object
    DatasetDefinition:
      additionalProperties:
        $ref: "#/components/schemas/DataField"
      description: |-
        The dataset definition for the brand template. The dataset definition contains the data inputs available for use with the
        [Create design autofill job API](https://www.canva.dev/docs/connect/api-reference/autofills/create-design-autofill-job/).
      example:
        cute_pet_image_of_the_day:
          type: image
        cute_pet_witty_pet_says:
          type: text
        cute_pet_sales_chart:
          type: chart
      type: object
    DataField:
      description: A named data field that can be autofilled in the brand template.
      discriminator:
        mapping:
          image: "#/components/schemas/ImageDataField"
          text: "#/components/schemas/TextDataField"
          chart: "#/components/schemas/ChartDataField"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/ImageDataField"
        - $ref: "#/components/schemas/TextDataField"
        - $ref: "#/components/schemas/ChartDataField"
      type: object
    ImageDataField:
      description:
        An image for a brand template. You can autofill the brand template
        with an image by providing its `asset_id`.
      properties:
        type:
          enum:
            - image
          type: string
      required:
        - type
      type: object
    TextDataField:
      description:
        Some text for a brand template. You can autofill the brand template
        with this value.
      properties:
        type:
          enum:
            - text
          type: string
      required:
        - type
      type: object
    ChartDataField:
      description: |-
        Chart data for a brand template. You can autofill the brand template with tabular data.

        WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
      properties:
        type:
          enum:
            - chart
          type: string
      required:
        - type
      type: object
    Capability:
      description: |-
        Some APIs are annotated with required capabilities. These endpoints require the user to
        possess the required capabilities in order to be called successfully.
      enum:
        - autofill
        - brand_template
        - resize
      type: string
      x-enum-descriptions:
        - "Capability required to call autofill APIs Users that are members of a [Canva\
          \ Enterprise](https://www.canva.com/enterprise/) organization have this capability."
        - "Capability required to use brand template APIs. Users that are members of\
          \ a [Canva Enterprise](https://www.canva.com/enterprise/) organization have\
          \ this capability."
        - Capability required to create design resize jobs. Users on a Canva plan with
          premium features (such as Canva Pro) have this capability.
    CreateCommentRequest:
      properties:
        attached_to:
          $ref: "#/components/schemas/CommentObjectInput"
        message:
          description: |-
            The comment message. This is the comment body shown in the Canva UI.

            You can also mention users in your message by specifying their User ID and Team ID
            using the format `[user_id:team_id]`. If the `assignee_id` parameter is specified, you
            must mention the assignee in the message.
          example: "Great work [oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP]!"
          type: string
        assignee_id:
          description: |-
            Lets you assign the comment to a Canva user using their User ID. You _must_ mention the
            assigned user in the `message`.
          example: oUnPjZ2k2yuhftbWF7873o
          type: string
      required:
        - attached_to
        - message
      type: object
    CreateReplyRequest:
      properties:
        attached_to:
          $ref: "#/components/schemas/CommentObjectInput"
        message:
          description: |-
            The reply comment message. This is the reply comment body shown in the Canva UI.

            You can also mention users in your message by specifying their User ID and Team ID
            using the format `[user_id:team_id]`.
          example: Thanks!
          type: string
      required:
        - attached_to
        - message
      type: object
    CreateReplyV2Request:
      properties:
        message_plaintext:
          description: |-
            The comment message of the reply in plaintext. This is the reply comment shown in the Canva UI.

            You can also mention users in your message by specifying their User ID and Team ID
            using the format `[user_id:team_id]`.
          example: Thanks!
          type: string
      required:
        - message_plaintext
      type: object
    CreateCommentResponse:
      properties:
        comment:
          $ref: "#/components/schemas/ParentComment"
      required:
        - comment
      type: object
    CreateThreadResponse:
      properties:
        thread:
          $ref: "#/components/schemas/Thread"
      required:
        - thread
      type: object
    CreateReplyResponse:
      properties:
        comment:
          $ref: "#/components/schemas/ReplyComment"
      required:
        - comment
      type: object
    CreateThreadRequest:
      properties:
        message_plaintext:
          description: |-
            The comment message in plaintext. This is the comment body shown in the Canva UI.

            You can also mention users in your message by specifying their User ID and Team ID
            using the format `[user_id:team_id]`. If the `assignee_id` parameter is specified, you
            must mention the assignee in the message.
          example: "Great work [oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP]!"
          type: string
        assignee_id:
          description: |-
            Lets you assign the comment to a Canva user using their User ID. You _must_ mention the
            assigned user in the `message`.
          example: oUnPjZ2k2yuhftbWF7873o
          type: string
      required:
        - message_plaintext
      type: object
    CreateReplyV2Response:
      properties:
        reply:
          $ref: "#/components/schemas/Reply"
      required:
        - reply
      type: object
    GetThreadResponse:
      description: |-
        Successful response from a `getThread` request.

        The `comment` property is deprecated.
        For details of a comment thread, please use the `thread` property.
      properties:
        comment:
          $ref: "#/components/schemas/Comment"
        thread:
          $ref: "#/components/schemas/Thread"
      type: object
    GetReplyResponse:
      description: Successful response from a `getReply` request.
      properties:
        reply:
          $ref: "#/components/schemas/Reply"
      required:
        - reply
      type: object
    Comment:
      deprecated: true
      description: |-
        The comment object, which contains metadata about the comment.
        Deprecated in favor of the new `thread` object.
      discriminator:
        mapping:
          parent: "#/components/schemas/ParentComment"
          reply: "#/components/schemas/ReplyComment"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/ParentComment"
        - $ref: "#/components/schemas/ReplyComment"
      type: object
    ParentComment:
      deprecated: true
      description: |-
        Data about the comment, including the message, author, and
        the object (such as a design) the comment is attached to.
      properties:
        type:
          enum:
            - parent
          type: string
        id:
          description: |-
            The ID of the comment.

            You can use this ID to create replies to the comment using the [Create reply API](https://www.canva.dev/docs/connect/api-reference/comments/create-reply/).
          example: KeAbiEAjZEj
          type: string
        attached_to:
          $ref: "#/components/schemas/CommentObject"
        message:
          description: |-
            The comment message. This is the comment body shown in the Canva UI.
            User mentions are shown here in the format `[user_id:team_id]`.
          example: "Great work [oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP]!"
          type: string
        author:
          $ref: "#/components/schemas/User"
        created_at:
          description: |-
            When the comment or reply was created, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: **********
          format: int64
          type: integer
        updated_at:
          description: |-
            When the comment or reply was last updated, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1692928900
          format: int64
          type: integer
        mentions:
          additionalProperties:
            $ref: "#/components/schemas/TeamUser"
          deprecated: true
          description: The Canva users mentioned in the comment.
          example:
            oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP:
              user_id: oUnPjZ2k2yuhftbWF7873o
              team_id: oBpVhLW22VrqtwKgaayRbP
              display_name: John Doe
          type: object
        assignee:
          $ref: "#/components/schemas/User"
        resolver:
          $ref: "#/components/schemas/User"
      required:
        - author
        - id
        - mentions
        - message
        - type
      type: object
    ReplyComment:
      deprecated: true
      description: |-
        Data about the reply comment, including the message, author, and
        the object (such as a design) the comment is attached to.
      properties:
        type:
          enum:
            - reply
          type: string
        id:
          description: The ID of the comment.
          example: KeAZEAjijEb
          type: string
        attached_to:
          $ref: "#/components/schemas/CommentObject"
        message:
          description: |-
            The comment message. This is the comment body shown in the Canva UI.
            User mentions are shown here in the format `[user_id:team_id]`.
          example: Thanks!
          type: string
        author:
          $ref: "#/components/schemas/User"
        created_at:
          description: |-
            When the comment or reply was created, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1692929800
          format: int64
          type: integer
        updated_at:
          description: |-
            When the comment or reply was last updated, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1692929900
          format: int64
          type: integer
        mentions:
          additionalProperties:
            $ref: "#/components/schemas/TeamUser"
          deprecated: true
          description: The Canva users mentioned in the comment.
          example:
            oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP:
              user_id: oUnPjZ2k2yuhftbWF7873o
              team_id: oBpVhLW22VrqtwKgaayRbP
              display_name: John Doe
          type: object
        thread_id:
          description: |-
            The ID of the comment thread this reply is in. This ID is the same as the `id` of the
            parent comment.
          example: KeAbiEAjZEj
          type: string
      required:
        - author
        - id
        - mentions
        - message
        - thread_id
        - type
      type: object
    CommentObject:
      deprecated: true
      description:
        Identifying information about the object (such as a design) that
        the comment is attached to.
      discriminator:
        mapping:
          design: "#/components/schemas/DesignCommentObject"
        propertyName: type
      example:
        design_id: DAFVztcvd9z
        type: design
      oneOf:
        - $ref: "#/components/schemas/DesignCommentObject"
      type: object
    DesignCommentObject:
      deprecated: true
      description: If the comment is attached to a Canva Design.
      properties:
        type:
          enum:
            - design
          type: string
        design_id:
          description: The ID of the design this comment is attached to.
          example: DAFVztcvd9z
          type: string
      required:
        - design_id
        - type
      type: object
    CommentObjectInput:
      description: |-
        An object containing identifying information for the design or other object you want to
        attach the comment to.
      discriminator:
        mapping:
          design: "#/components/schemas/DesignCommentObjectInput"
        propertyName: type
      example:
        design_id: DAFVztcvd9z
        type: design
      oneOf:
        - $ref: "#/components/schemas/DesignCommentObjectInput"
      type: object
    DesignCommentObjectInput:
      description: If the comment is attached to a Canva Design.
      properties:
        type:
          enum:
            - design
          type: string
        design_id:
          description: The ID of the design you want to attach this comment to.
          example: DAFVztcvd9z
          type: string
      required:
        - design_id
        - type
      type: object
    CommentEventDeprecated:
      deprecated: true
      description: |-
        Basic details about the comment.

        The `comment` property is deprecated.
        For details of the comment event, use the `comment_event` property instead.
      properties:
        type:
          $ref: "#/components/schemas/CommentEventTypeEnum"
        data:
          $ref: "#/components/schemas/Comment"
      required:
        - data
        - type
      type: object
    Mentions:
      additionalProperties:
        $ref: "#/components/schemas/TeamUser"
      deprecated: true
      description: The Canva users mentioned in the comment.
      example:
        oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP:
          user_id: oUnPjZ2k2yuhftbWF7873o
          team_id: oBpVhLW22VrqtwKgaayRbP
          display_name: John Doe
      type: object
    CommentEventTypeEnum:
      deprecated: true
      description: The type of comment event.
      enum:
        - comment
        - reply
        - mention
        - assign
        - resolve
      type: string
    CommentEvent:
      description:
        "The type of comment event, including additional type-specific\
        \ properties."
      discriminator:
        mapping:
          new: "#/components/schemas/NewCommentEvent"
          assigned: "#/components/schemas/AssignedCommentEvent"
          resolved: "#/components/schemas/ResolvedCommentEvent"
          reply: "#/components/schemas/ReplyCommentEvent"
          mention: "#/components/schemas/MentionCommentEvent"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/NewCommentEvent"
        - $ref: "#/components/schemas/AssignedCommentEvent"
        - $ref: "#/components/schemas/ResolvedCommentEvent"
        - $ref: "#/components/schemas/ReplyCommentEvent"
        - $ref: "#/components/schemas/MentionCommentEvent"
      type: object
    NewCommentEvent:
      description: Event type for a new comment thread.
      properties:
        type:
          enum:
            - new
          type: string
        comment_url:
          description: "A URL to the design, focused on the comment thread."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        comment:
          $ref: "#/components/schemas/Thread"
      required:
        - comment
        - comment_url
        - type
      type: object
    AssignedCommentEvent:
      description: Event type for a comment thread that has been assigned.
      properties:
        type:
          enum:
            - assigned
          type: string
        comment_url:
          description: "A URL to the design, focused on the comment thread."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        comment:
          $ref: "#/components/schemas/Thread"
      required:
        - comment
        - comment_url
        - type
      type: object
    ResolvedCommentEvent:
      description: Event type for a comment thread that has been resolved.
      properties:
        type:
          enum:
            - resolved
          type: string
        comment_url:
          description: "A URL to the design, focused on the comment thread."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        comment:
          $ref: "#/components/schemas/Thread"
      required:
        - comment
        - comment_url
        - type
      type: object
    ReplyCommentEvent:
      description: Event type for a reply to a comment thread.
      properties:
        type:
          enum:
            - reply
          type: string
        reply_url:
          description: "A URL to the design, focused on the comment reply."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        reply:
          $ref: "#/components/schemas/Reply"
      required:
        - reply
        - reply_url
        - type
      type: object
    MentionCommentEvent:
      description: Event type for a mention in a comment thread or reply.
      properties:
        type:
          enum:
            - mention
          type: string
        content:
          $ref: "#/components/schemas/MentionEventContent"
      required:
        - content
        - type
      type: object
    MentionEventContent:
      description:
        "The type of mention event content, along with additional type-specific\
        \ properties."
      discriminator:
        mapping:
          thread: "#/components/schemas/ThreadMentionEventContent"
          reply: "#/components/schemas/ReplyMentionEventContent"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/ThreadMentionEventContent"
        - $ref: "#/components/schemas/ReplyMentionEventContent"
      type: object
    ThreadMentionEventContent:
      description: Content for a mention in a comment thread.
      properties:
        type:
          enum:
            - thread
          type: string
        comment_url:
          description: "A URL to the design, focused on the comment thread."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        comment:
          $ref: "#/components/schemas/Thread"
      required:
        - comment
        - comment_url
        - type
      type: object
    ReplyMentionEventContent:
      description: Content for a mention in a comment reply.
      properties:
        type:
          enum:
            - reply
          type: string
        reply_url:
          description: "A URL to the design, focused on the comment reply."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        reply:
          $ref: "#/components/schemas/Reply"
      required:
        - reply
        - reply_url
        - type
      type: object
    ListRepliesResponse:
      description: Successful response from a `listReplies` request.
      properties:
        continuation:
          description: |-
            If the success response contains a continuation token, the list contains more items
            you can list. You can use this token as a query parameter and retrieve more items
            from the list, for example `?continuation={continuation}`.

            To retrieve all items, you might need to make multiple requests.
          example: RkFGMgXlsVTDbMd:MR3L0QjiaUzycIAjx0yMyuNiV0OildoiOwL0x32G4NjNu4FwtAQNxowUQNMMYN
          type: string
        items:
          items:
            $ref: "#/components/schemas/Reply"
          type: array
      required:
        - items
      type: object
    Thread:
      description: |-
        A discussion thread on a design.

        The `type` of the thread can be found in the `thread_type` object, along with additional type-specific properties.
        The `author` of the thread might be missing if that user account no longer exists.
      properties:
        id:
          description: |-
            The ID of the thread.

            You can use this ID to create replies to the thread using the [Create reply API](https://www.canva.dev/docs/connect/api-reference/comments/create-reply/).
          example: KeAbiEAjZEj
          type: string
        design_id:
          description: The ID of the design that the discussion thread is on.
          example: DAFVztcvd9z
          type: string
        thread_type:
          $ref: "#/components/schemas/ThreadType"
        author:
          $ref: "#/components/schemas/User"
        created_at:
          description: |-
            When the thread was created, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: **********
          format: int64
          type: integer
        updated_at:
          description: |-
            When the thread was last updated, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1692928900
          format: int64
          type: integer
      required:
        - created_at
        - design_id
        - id
        - thread_type
        - updated_at
      type: object
    ThreadType:
      description:
        "The type of the discussion thread, along with additional type-specific\
        \ properties."
      discriminator:
        mapping:
          comment: "#/components/schemas/CommentThreadType"
          suggestion: "#/components/schemas/SuggestionThreadType"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/CommentThreadType"
        - $ref: "#/components/schemas/SuggestionThreadType"
      type: object
    CommentThreadType:
      description: A comment thread.
      properties:
        type:
          enum:
            - comment
          type: string
        content:
          $ref: "#/components/schemas/CommentContent"
        mentions:
          additionalProperties:
            $ref: "#/components/schemas/UserMention"
          description: The Canva users mentioned in the comment thread or reply.
          example:
            oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP:
              tag: oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP
              user:
                user_id: oUnPjZ2k2yuhftbWF7873o
                team_id: oBpVhLW22VrqtwKgaayRbP
                display_name: John Doe
          type: object
        assignee:
          $ref: "#/components/schemas/User"
        resolver:
          $ref: "#/components/schemas/User"
      required:
        - content
        - mentions
        - type
      type: object
    SuggestionThreadType:
      description: A suggestion thread.
      properties:
        type:
          enum:
            - suggestion
          type: string
        suggested_edits:
          items:
            $ref: "#/components/schemas/SuggestedEdit"
          minItems: 1
          type: array
        status:
          $ref: "#/components/schemas/SuggestionStatus"
      required:
        - status
        - suggested_edits
        - type
      type: object
    SuggestedEdit:
      description:
        "The type of the suggested edit, along with additional type-specific\
        \ properties."
      discriminator:
        mapping:
          add: "#/components/schemas/AddSuggestedEdit"
          delete: "#/components/schemas/DeleteSuggestedEdit"
          format: "#/components/schemas/FormatSuggestedEdit"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/AddSuggestedEdit"
        - $ref: "#/components/schemas/DeleteSuggestedEdit"
        - $ref: "#/components/schemas/FormatSuggestedEdit"
      type: object
    AddSuggestedEdit:
      description: A suggestion to add some text.
      properties:
        type:
          enum:
            - add
          type: string
        text:
          example: fantastic
          type: string
      required:
        - text
        - type
      type: object
    DeleteSuggestedEdit:
      description: A suggestion to delete some text.
      properties:
        type:
          enum:
            - delete
          type: string
        text:
          example: just
          type: string
      required:
        - text
        - type
      type: object
    FormatSuggestedEdit:
      description: A suggestion to format some text.
      properties:
        type:
          enum:
            - format
          type: string
        format:
          $ref: "#/components/schemas/SuggestionFormat"
      required:
        - format
        - type
      type: object
    SuggestionStatus:
      description: The current status of the suggestion.
      enum:
        - open
        - accepted
        - rejected
      type: string
      x-enum-descriptions:
        - "A suggestion was made, but it hasn't been accepted or rejected yet."
        - A suggestion was accepted and applied to the design.
        - A suggestion was rejected and not applied to the design.
    SuggestionFormat:
      description: The suggested format change.
      enum:
        - font_family
        - font_size
        - font_weight
        - font_style
        - color
        - background_color
        - decoration
        - strikethrough
        - link
        - letter_spacing
        - line_height
        - direction
        - text_align
        - list_marker
        - list_level
        - margin_inline_start
        - text_indent
        - font_size_modifier
        - vertical_align
      example: font_style
      type: string
    Reply:
      description: |-
        A reply to a thread.

        The `author` of the reply might be missing if that user account no longer exists.
      properties:
        id:
          description: The ID of the reply.
          example: KeAZEAjijEb
          type: string
        design_id:
          description:
            The ID of the design that the thread for this reply is attached
            to.
          example: DAFVztcvd9z
          type: string
        thread_id:
          description: The ID of the thread this reply is in.
          example: KeAbiEAjZEj
          type: string
        author:
          $ref: "#/components/schemas/User"
        content:
          $ref: "#/components/schemas/CommentContent"
        mentions:
          additionalProperties:
            $ref: "#/components/schemas/UserMention"
          description: The Canva users mentioned in the comment thread or reply.
          example:
            oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP:
              tag: oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP
              user:
                user_id: oUnPjZ2k2yuhftbWF7873o
                team_id: oBpVhLW22VrqtwKgaayRbP
                display_name: John Doe
          type: object
        created_at:
          description: |-
            When the reply was created, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1692929800
          format: int64
          type: integer
        updated_at:
          description: |-
            When the reply was last updated, as a Unix timestamp
            (in seconds since the Unix Epoch).
          example: 1692929900
          format: int64
          type: integer
      required:
        - content
        - created_at
        - design_id
        - id
        - mentions
        - thread_id
        - updated_at
      type: object
    CommentContent:
      description: The content of a comment thread or reply.
      properties:
        plaintext:
          description: |-
            The content in plaintext.
            Any user mention tags are shown in the format `[user_id:team_id]`.
          example: "Great work [oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP]!"
          type: string
        markdown:
          description: |-
            The content in markdown.
            Any user mention tags are shown in the format `[user_id:team_id]`
          example: "*_Great work_* [oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP]!"
          type: string
      required:
        - plaintext
      type: object
    SuggestionEventType:
      description:
        "The type of suggestion event, along with additional type-specific\
        \ properties."
      discriminator:
        mapping:
          new: "#/components/schemas/NewSuggestionEventType"
          accepted: "#/components/schemas/AcceptedSuggestionEventType"
          rejected: "#/components/schemas/RejectedSuggestionEventType"
          reply: "#/components/schemas/ReplySuggestionEventType"
          mention: "#/components/schemas/MentionSuggestionEventType"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/NewSuggestionEventType"
        - $ref: "#/components/schemas/AcceptedSuggestionEventType"
        - $ref: "#/components/schemas/RejectedSuggestionEventType"
        - $ref: "#/components/schemas/ReplySuggestionEventType"
        - $ref: "#/components/schemas/MentionSuggestionEventType"
      type: object
    NewSuggestionEventType:
      description: Event type for a new suggestion.
      properties:
        type:
          enum:
            - new
          type: string
        suggestion_url:
          description: "A URL to the design, focused on the suggestion."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        suggestion:
          $ref: "#/components/schemas/Thread"
      required:
        - suggestion
        - suggestion_url
        - type
      type: object
    AcceptedSuggestionEventType:
      description: Event type for a suggestion that has been accepted.
      properties:
        type:
          enum:
            - accepted
          type: string
        suggestion_url:
          description: "A URL to the design, focused on the suggestion."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        suggestion:
          $ref: "#/components/schemas/Thread"
      required:
        - suggestion
        - suggestion_url
        - type
      type: object
    RejectedSuggestionEventType:
      description: Event type for a suggestion that has been rejected.
      properties:
        type:
          enum:
            - rejected
          type: string
        suggestion_url:
          description: "A URL to the design, focused on the suggestion."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        suggestion:
          $ref: "#/components/schemas/Thread"
      required:
        - suggestion
        - suggestion_url
        - type
      type: object
    ReplySuggestionEventType:
      description: Event type for a reply to a suggestion.
      properties:
        type:
          enum:
            - reply
          type: string
        reply_url:
          description: "A URL to the design, focused on the suggestion reply."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        reply:
          $ref: "#/components/schemas/Reply"
      required:
        - reply
        - reply_url
        - type
      type: object
    MentionSuggestionEventType:
      description: Event type for a mention in a reply to a suggestion.
      properties:
        type:
          enum:
            - mention
          type: string
        reply_url:
          description: "A URL to the design, focused on the suggestion reply."
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        reply:
          $ref: "#/components/schemas/Reply"
      required:
        - reply
        - reply_url
        - type
      type: object
    UserMentions:
      additionalProperties:
        $ref: "#/components/schemas/UserMention"
      description: The Canva users mentioned in the comment thread or reply.
      example:
        oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP:
          tag: oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP
          user:
            user_id: oUnPjZ2k2yuhftbWF7873o
            team_id: oBpVhLW22VrqtwKgaayRbP
            display_name: John Doe
      type: object
    UserMention:
      description:
        Information about the user mentioned in a comment thread or reply.
        Each user mention is keyed using the user's user ID and team ID separated
        by a colon (`user_id:team_id`).
      properties:
        tag:
          description:
            The mention tag for the user mentioned in the comment thread
            or reply content. This has the format of the user's user ID and team ID
            separated by a colon (`user_id:team_id`).
          example: oUnPjZ2k2yuhftbWF7873o:oBpVhLW22VrqtwKgaayRbP
          type: string
        user:
          $ref: "#/components/schemas/TeamUser"
      required:
        - tag
        - user
      type: object
    GetSigningPublicKeysResponse:
      properties:
        keys:
          description: |-
            A Json Web Key Set (JWKS) with public keys used for signing webhooks. You can use this JWKS
            to verify that a webhook was sent from Canva.
          example:
            - kid: a418dc7d-ecc5-5c4b-85ce-e1104a8addbe
              kty: OKP
              crv: Ed25519
              x: aIQtqd0nDfB-ug0DrzZbwTum-1ITdXvKxGFak_1VB2j
            - kid: c8de5bec1-1b88-4ddaae04acc-ce415-5d7
              kty: OKP
              crv: Ed25519
              x: m2d1FT-gfBXxIzKwdQVTra0D-aBq_ubZ1jI0GuvkDtn
          items:
            $ref: "#/components/schemas/EdDsaJwk"
          type: array
      required:
        - keys
      type: object
    EdDsaJwk:
      description: |-
        A JSON Web Key Set (JWKS) using the Edwards-curve Digital Signature Algorithm (EdDSA), as
        described in [RFC-8037](https://www.rfc-editor.org/rfc/rfc8037.html#appendix-A).
      properties:
        kid:
          description: |-
            The `kid` (key ID) is a unique identifier for a public key. When the keys used
            to sign webhooks are rotated, you can use this ID to select the correct key
            within a JWK Set during the key rollover. The `kid` value is case-sensitive.
          type: string
        kty:
          description: |-
            The `kty` (key type) identifies the cryptographic algorithm family used with
            the key, such as "RSA" or "EC". Only Octet Key Pairs
            (`OKPs`) are supported.
            The `kty` value is case-sensitive. For more information on the `kty` property
            and OKPs, see [RFC-8037 — "kty" (Key Type)
            Parameter](https://www.rfc-editor.org/rfc/rfc8037.html#section-2).
          type: string
        crv:
          description: |-
            The `crv` (curve) property identifies the curve used for elliptical curve
            encryptions. Only "Ed25519" is supported. For more information on the `crv`
            property, see [RFC-8037 — Key Type
            "OKP"](https://www.rfc-editor.org/rfc/rfc8037.html#section-2).
          type: string
        x:
          description: |-
            The `x` property is the public key of an elliptical curve encryption. The key
            is Base64urlUInt-encoded. For more information on the `x` property, see
            [RFC-8037 — "x" (X Coordinate)
            Parameter](https://www.rfc-editor.org/rfc/rfc8037#section-2).
          type: string
      required:
        - crv
        - kid
        - kty
        - x
      type: object
    DataTable:
      description: |-
        Tabular data, structured in rows of cells.

        - The first row usually contains column headers.
        - Each cell must have a data type configured.
        - All rows must have the same number of cells.
        - Maximum of 100 rows and 20 columns.

        WARNING: Chart data fields are a [preview feature](https://www.canva.dev/docs/connect/#preview-apis). There might be unannounced breaking changes to this feature which won't produce a new API version.
      example:
        rows:
          - cells:
              - type: string
                value: Geographic Region
              - type: string
                value: Sales (millions AUD)
              - type: string
                value: Target (millions AUD)
              - type: string
                value: Target met?
              - type: string
                value: Date met
          - cells:
              - type: string
                value: Asia Pacific
              - type: number
                value: 10.2
              - type: number
                value: 10
              - type: boolean
                value: true
              - type: date
                value: 1721944387
          - cells:
              - type: string
                value: EMEA
              - type: number
                value: 13.8
              - type: number
                value: 14
              - type: boolean
                value: false
              - type: date
      properties:
        rows:
          description: |-
            Rows of data.

            The first row usually contains column headers.
          items:
            $ref: "#/components/schemas/DataTableRow"
          maxItems: 100
          type: array
      required:
        - rows
      type: object
    DataTableRow:
      description: A single row of tabular data.
      properties:
        cells:
          description: |-
            Cells of data in row.

            All rows must have the same number of cells.
          items:
            $ref: "#/components/schemas/DataTableCell"
          maxItems: 20
          type: array
      required:
        - cells
      type: object
    DataTableCell:
      description: A single tabular data cell.
      discriminator:
        mapping:
          string: "#/components/schemas/StringDataTableCell"
          number: "#/components/schemas/NumberDataTableCell"
          boolean: "#/components/schemas/BooleanDataTableCell"
          date: "#/components/schemas/DateDataTableCell"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/StringDataTableCell"
        - $ref: "#/components/schemas/NumberDataTableCell"
        - $ref: "#/components/schemas/BooleanDataTableCell"
        - $ref: "#/components/schemas/DateDataTableCell"
      type: object
    StringDataTableCell:
      description: A string tabular data cell.
      properties:
        type:
          enum:
            - string
          type: string
        value:
          type: string
      required:
        - type
      type: object
    NumberDataTableCell:
      description: A number tabular data cell.
      properties:
        type:
          enum:
            - number
          type: string
        value:
          format: double
          type: number
      required:
        - type
      type: object
    BooleanDataTableCell:
      description: A boolean tabular data cell.
      properties:
        type:
          enum:
            - boolean
          type: string
        value:
          type: boolean
      required:
        - type
      type: object
    DateDataTableCell:
      description: |-
        A date tabular data cell.

        Specified as a Unix timestamp (in seconds since the Unix Epoch).
      properties:
        type:
          enum:
            - date
          type: string
        value:
          format: int64
          type: integer
      required:
        - type
      type: object
    SortByType:
      default: relevance
      enum:
        - relevance
        - modified_descending
        - modified_ascending
        - title_descending
        - title_ascending
      type: string
      x-enum-descriptions:
        - Sort results using a relevance algorithm.
        - Sort results by the date last modified in descending order.
        - Sort results by the date last modified in ascending order.
        - Sort results by title in descending order.
        - Sort results by title in ascending order
    OwnershipType:
      default: any
      enum:
        - any
        - owned
        - shared
      type: string
      x-enum-descriptions:
        - Owned by and shared with the user.
        - Owned by the user.
        - Shared with the user.
    GetListDesignResponse:
      properties:
        continuation:
          description: |-
            A continuation token.
            If the success response contains a continuation token, the list contains more designs
            you can list. You can use this token as a query parameter and retrieve more
            designs from the list, for example
            `/v1/designs?continuation={continuation}`.

            To retrieve all of a user's designs, you might need to make multiple requests.
          example: RkFGMgXlsVTDbMd:MR3L0QjiaUzycIAjx0yMyuNiV0OildoiOwL0x32G4NjNu4FwtAQNxowUQNMMYN
          type: string
        items:
          description: The list of designs.
          items:
            $ref: "#/components/schemas/Design"
          type: array
      required:
        - items
      type: object
    CreateDesignRequest:
      description: |-
        Body parameters for creating a new design.
        At least one of `design_type` or `asset_id` must be defined
        to create a new design.
      properties:
        design_type:
          $ref: "#/components/schemas/DesignTypeInput"
        asset_id:
          description:
            "The ID of an asset to insert into the created design. Currently,\
            \ this only supports image assets."
          example: Msd59349ff
          type: string
        title:
          description: The name of the design.
          example: My Holiday Presentation
          maxLength: 255
          minLength: 1
          type: string
      type: object
    CreateDesignResponse:
      description: Details about the new design.
      properties:
        design:
          $ref: "#/components/schemas/Design"
      required:
        - design
      type: object
    GetDesignResponse:
      description: Successful response from a `getDesign` request.
      properties:
        design:
          $ref: "#/components/schemas/Design"
      required:
        - design
      type: object
    GetDesignPagesResponse:
      description: Successful response from a `getDesignPages` request.
      properties:
        items:
          description: The list of pages.
          items:
            $ref: "#/components/schemas/DesignPage"
          type: array
      required:
        - items
      type: object
    GetDesignExportFormatsResponse:
      description: Successful response from a `getDesignExportFormats` request.
      properties:
        formats:
          $ref: "#/components/schemas/ExportFormatOptions"
      required:
        - formats
      type: object
    Design:
      description: "The design object, which contains metadata about the design."
      properties:
        id:
          description: The design ID.
          example: DAFVztcvd9z
          type: string
        title:
          description: The design title.
          example: My summer holiday
          type: string
        owner:
          $ref: "#/components/schemas/TeamUserSummary"
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
        urls:
          $ref: "#/components/schemas/DesignLinks"
        created_at:
          description: |-
            When the design was created in Canva, as a Unix timestamp (in seconds since the Unix
            Epoch).
          example: 1377396000
          format: int64
          type: integer
        updated_at:
          description: |-
            When the design was last updated in Canva, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: **********
          format: int64
          type: integer
        page_count:
          description:
            "The total number of pages in the design. Some design types\
            \ don't have pages (for example, Canva docs)."
          example: 5
          format: int32
          minimum: 0
          type: integer
      required:
        - created_at
        - id
        - owner
        - updated_at
        - urls
      type: object
    PageIndex:
      description:
        The index of the page in the design. The first page in a design
        has the index value `1`.
      maximum: 500
      minimum: 1
      type: integer
    DesignLinks:
      description: A temporary set of URLs for viewing or editing the design.
      properties:
        edit_url:
          description: |-
            A temporary editing URL for the design. This URL is only accessible to the user that made the API request, and is designed to support [return navigation](https://www.canva.dev/docs/connect/return-navigation-guide/) workflows.

            NOTE: This is not a permanent URL, it is only valid for 30 days.
          example: https://www.canva.com/api/design/eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIiwiZXhwaXJ5IjoxNzQyMDk5NDAzMDc5fQ..GKLx2hrJa3wSSDKQ.hk3HA59qJyxehR-ejzt2DThBW0cbRdMBz7Fb5uCpwD-4o485pCf4kcXt_ypUYX0qMHVeZ131YvfwGPIhbk-C245D8c12IIJSDbZUZTS7WiCOJZQ.sNz3mPSQxsETBvl_-upMYA/edit
          type: string
        view_url:
          description: |
            A temporary viewing URL for the design. This URL is only accessible to the user that made the API request, and is designed to support [return navigation](https://www.canva.dev/docs/connect/return-navigation-guide/) workflows.

            NOTE: This is not a permanent URL, it is only valid for 30 days.
          example: https://www.canva.com/api/design/eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIiwiZXhwaXJ5IjoxNzQyMDk5NDAzMDc5fQ..GKLx2hrJa3wSSDKQ.hk3HA59qJyxehR-ejzt2DThBW0cbRdMBz7Fb5uCpwD-4o485pCf4kcXt_ypUYX0qMHVeZ131YvfwGPIhbk-C245D8c12IIJSDbZUZTS7WiCOJZQ.sNz3mPSQxsETBvl_-upMYA/view
          type: string
      required:
        - edit_url
        - view_url
      type: object
    DesignSummary:
      description:
        "Basic details about the design, such as the design's ID, title,\
        \ and URL."
      properties:
        id:
          description: The design ID.
          example: DAFVztcvd9z
          type: string
        title:
          description: The design title.
          example: My summer holiday
          type: string
        url:
          description: URL of the design.
          example: https://www.canva.com/design/DAFVztcvd9z/edit
          type: string
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
        urls:
          $ref: "#/components/schemas/DesignLinks"
        created_at:
          description: |-
            When the design was created in Canva, as a Unix timestamp (in seconds since the Unix
            Epoch).
          example: 1377396000
          format: int64
          type: integer
        updated_at:
          description: |-
            When the design was last updated in Canva, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: **********
          format: int64
          type: integer
        page_count:
          description:
            "The total number of pages in the design. Some design types\
            \ don't have pages (for example, Canva docs)."
          example: 3
          format: int32
          minimum: 0
          type: integer
      required:
        - created_at
        - id
        - updated_at
        - urls
      type: object
    DesignPage:
      description:
        "Basic details about a page in a design, such as the page's index\
        \ and thumbnail."
      properties:
        index:
          description:
            The index of the page in the design. The first page in a design
            has the index value `1`.
          maximum: 500
          minimum: 1
          type: integer
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
      required:
        - index
      type: object
    DesignImportMetadata:
      description:
        Metadata about the design that you include as a header parameter
        when importing a design.
      properties:
        title_base64:
          description: |-
            The design's title, encoded in Base64.

            The maximum length of a design title in Canva (unencoded) is 50 characters.

            Base64 encoding allows titles containing emojis and other special
            characters to be sent using HTTP headers.
            For example, "My Awesome Design 😍" Base64 encoded
            is `TXkgQXdlc29tZSBEZXNpZ24g8J+YjQ==`.
          example: TXkgQXdlc29tZSBEZXNpZ24g8J+YjQ==
          minLength: 1
          type: string
        mime_type:
          description:
            "The MIME type of the file being imported. If not provided,\
            \ Canva attempts to automatically detect the type of the file."
          example: application/pdf
          type: string
      required:
        - title_base64
      type: object
    CreateDesignImportJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignImportJob"
      required:
        - job
      type: object
    DesignImportJob:
      description: The status of the design import job.
      properties:
        id:
          description: The ID of the design import job.
          example: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          type: string
        status:
          $ref: "#/components/schemas/DesignImportStatus"
        result:
          $ref: "#/components/schemas/DesignImportJobResult"
        error:
          $ref: "#/components/schemas/DesignImportError"
      required:
        - id
        - status
      type: object
    DesignImportStatus:
      description: The status of the design import job.
      enum:
        - failed
        - in_progress
        - success
      example: success
      type: string
    DesignImportError:
      description:
        "If the import job fails, this object provides details about the\
        \ error."
      properties:
        code:
          $ref: "#/components/schemas/DesignImportErrorCode"
        message:
          description: A human-readable description of what went wrong.
          example:
            "We're sorry, but the file upload quota has exceeded. Please try\
            \ again later."
          type: string
      required:
        - code
        - message
      type: object
    DesignImportErrorCode:
      description: |-
        A short string about why the import failed. This field can be used to handle errors
        programmatically.
      enum:
        - design_creation_throttled
        - design_import_throttled
        - duplicate_import
        - internal_error
        - invalid_file
        - fetch_failed
      example: design_creation_throttled
      type: string
    DesignImportJobResult:
      properties:
        designs:
          description: |-
            A list of designs imported from the external file. It usually contains one item.
            Imports with a large number of pages or assets are split into multiple designs.
          items:
            $ref: "#/components/schemas/DesignSummary"
          type: array
      required:
        - designs
      type: object
    GetDesignImportJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignImportJob"
      required:
        - job
      type: object
    CreateUrlImportJobRequest:
      properties:
        title:
          description: A title for the design.
          example: My Awesome Design
          maxLength: 255
          minLength: 1
          type: string
        url:
          description:
            The URL of the file to import. This URL must be accessible
            from the internet and be publicly available.
          maxLength: 2048
          minLength: 1
          type: string
        mime_type:
          description:
            "The MIME type of the file being imported. If not provided,\
            \ Canva attempts to automatically detect the type of the file."
          example: application/vnd.apple.keynote
          maxLength: 100
          minLength: 1
          type: string
      required:
        - title
        - url
      type: object
    CreateUrlImportJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignImportJob"
      required:
        - job
      type: object
    GetUrlImportJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignImportJob"
      required:
        - job
      type: object
    DesignTypeInput:
      description: The desired design type.
      discriminator:
        mapping:
          preset: "#/components/schemas/PresetDesignTypeInput"
          custom: "#/components/schemas/CustomDesignTypeInput"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/PresetDesignTypeInput"
        - $ref: "#/components/schemas/CustomDesignTypeInput"
      type: object
    PresetDesignTypeInput:
      description: Provide the common design type.
      properties:
        type:
          enum:
            - preset
          type: string
        name:
          $ref: "#/components/schemas/PresetDesignTypeName"
      required:
        - name
        - type
      type: object
    PresetDesignTypeName:
      description: The name of the design type.
      enum:
        - doc
        - whiteboard
        - presentation
      type: string
      x-enum-descriptions:
        - "A [Canva doc](https://www.canva.com/docs/); a document for Canva's online\
          \ text editor."
        - "A [whiteboard](https://www.canva.com/online-whiteboard/); a design which\
          \ gives you infinite space to collaborate."
        - "A [presentation](https://www.canva.com/presentations/); lets you create and\
          \ collaborate for presenting to an audience."
    CustomDesignTypeInput:
      description: Provide the width and height to define a custom design type.
      properties:
        type:
          enum:
            - custom
          type: string
        width:
          description: "The width of the design, in pixels."
          example: 320
          maximum: 8000
          minimum: 40
          type: integer
        height:
          description: "The height of the design, in pixels."
          example: 200
          maximum: 8000
          minimum: 40
          type: integer
      required:
        - height
        - type
        - width
      type: object
    Error:
      properties:
        code:
          $ref: "#/components/schemas/ErrorCode"
        message:
          description: A human-readable description of what went wrong.
          type: string
      required:
        - code
        - message
      type: object
    OauthError:
      properties:
        error:
          $ref: "#/components/schemas/ErrorCode"
        error_description:
          description: A human-readable description of what went wrong.
          type: string
      required:
        - error
        - error_description
      type: object
    ErrorCode:
      description: |
        A short string indicating what failed. This field can be used to handle errors programmatically.
      enum:
        - internal_error
        - invalid_field
        - invalid_header_value
        - permission_denied
        - too_many_requests
        - not_found
        - bad_request_body
        - bad_http_method
        - bad_request_params
        - bad_query_params
        - endpoint_not_found
        - unsupported_version
        - invalid_access_token
        - revoked_access_token
        - missing_field
        - missing_scope
        - invalid_grant
        - invalid_request
        - invalid_client
        - unauthorized_client
        - unsupported_grant_type
        - invalid_scope
        - invalid_basic_header
        - invalid_file_format
        - quota_exceeded
        - unsupported_content_type
        - request_too_large
        - folder_not_found
        - item_in_multiple_folders
        - asset_not_found
        - max_limit_reached
        - permission_not_found
        - permission_exists
        - unauthorized_user
        - user_not_found
        - group_not_found
        - app_not_found
        - content_not_found
        - doctype_not_found
        - design_not_found
        - offset_too_large
        - page_not_found
        - design_or_comment_not_found
        - design_or_thread_not_found
        - design_type_not_found
        - team_not_found
        - comment_not_found
        - too_many_comments
        - too_many_replies
        - message_too_long
        - thread_not_found
        - reply_not_found
        - design_not_fillable
        - autofill_data_invalid
        - feature_not_available
        - license_required
      type: string
    CreateDesignExportJobRequest:
      description: |-
        Body parameters for starting an export job for a design.
        It must include a design ID, and one of the supported export formats.
      example:
        design_id: DAVZr1z5464
        format:
          type: pdf
          size: a4
          pages:
            - 2
            - 3
            - 4
      properties:
        design_id:
          description: The design ID.
          type: string
        format:
          $ref: "#/components/schemas/ExportFormat"
      required:
        - design_id
        - format
      type: object
    ExportFormat:
      description: Details about the desired export format.
      discriminator:
        mapping:
          pdf: "#/components/schemas/PdfExportFormat"
          jpg: "#/components/schemas/JpgExportFormat"
          png: "#/components/schemas/PngExportFormat"
          pptx: "#/components/schemas/PptxExportFormat"
          gif: "#/components/schemas/GifExportFormat"
          mp4: "#/components/schemas/Mp4ExportFormat"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/PdfExportFormat"
        - $ref: "#/components/schemas/JpgExportFormat"
        - $ref: "#/components/schemas/PngExportFormat"
        - $ref: "#/components/schemas/PptxExportFormat"
        - $ref: "#/components/schemas/GifExportFormat"
        - $ref: "#/components/schemas/Mp4ExportFormat"
      type: object
    PdfExportFormat:
      description: Export the design as a PDF. Providing a paper size is optional.
      properties:
        type:
          enum:
            - pdf
          type: string
        export_quality:
          $ref: "#/components/schemas/ExportQuality"
        size:
          $ref: "#/components/schemas/ExportPageSize"
        pages:
          description: |-
            To specify which pages to export in a multi-page design, provide the page numbers as
            an array. The first page in a design is page `1`.
            If `pages` isn't specified, all the pages are exported.
          example:
            - 2
            - 3
            - 4
          items:
            minimum: 1
            type: integer
          type: array
      required:
        - type
      type: object
    GifExportFormat:
      description: |-
        Export the design as a GIF. Height or width (or both) may be specified, otherwise the file
        will be exported at it's default size. Large designs will be scaled down, and aspect ratio
        will always be maintained.
      properties:
        type:
          enum:
            - gif
          type: string
        export_quality:
          $ref: "#/components/schemas/ExportQuality"
        height:
          description: |-
            Specify the height in pixels of the exported image. Note the following behavior:

            - If no height or width is specified, the image is exported using the dimensions of the design.
            - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
            - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
          example: 400
          format: int32
          maximum: 25000
          minimum: 40
          type: integer
        width:
          description: |-
            Specify the width in pixels of the exported image. Note the following behavior:

            - If no width or height is specified, the image is exported using the dimensions of the design.
            - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
            - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
          example: 400
          format: int32
          maximum: 25000
          minimum: 40
          type: integer
        pages:
          description: |-
            To specify which pages to export in a multi-page design, provide the page numbers as
            an array. The first page in a design is page `1`.
            If `pages` isn't specified, all the pages are exported.
          example:
            - 2
            - 3
            - 4
          items:
            minimum: 1
            type: integer
          type: array
      required:
        - type
      type: object
    JpgExportFormat:
      description: |-
        Export the design as a JPEG. Compression quality must be provided. Height or width (or both)
        may be specified, otherwise the file will be exported at it's default size.

        If the user is on the Canva Free plan, the export height and width for a fixed-dimension design can't be upscaled by more than a factor of `1.125`.
      properties:
        type:
          enum:
            - jpg
          type: string
        export_quality:
          $ref: "#/components/schemas/ExportQuality"
        quality:
          description:
            "For the `jpg` type, the `quality` of the exported JPEG determines\
            \ how compressed the exported file should be. A _low_ `quality` value\
            \ will create a file with a smaller file size, but the resulting file\
            \ will have pixelated artifacts when compared to a file created with a\
            \ _high_ `quality` value."
          example: 80
          maximum: 100
          minimum: 1
          type: integer
        height:
          description: |-
            Specify the height in pixels of the exported image. Note the following behavior:

            - If no height or width is specified, the image is exported using the dimensions of the design.
            - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
            - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
          example: 400
          format: int32
          maximum: 25000
          minimum: 40
          type: integer
        width:
          description: |-
            Specify the width in pixels of the exported image. Note the following behavior:

            - If no width or height is specified, the image is exported using the dimensions of the design.
            - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
            - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
          example: 400
          format: int32
          maximum: 25000
          minimum: 40
          type: integer
        pages:
          description: |-
            To specify which pages to export in a multi-page design, provide the page numbers as
            an array. The first page in a design is page `1`.
            If `pages` isn't specified, all the pages are exported.
          example:
            - 2
            - 3
            - 4
          items:
            minimum: 1
            type: integer
          type: array
      required:
        - quality
        - type
      type: object
    PngExportFormat:
      description: |-
        Export the design as a PNG. Height or width (or both) may be specified, otherwise
        the file will be exported at it's default size. You may also specify whether to export the
        file losslessly, and whether to export a multi-page design as a single image.

        If the user is on the Canva Free plan, the export height and width for a fixed-dimension design can't be upscaled by more than a factor of `1.125`.
      properties:
        type:
          enum:
            - png
          type: string
        export_quality:
          $ref: "#/components/schemas/ExportQuality"
        height:
          description: |-
            Specify the height in pixels of the exported image. Note the following behavior:

            - If no height or width is specified, the image is exported using the dimensions of the design.
            - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
            - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
          example: 400
          format: int32
          maximum: 25000
          minimum: 40
          type: integer
        width:
          description: |-
            Specify the width in pixels of the exported image. Note the following behavior:

            - If no width or height is specified, the image is exported using the dimensions of the design.
            - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
            - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
          example: 400
          format: int32
          maximum: 25000
          minimum: 40
          type: integer
        lossless:
          default: true
          description: |-
            If set to `true` (default), the PNG is exported without compression.
            If set to `false`, the PNG is compressed using a lossy compression algorithm. Lossy PNG compression is only available to users on a Canva plan that has premium features, such as Canva Pro. If the user is on the Canva Free plan and this parameter is set to `false`, the export operation will fail.
          type: boolean
        transparent_background:
          default: false
          description: |-
            If set to `true`, the PNG is exported with a transparent background.
            This option is only available to users on a Canva plan that has premium features, such as Canva Pro. If the user is on the Canva Free plan and this parameter is set to `true`, the export operation will fail.
          type: boolean
        as_single_image:
          default: false
          description: |-
            When `true`, multi-page designs are merged into a single image.
            When `false` (default), each page is exported as a separate image.
          type: boolean
        pages:
          description: |-
            To specify which pages to export in a multi-page design, provide the page numbers as
            an array. The first page in a design is page `1`.
            If `pages` isn't specified, all the pages are exported.
          example:
            - 2
            - 3
            - 4
          items:
            minimum: 1
            type: integer
          type: array
      required:
        - type
      type: object
    PptxExportFormat:
      description: Export the design as a PPTX.
      properties:
        type:
          enum:
            - pptx
          type: string
        pages:
          description: |-
            To specify which pages to export in a multi-page design, provide the page numbers as
            an array. The first page in a design is page `1`.
            If `pages` isn't specified, all the pages are exported.
          example:
            - 2
            - 3
            - 4
          items:
            minimum: 1
            type: integer
          type: array
      required:
        - type
      type: object
    Mp4ExportFormat:
      description:
        Export the design as an MP4. You must specify the quality of the
        exported video.
      properties:
        type:
          enum:
            - mp4
          type: string
        export_quality:
          $ref: "#/components/schemas/ExportQuality"
        quality:
          $ref: "#/components/schemas/Mp4ExportQuality"
        pages:
          description: |-
            To specify which pages to export in a multi-page design, provide the page numbers as
            an array. The first page in a design is page `1`.
            If `pages` isn't specified, all the pages are exported.
          example:
            - 2
            - 3
            - 4
          items:
            minimum: 1
            type: integer
          type: array
      required:
        - quality
        - type
      type: object
    CreateDesignExportJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/ExportJob"
      required:
        - job
      type: object
    GetDesignExportJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/ExportJob"
      required:
        - job
      type: object
    ExportJob:
      description: The status of the export job.
      properties:
        id:
          description: The export job ID.
          example: e08861ae-3b29-45db-8dc1-1fe0bf7f1cc8
          type: string
        status:
          $ref: "#/components/schemas/DesignExportStatus"
        urls:
          description: |-
            Download URL(s) for the completed export job. These URLs expire after 24 hours.

            Depending on the design type and export format, there is a download URL for each page in the design. The list is sorted by page order.
          example:
            - https://export-download.canva-dev.com/...
          items:
            type: string
          type: array
        error:
          $ref: "#/components/schemas/ExportError"
      required:
        - id
        - status
      type: object
    ExportPageSize:
      default: a4
      description:
        The paper size of the export PDF file. The `size` attribute is
        only supported for Documents (Canva Docs).
      enum:
        - a4
        - a3
        - letter
        - legal
      example: a4
      type: string
    DesignExportStatus:
      description: |-
        The export status of the job. A newly created job will be `in_progress` and will eventually
        become `success` or `failed`.
      enum:
        - failed
        - in_progress
        - success
      type: string
    ExportHeight:
      description: |-
        Specify the height in pixels of the exported image. Note the following behavior:

        - If no height or width is specified, the image is exported using the dimensions of the design.
        - If only one of height or width is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
        - If both the height and width are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
      example: 400
      format: int32
      maximum: 25000
      minimum: 40
      type: integer
    ExportWidth:
      description: |-
        Specify the width in pixels of the exported image. Note the following behavior:

        - If no width or height is specified, the image is exported using the dimensions of the design.
        - If only one of width or height is specified, then the image is scaled to match that dimension, respecting the design's aspect ratio.
        - If both the width and height are specified, but the values don't match the design's aspect ratio, the export defaults to the larger dimension.
      example: 400
      format: int32
      maximum: 25000
      minimum: 40
      type: integer
    Mp4ExportQuality:
      description: |-
        The orientation and resolution of the exported video. Orientation is either `horizontal` or
        `vertical`, and resolution is one of `480p`, `720p`, `1080p` or `4k`.
      enum:
        - horizontal_480p
        - horizontal_720p
        - horizontal_1080p
        - horizontal_4k
        - vertical_480p
        - vertical_720p
        - vertical_1080p
        - vertical_4k
      type: string
    ExportFormatOptions:
      description: The available file formats for exporting the design.
      properties:
        pdf:
          $ref: "#/components/schemas/PdfExportFormatOption"
        jpg:
          $ref: "#/components/schemas/JpgExportFormatOption"
        png:
          $ref: "#/components/schemas/PngExportFormatOption"
        svg:
          $ref: "#/components/schemas/SvgExportFormatOption"
        pptx:
          $ref: "#/components/schemas/PptxExportFormatOption"
        gif:
          $ref: "#/components/schemas/GifExportFormatOption"
        mp4:
          $ref: "#/components/schemas/Mp4ExportFormatOption"
      type: object
    PdfExportFormatOption:
      description: Whether the design can be exported as a PDF.
      type: object
    GifExportFormatOption:
      description: Whether the design can be exported as a GIF.
      type: object
    JpgExportFormatOption:
      description: Whether the design can be exported as a JPEG.
      type: object
    PngExportFormatOption:
      description: Whether the design can be exported as a PNG.
      type: object
    SvgExportFormatOption:
      description: Whether the design can be exported as an SVG.
      type: object
    PptxExportFormatOption:
      description: Whether the design can be exported as a PPTX.
      type: object
    Mp4ExportFormatOption:
      description: Whether the design can be exported as an MP4.
      type: object
    ExportError:
      description: "If the export fails, this object provides details about the error."
      properties:
        code:
          $ref: "#/components/schemas/ExportErrorCode"
        message:
          description: A human-readable description of what went wrong.
          type: string
      required:
        - code
        - message
      type: object
    ExportErrorCode:
      description: |-
        If the export failed, this specifies the reason why it failed.

        - `license_required`: The design contains [premium elements](https://www.canva.com/help/premium-elements/) that haven't been purchased. You can either buy the elements or upgrade to a Canva plan (such as Canva Pro) that has premium features, then try again. Alternatively, you can set `export_quality` to `regular` to export your document in regular quality.
        - `approval_required`: The design requires [reviewer approval](https://www.canva.com/en_au/help/design-approval/) before it can be exported.
        - `internal_failure`: The service encountered an error when exporting your design.
      enum:
        - license_required
        - approval_required
        - internal_failure
      type: string
      x-enum-descriptions:
        - "The design contains [premium elements](https://www.canva.com/help/premium-elements/)\
          \ that haven't been purchased. You can either buy the elements or upgrade\
          \ to a Canva plan (such as Canva Pro) that has premium features, then try\
          \ again. Alternatively, you can set `export_quality` to `regular` to export\
          \ your document in regular quality."
        - "The design requires [reviewer approval](https://www.canva.com/en_au/help/design-approval/)\
          \ before it can be exported."
        - The service encountered an error when exporting your design.
    ExportQuality:
      default: regular
      description: Specifies the export quality of the design.
      enum:
        - regular
        - pro
      type: string
      x-enum-descriptions:
        - Regular quality export.
        - |-
          Premium quality export.

          NOTE: A `pro` export might fail if the design contains [premium elements](https://www.canva.com/help/premium-elements/) and the calling user either hasn't purchased the elements or isn't on a Canva plan (such as Canva Pro) that has premium features.
    FolderItemSortBy:
      default: modified_descending
      enum:
        - created_ascending
        - created_descending
        - modified_ascending
        - modified_descending
        - title_ascending
        - title_descending
      type: string
      x-enum-descriptions:
        - "Sort results by creation date, in ascending order."
        - "Sort results by creation date, in descending order."
        - "Sort results by the last modified date, in ascending order."
        - "Sort results by the last modified date, in descending order."
        - "Sort results by title, in ascending order. The title is either the `name`\
          \ field for a folder or asset, or the `title` field for a design."
        - "Sort results by title, in descending order. The title is either the `name`\
          \ field for a folder or asset, or the `title` field for a design."
    FolderItemType:
      enum:
        - design
        - folder
        - image
      type: string
    GetFolderResponse:
      description: The folder ID.
      properties:
        folder:
          $ref: "#/components/schemas/Folder"
      required:
        - folder
      type: object
    CreateFolderRequest:
      description: Body parameters for creating a new folder.
      properties:
        name:
          description: The name of the folder.
          example: My awesome holiday
          maxLength: 255
          minLength: 1
          type: string
        parent_folder_id:
          description: |-
            The folder ID of the parent folder. To create a new folder at the top level of a user's
            [projects](https://www.canva.com/help/find-designs-and-folders/), use the ID `root`.
          example: FAF2lZtloor
          maxLength: 50
          minLength: 1
          type: string
      required:
        - name
        - parent_folder_id
      type: object
    CreateFolderResponse:
      description: Details about the new folder.
      properties:
        folder:
          $ref: "#/components/schemas/Folder"
      type: object
    UpdateFolderRequest:
      description: Body parameters for updating the folder's details.
      properties:
        name:
          description: "The folder name, as shown in the Canva UI."
          example: My awesome holiday
          maxLength: 255
          minLength: 1
          type: string
      required:
        - name
      type: object
    UpdateFolderResponse:
      description: Details about the updated folder.
      properties:
        folder:
          $ref: "#/components/schemas/Folder"
      type: object
    ListFolderItemsResponse:
      description: |-
        A list of the items in a folder.
        If the success response contains a continuation token, the folder contains more items
        you can list. You can use this token as a query parameter and retrieve more
        items from the list, for example
        `/v1/folders/{folderId}/items?continuation={continuation}`.

        To retrieve all the items in a folder, you might need to make multiple requests.
      properties:
        items:
          description: An array of items in the folder.
          items:
            $ref: "#/components/schemas/FolderItemSummary"
          type: array
        continuation:
          description: |-
            If the success response contains a continuation token, the folder contains more items
            you can list. You can use this token as a query parameter and retrieve more
            items from the list, for example
            `/v1/folders/{folderId}/items?continuation={continuation}`.

            To retrieve all the items in a folder, you might need to make multiple requests.
          example: RkFGMgXlsVTDbMd:MR3L0QjiaUzycIAjx0yMyuNiV0OildoiOwL0x32G4NjNu4FwtAQNxowUQNMMYN
          type: string
      required:
        - items
      type: object
    FolderItemSummary:
      description: Details about the folder item.
      discriminator:
        mapping:
          folder: "#/components/schemas/FolderItem"
          design: "#/components/schemas/DesignItem"
          image: "#/components/schemas/ImageItem"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/FolderItem"
        - $ref: "#/components/schemas/DesignItem"
        - $ref: "#/components/schemas/ImageItem"
      type: object
    FolderItem:
      description: Details about the folder.
      properties:
        type:
          enum:
            - folder
          type: string
        folder:
          $ref: "#/components/schemas/Folder"
      required:
        - folder
        - type
      type: object
    DesignItem:
      description: Details about the design.
      properties:
        type:
          enum:
            - design
          type: string
        design:
          $ref: "#/components/schemas/DesignSummary"
      required:
        - design
        - type
      type: object
    ImageItem:
      description: Details about the image asset.
      properties:
        type:
          enum:
            - image
          type: string
        image:
          $ref: "#/components/schemas/AssetSummary"
      required:
        - image
        - type
      type: object
    MoveFolderItemRequest:
      description: Body parameters for moving the folder.
      properties:
        to_folder_id:
          description: |-
            The ID of the folder you want to move the item to (the destination folder).
            If you want to move the item to the top level of a Canva user's
            [projects](https://www.canva.com/help/find-designs-and-folders/), use the ID `root`.
          example: FAF2lZtloor
          maxLength: 50
          minLength: 1
          type: string
        item_id:
          description:
            "The ID of the item you want to move. Currently, video assets\
            \ are not supported."
          example: Msd59349ff
          maxLength: 50
          minLength: 1
          type: string
      required:
        - item_id
        - to_folder_id
      type: object
    Folder:
      description: "The folder object, which contains metadata about the folder."
      properties:
        id:
          description: The folder ID.
          example: FAF2lZtloor
          type: string
        name:
          description: The folder name.
          example: My awesome holiday
          type: string
        created_at:
          description: |-
            When the folder was created, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: 1377396000
          format: int64
          type: integer
        updated_at:
          description: |-
            When the folder was last updated, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: **********
          format: int64
          type: integer
        thumbnail:
          $ref: "#/components/schemas/Thumbnail"
      required:
        - created_at
        - id
        - name
        - updated_at
      type: object
    FolderSummary:
      description: |-
        This object contains some folder metadata. You can retrieve additional metadata
        using the folder ID and the `/v1/folders/{folderId}` endpoint.
      properties:
        id:
          description: The folder ID.
          example: FAF2lZtloor
          type: string
        title:
          deprecated: true
          description: |-
            The folder name, as shown in the Canva UI. This property is deprecated, so you should
            use the `name` property instead.
          example: My awesome holiday
          type: string
        name:
          description: "The folder name, as shown in the Canva UI."
          example: My awesome holiday
          type: string
        created_at:
          description: |-
            When the folder was created, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: 1377396000
          format: int64
          type: integer
        updated_at:
          description: |-
            When the folder was last updated, as a Unix timestamp (in seconds since the
            Unix Epoch).
          example: **********
          format: int64
          type: integer
        url:
          description: The folder URL.
          example: https://www.canva.com/folder/FAF2lZtloor
          type: string
      required:
        - created_at
        - id
        - name
        - updated_at
      type: object
    Group:
      description: |-
        Metadata for the Canva Group, consisting of the Group ID,
        display name, and whether it's an external Canva Group.
      properties:
        id:
          description: The ID of the group with permissions to access the design.
          example: dl9n9SoWoExMsw6Ri1iTg
          type: string
        display_name:
          description: The display name of the group.
          example: Sales team
          type: string
        external:
          description: |-
            Is the user making the API call (the authenticated user) and the Canva Group
            from different Canva Teams?

            - When `true`, the user and the group aren't in the same Canva Team.
            - When `false`, the user and the group are in the same Canva Team.
          type: boolean
      required:
        - external
        - id
      type: object
    ExchangeAccessTokenRequest:
      discriminator:
        mapping:
          authorization_code: "#/components/schemas/ExchangeAuthCodeRequest"
          refresh_token: "#/components/schemas/ExchangeRefreshTokenRequest"
        propertyName: grant_type
      oneOf:
        - $ref: "#/components/schemas/ExchangeAuthCodeRequest"
        - $ref: "#/components/schemas/ExchangeRefreshTokenRequest"
      type: object
    ExchangeAuthCodeRequest:
      properties:
        grant_type:
          description: For exchanging an authorization code for an access token.
          enum:
            - authorization_code
          example: authorization_code
          type: string
        code_verifier:
          description:
            The `code_verifier` value that you generated when creating
            the user authorization URL.
          example: i541qdcfkb4htnork0w92lnu43en99ls5a48ittv6udqgiflqon8vusojojakbq4
          type: string
        code:
          description:
            The authorization code you received after the user authorized
            the integration.
          example: kp8nnroja7qnx00.opyc1p76rcbyflsxbycjqfp3ub8vzsvltpzwafy9q5l45dn5fxzhe7i7a6mg1i2t8jpsa6sebdeumkzzhicskabgevrxsssec4dvjwfvhq4gs3ugghguar0voiqpfb7axsapiojoter8v3w2s5s3st84jpv2l06h667iw241xngy9c8=vu1tnjp7sz
          type: string
        client_id:
          description: |
            Your integration's unique ID, for authenticating the request.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: OC-FAB12-AbCdEf
          type: string
        client_secret:
          description: |
            Your integration's client secret, for authenticating the request. Begins with `cnvca`.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: cnvcaAbcdefg12345_hijklm6789
          type: string
        redirect_uri:
          description: |
            Only required if a redirect URL was supplied when you [created the user authorization URL](https://www.canva.dev/docs/connect/authentication/#create-the-authorization-url).

            Must be one of those already specified by the client. If not supplied, the first redirect_uri defined for the client will be used by default.
          example: https://example.com/process-auth
          type: string
      required:
        - code
        - code_verifier
        - grant_type
      type: object
    ExchangeRefreshTokenRequest:
      properties:
        grant_type:
          description: For generating an access token using a refresh token.
          enum:
            - refresh_token
          example: refresh_token
          type: string
        client_id:
          description: |
            Your integration's unique ID, for authenticating the request.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: OC-FAB12-AbCdEf
          type: string
        client_secret:
          description: |
            Your integration's client secret, for authenticating the request. Begins with `cnvca`.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: cnvcaAbcdefg12345_hijklm6789
          type: string
        refresh_token:
          description:
            The refresh token to be exchanged. You can copy this value
            from the successful response received when generating an access token.
          example: JABix5nolsk9k8n2r0f8nq1gw4zjo40ht6sb4i573wgdzmkwdmiy6muh897hp0bxyab276wtgqkvtob2mg9aidt5d6rcltcbcgs101
          type: string
        scope:
          description: |
            Optional scope value when refreshing an access token. Separate multiple [scopes](https://www.canva.dev/docs/connect/appendix/scopes/) with a single space between each scope.

            The requested scope cannot include any permissions not already granted, so this parameter allows you to limit the scope when refreshing a token. If omitted, the scope for the token remains unchanged.
          example: design:meta:read
          type: string
      required:
        - grant_type
        - refresh_token
      type: object
    ExchangeAccessTokenResponse:
      description: Exchange auth token to access token.
      properties:
        access_token:
          description:
            "The bearer access token to use to authenticate to Canva Connect\
            \ API endpoints. If requested using a `authorization_code` or `refresh_token`,\
            \ this allows you to act on behalf of a user."
          example: JagALLazU0i2ld9WW4zTO4kaG0lkvP8Y5sSO206ZwxNF4E1y3xKJKF7TzN17BXTfaNOeY0P88AeRCE6cRF7SJzvf3Sx97rA80sGHtFplFo
          type: string
        refresh_token:
          description: The token that you can use to refresh the access token.
          example: JABix5nolsk9k8n2r0f8nq1gw4zjo40ht6sb4i573wgdzmkwdmiy6muh897hp0bxyab276wtgqkvtob2mg9aidt5d6rcltcbcgs101
          type: string
        token_type:
          description: The token type returned. This is always `Bearer`.
          example: Bearer
          type: string
        expires_in:
          description: The expiry time (in seconds) for the token.
          example: 14400
          format: int64
          type: integer
        scope:
          description:
            "The [scopes](https://www.canva.dev/docs/connect/appendix/scopes/)\
            \ that the token has been granted."
          example: asset:read design:meta:read design:permission:read folder:read
          type: string
      required:
        - access_token
        - expires_in
        - refresh_token
        - token_type
      type: object
    IntrospectTokenRequest:
      properties:
        token:
          description: The token to introspect.
          example: JagALLazU0i2ld9WW4zTO4kaG0lkvP8Y5sSO206ZwxNF4E1y3xKJKF7TzN17BXTfaNOeY0P88AeRCE6cRF7SJzvf3Sx97rA80sGHtFplFo
          type: string
        client_id:
          description: |
            Your integration's unique ID, for authenticating the request.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: OC-FAB12-AbCdEf
          type: string
        client_secret:
          description: |
            Your integration's client secret, for authenticating the request. Begins with `cnvca`.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: cnvcaAbcdefg12345_hijklm6789
          type: string
      required:
        - token
      type: object
    IntrospectTokenResponse:
      description: Introspection result of access or refresh tokens
      properties:
        active:
          description: |
            Whether the access token is active.

            If `true`, the access token is valid and active. If `false`, the access token is invalid.
          example: true
          type: boolean
        scope:
          description:
            "The [scopes](https://www.canva.dev/docs/connect/appendix/scopes/)\
            \ that the token has been granted."
          example: asset:read design:meta:read design:permission:read folder:read
          type: string
        client:
          description: The ID of the client that requested the token.
          example: OC-FAB12-AbCdEf
          type: string
        exp:
          description:
            "The expiration time of the token, as a [Unix timestamp](https://en.wikipedia.org/wiki/Unix_time)\
            \ in seconds."
          example: 1712216144
          format: int64
          type: integer
        iat:
          description:
            "When the token was issued, as a [Unix timestamp](https://en.wikipedia.org/wiki/Unix_time)\
            \ in seconds."
          example: 1712201744
          format: int64
          type: integer
        nbf:
          description:
            "The \"not before\" time of the token, which specifies the\
            \ time before which the access token most not be accepted, as a [Unix\
            \ timestamp](https://en.wikipedia.org/wiki/Unix_time) in seconds."
          example: 1712201744
          format: int64
          type: integer
        jti:
          description: A unique ID for the access token.
          example: AbC1d-efgHIJKLMN2oPqrS
          type: string
        sub:
          description: |
            The subject of the claim. This is the ID of the Canva user that the access token acts on behalf of.

            This is an obfuscated value, so a single user has a unique ID for each integration. If the same user authorizes another integration, their ID in that other integration is different.
          example: oBCdEF1Gh2i3jkLmno-pq
          type: string
      required:
        - active
      type: object
    RevokeTokensRequest:
      description: Supply an access token or refresh token to have its lineage revoked.
      properties:
        token:
          description: The token to revoke.
          example: agALLazU0i2ld9WW4zTO4kaG0lkvP8Y5sSO206ZwxNF4E1y3xKJKF7TzN17BXTfaNOeY0P88AeRCE6cRF7SJzvf3Sx97rA80sGHtFplFo
          type: string
        client_id:
          description: |
            Your integration's unique ID, for authenticating the request.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: OC-FAB12-AbCdEf
          type: string
        client_secret:
          description: |
            Your integration's client secret, for authenticating the request. Begins with `cnvca`.

            NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
          example: cnvcaAbcdefg12345_hijklm6789
          type: string
      required:
        - token
      type: object
    RevokeTokensResponse:
      description: The response on a successful token revocation.
      type: object
    scope_response:
      description:
        "The [scopes](https://www.canva.dev/docs/connect/appendix/scopes/)\
        \ that the token has been granted."
      example: asset:read design:meta:read design:permission:read folder:read
      type: string
    client_id:
      description: |
        Your integration's unique ID, for authenticating the request.

        NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
      example: OC-FAB12-AbCdEf
      type: string
    client_secret:
      description: |
        Your integration's client secret, for authenticating the request. Begins with `cnvca`.

        NOTE: We recommend that you use basic access authentication instead of specifying `client_id` and `client_secret` as body parameters.
      example: cnvcaAbcdefg12345_hijklm6789
      type: string
    CreateDesignResizeJobRequest:
      description: |-
        Body parameters for starting a resize job for a design.
        It must include a design ID, and one of the supported design type.
      properties:
        design_id:
          description: The design ID.
          pattern: "^[a-zA-Z0-9_-]{1,50}$"
          type: string
        design_type:
          $ref: "#/components/schemas/DesignTypeInput"
      required:
        - design_id
        - design_type
      type: object
    CreateDesignResizeJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignResizeJob"
      required:
        - job
      type: object
    GetDesignResizeJobResponse:
      properties:
        job:
          $ref: "#/components/schemas/DesignResizeJob"
      required:
        - job
      type: object
    DesignResizeJob:
      description: Details about the design resize job.
      properties:
        id:
          description: The design resize job ID.
          example: bbd8dfcd-ead1-4871-81d5-962bfec82274
          type: string
        status:
          $ref: "#/components/schemas/DesignResizeStatus"
        result:
          $ref: "#/components/schemas/DesignResizeJobResult"
        error:
          $ref: "#/components/schemas/DesignResizeError"
      required:
        - id
        - status
      type: object
    DesignResizeJobResult:
      description: |-
        Design has been created and saved to user's root
        ([projects](https://www.canva.com/help/find-designs-and-folders/)) folder.
      properties:
        design:
          $ref: "#/components/schemas/DesignSummary"
      required:
        - design
      type: object
    DesignResizeStatus:
      description: Status of the design resize job.
      enum:
        - in_progress
        - success
        - failed
      example: success
      type: string
    DesignResizeErrorCode:
      enum:
        - thumbnail_generation_error
        - design_resize_error
        - create_design_error
      type: string
    DesignResizeError:
      description:
        "If the design resize job fails, this object provides details about\
        \ the error."
      properties:
        code:
          $ref: "#/components/schemas/DesignResizeErrorCode"
        message:
          description: A human-readable description of what went wrong.
          type: string
      required:
        - code
        - message
      type: object
    Team:
      description: |-
        Metadata for the Canva Team, consisting of the Team ID,
        display name, and whether it's an external Canva Team.
      properties:
        id:
          description: The ID of the Canva Team.
          example: Oi2RJILTrKk0KRhRUZozX
          type: string
        display_name:
          description: The name of the Canva Team as shown in the Canva UI.
          example: Acme Corporation
          type: string
        external:
          description: |-
            Is the user making the API call (the authenticated user) from the Canva Team shown?

            - When `true`, the user isn't in the Canva Team shown.
            - When `false`, the user is in the Canva Team shown.
          type: boolean
      required:
        - display_name
        - external
        - id
      type: object
    Thumbnail:
      description: A thumbnail image representing the object.
      properties:
        width:
          description: The width of the thumbnail image in pixels.
          example: 595
          type: integer
        height:
          description: The height of the thumbnail image in pixels.
          example: 335
          type: integer
        url:
          description: |-
            A URL for retrieving the thumbnail image.
            This URL expires after 15 minutes. This URL includes a query string
            that's required for retrieving the thumbnail.
          example: https://document-export.canva.com/Vczz9/zF9vzVtdADc/2/thumbnail/0001.png?<query-string>
          type: string
      required:
        - height
        - url
        - width
      type: object
    User:
      description: "Metadata for the user, consisting of the User ID and display name."
      properties:
        id:
          description: The ID of the user.
          example: uKakKUfI03Fg8k2gZ6OkT
          type: string
        display_name:
          description: The name of the user as shown in the Canva UI.
          example: John Doe
          type: string
      required:
        - id
      type: object
    TeamUserSummary:
      description: "Metadata for the user, consisting of the User ID and Team ID."
      properties:
        user_id:
          description: The ID of the user.
          example: auDAbliZ2rQNNOsUl5OLu
          type: string
        team_id:
          description: The ID of the user's Canva Team.
          example: Oi2RJILTrKk0KRhRUZozX
          type: string
      required:
        - team_id
        - user_id
      type: object
    TeamUser:
      description:
        "Metadata for the user, consisting of the User ID, Team ID, and\
        \ display name."
      properties:
        user_id:
          description: The ID of the user.
          example: auDAbliZ2rQNNOsUl5OLu
          type: string
        team_id:
          description: The ID of the user's Canva Team.
          example: Oi2RJILTrKk0KRhRUZozX
          type: string
        display_name:
          description: The name of the user as shown in the Canva UI.
          example: Jane Doe
          type: string
      type: object
    UserProfile:
      description:
        "Profile for the user, consisting of the display name and other\
        \ attributes."
      properties:
        display_name:
          description: The name of the user as shown in the Canva UI.
          example: Jane Doe
          type: string
      type: object
    UsersMeResponse:
      properties:
        team_user:
          $ref: "#/components/schemas/TeamUserSummary"
      required:
        - team_user
      type: object
    UserProfileResponse:
      properties:
        profile:
          $ref: "#/components/schemas/UserProfile"
      required:
        - profile
      type: object
    GetUserCapabilitiesResponse:
      properties:
        capabilities:
          items:
            $ref: "#/components/schemas/Capability"
          type: array
      type: object
    Notification:
      properties:
        id:
          description: The unique identifier for the notification.
          example: eb595730
          type: string
        created_at:
          description: |-
            When the notification was created, as a UNIX timestamp (in seconds
            since the UNIX epoch).
          example: 1377396000
          format: int64
          type: integer
        content:
          $ref: "#/components/schemas/NotificationContent"
      required:
        - content
        - created_at
        - id
      type: object
    NotificationContent:
      description:
        "The notification content object, which contains metadata about\
        \ the event."
      discriminator:
        mapping:
          share_design: "#/components/schemas/ShareDesignNotificationContent"
          share_folder: "#/components/schemas/ShareFolderNotificationContent"
          comment: "#/components/schemas/CommentNotificationContent"
          design_access_requested: "#/components/schemas/DesignAccessRequestedNotificationContent"
          design_approval_requested: "#/components/schemas/DesignApprovalRequestedNotificationContent"
          design_approval_response: "#/components/schemas/DesignApprovalResponseNotificationContent"
          design_approval_reviewer_invalidated: "#/components/schemas/DesignApprovalReviewerInvalidatedNotificationContent"
          design_mention: "#/components/schemas/DesignMentionNotificationContent"
          team_invite: "#/components/schemas/TeamInviteNotificationContent"
          folder_access_requested: "#/components/schemas/FolderAccessRequestedNotificationContent"
          suggestion: "#/components/schemas/SuggestionNotificationContent"
        propertyName: type
      oneOf:
        - $ref: "#/components/schemas/ShareDesignNotificationContent"
        - $ref: "#/components/schemas/ShareFolderNotificationContent"
        - $ref: "#/components/schemas/CommentNotificationContent"
        - $ref: "#/components/schemas/DesignAccessRequestedNotificationContent"
        - $ref: "#/components/schemas/DesignApprovalRequestedNotificationContent"
        - $ref: "#/components/schemas/DesignApprovalResponseNotificationContent"
        - $ref: "#/components/schemas/DesignApprovalReviewerInvalidatedNotificationContent"
        - $ref: "#/components/schemas/DesignMentionNotificationContent"
        - $ref: "#/components/schemas/TeamInviteNotificationContent"
        - $ref: "#/components/schemas/FolderAccessRequestedNotificationContent"
        - $ref: "#/components/schemas/SuggestionNotificationContent"
      type: object
    ShareDesignNotificationContent:
      description: The notification content for when someone shares a design.
      properties:
        type:
          enum:
            - share_design
          example: share_design
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        design:
          $ref: "#/components/schemas/DesignSummary"
        share_url:
          description:
            A URL that the user who receives the notification can use to
            access the shared design.
          example: https://www.canva.com/api/action?token=zWiz3GqRaWVkolwSgfBa9sKbsKgfHAoxv_mjs-mlX2M
          type: string
        share:
          $ref: "#/components/schemas/ShareAction"
      required:
        - design
        - receiving_team_user
        - share_url
        - triggering_user
        - type
      type: object
    ShareFolderNotificationContent:
      description: The notification content for when someone shares a folder.
      properties:
        type:
          enum:
            - share_folder
          example: share_folder
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        folder:
          $ref: "#/components/schemas/FolderSummary"
        share:
          $ref: "#/components/schemas/ShareAction"
      required:
        - folder
        - receiving_team_user
        - triggering_user
        - type
      type: object
    CommentNotificationContent:
      description: The notification content for when someone comments on a design.
      properties:
        type:
          enum:
            - comment
          example: comment
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        design:
          $ref: "#/components/schemas/DesignSummary"
        comment_url:
          deprecated: true
          description: |-
            A URL to the design, focused on the new comment.

            The `comment_url` property is deprecated.
            For details of the comment event, use the `comment_event` property instead.
          example: https://www.canva.com/design/3WCduQdjayTcPVM/z128cqanFu7E3/edit?ui=OdllGgZ4Snnq3MD8uI10bfA
          type: string
        comment:
          $ref: "#/components/schemas/CommentEventDeprecated"
        comment_event:
          $ref: "#/components/schemas/CommentEvent"
      required:
        - design
        - receiving_team_user
        - triggering_user
        - type
      type: object
    DesignAccessRequestedNotificationContent:
      description:
        The notification content for when someone requests access to a
        design.
      properties:
        type:
          enum:
            - design_access_requested
          example: design_access_requested
          type: string
        triggering_user:
          $ref: "#/components/schemas/TeamUser"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        design:
          $ref: "#/components/schemas/DesignSummary"
        grant_access_url:
          description: |-
            A URL, which is scoped only to the user that can grant the requested access to the
            design, that approves the requested access.
          example: https://www.canva.com/api/action?token=OosRN8M_eO2-QbLpUmP5JCwTMSXWfadtQYWuj9WKzoE
          type: string
      required:
        - design
        - grant_access_url
        - receiving_team_user
        - triggering_user
        - type
      type: object
    DesignApprovalRequestedNotificationContent:
      description: |-
        The notification content for when someone requests a user to
        [approve a design](https://www.canva.com/help/get-approval/).
      properties:
        type:
          enum:
            - design_approval_requested
          example: design_approval_requested
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        initial_requesting_user:
          $ref: "#/components/schemas/TeamUser"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        requested_groups:
          items:
            $ref: "#/components/schemas/Group"
          type: array
        design:
          $ref: "#/components/schemas/DesignSummary"
        approve_url:
          description: |-
            A URL, which is scoped only to the user requested to review the design, that links to
            the design with the approval UI opened.
          example: https://canva.com/api/action?token=HZb0lLHaEhNkT1qQrAwoe0-8SqyXUgJ4vnHGvN2rLZ0
          type: string
        approval_request:
          $ref: "#/components/schemas/ApprovalRequestAction"
      required:
        - approval_request
        - approve_url
        - design
        - initial_requesting_user
        - receiving_team_user
        - requested_groups
        - triggering_user
        - type
      type: object
    DesignApprovalResponseNotificationContent:
      description:
        The notification content for when someone approves a design or
        gives feedback.
      properties:
        type:
          enum:
            - design_approval_response
          example: design_approval_response
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        initial_requesting_user:
          $ref: "#/components/schemas/TeamUser"
        responding_groups:
          items:
            $ref: "#/components/schemas/Group"
          type: array
        design:
          $ref: "#/components/schemas/DesignSummary"
        approval_response:
          $ref: "#/components/schemas/ApprovalResponseAction"
      required:
        - approval_response
        - design
        - initial_requesting_user
        - receiving_team_user
        - responding_groups
        - triggering_user
        - type
      type: object
    DesignApprovalReviewerInvalidatedNotificationContent:
      description: The notification content for when a reviewer in a design is invalidated.
      properties:
        type:
          enum:
            - design_approval_reviewer_invalidated
          example: design_approval_reviewer_invalidated
          type: string
        receiving_team_user:
          $ref: "#/components/schemas/TeamUserSummary"
        design:
          $ref: "#/components/schemas/DesignSummary"
      required:
        - design
        - receiving_team_user
        - type
      type: object
    DesignMentionNotificationContent:
      description: |-
        The notification content for when someone mentions a user in a design.

        The link to the design in this notification is valid for 30 days, and can only be opened by
        the recipient of the notification.
      properties:
        type:
          enum:
            - design_mention
          example: design_mention
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        design:
          $ref: "#/components/schemas/DesignSummary"
      required:
        - design
        - receiving_team_user
        - triggering_user
        - type
      type: object
    TeamInviteNotificationContent:
      description: |-
        The notification content for when someone is invited to a
        [Canva team](https://www.canva.com/help/about-canva-for-teams/).
      properties:
        type:
          enum:
            - team_invite
          example: team_invite
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_user:
          $ref: "#/components/schemas/User"
        inviting_team:
          $ref: "#/components/schemas/Team"
      required:
        - inviting_team
        - receiving_user
        - triggering_user
        - type
      type: object
    FolderAccessRequestedNotificationContent:
      description:
        The notification content for when someone requests access to a
        folder.
      properties:
        type:
          enum:
            - folder_access_requested
          example: folder_access_requested
          type: string
        triggering_user:
          $ref: "#/components/schemas/TeamUser"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        folder:
          $ref: "#/components/schemas/FolderSummary"
      required:
        - folder
        - receiving_team_user
        - triggering_user
        - type
      type: object
    SuggestionNotificationContent:
      description: |-
        The notification content when someone does one of the following actions:

         - Suggests edits to a design.
         - Applies or rejects a suggestion.
         - Replies to a suggestion.
         - Mentions a user in a reply to a suggestion.
      properties:
        type:
          enum:
            - suggestion
          type: string
        triggering_user:
          $ref: "#/components/schemas/User"
        receiving_team_user:
          $ref: "#/components/schemas/TeamUser"
        design:
          $ref: "#/components/schemas/DesignSummary"
        suggestion_event_type:
          $ref: "#/components/schemas/SuggestionEventType"
      required:
        - design
        - receiving_team_user
        - suggestion_event_type
        - triggering_user
        - type
      type: object
    ShareAction:
      description: Metadata about the share event.
      properties:
        message:
          description: |-
            The optional message users can include when sharing something with another
            user using the Canva UI.
          example: Check this out!
          type: string
      required:
        - message
      type: object
    ApprovalRequestAction:
      description: Metadata about the design approval request.
      properties:
        message:
          description: The message included by the user when requesting a design approval.
          example: LGTM!
          type: string
      type: object
    ApprovalResponseAction:
      description: Metadata about the design approval response.
      properties:
        approved:
          description: |-
            Whether the design was approved. When `true`, the reviewer has approved
            the design.
          type: boolean
        ready_to_publish:
          description: |-
            Whether the design is ready to publish. When `true`, the design has been approved
            by all reviewers and can be published.
          type: boolean
        message:
          description:
            The message included by a user responding to a design approval
            request.
          type: string
      required:
        - approved
      type: object
  securitySchemes:
    basicAuth:
      scheme: basic
      type: http
    oauthAuthCode:
      flows:
        authorizationCode:
          authorizationUrl: https://www.canva.com/api/oauth/authorize
          scopes:
            app:read:
              "View [Canva app](https://www.canva.dev/docs/connect/canva-concepts/#canva-apps)\
              \ metadata."
            app:write:
              "Create, update, or remove [Canva apps](https://www.canva.dev/docs/connect/canva-concepts/#canva-apps)\
              \ on the user's behalf."
            design:content:read: View the contents of the user's designs.
            design:meta:read: View the metadata of the user's designs.
            design:content:write: Create designs on the user's behalf.
            design:permission:read: View the permissions assigned to a user's designs.
            design:permission:write:
              "Set, update, or remove the permissions assigned\
              \ to the user's designs."
            folder:read: |-
              View the metadata and contents of the user's folders, including their **Projects**
              folder.
            folder:write: |-
              Add, move, or remove the user's folders. It also lets you edit folder metadata,
              such as the folder's name.
            folder:permission:read: View permissions assigned to a user's folders.
            folder:permission:write:
              "Set, update, or remove permissions assigned\
              \ to the user's folders."
            asset:read:
              "View the metadata for the user's assets, such as uploaded\
              \ images."
            asset:write: "Upload, update, or delete assets on the user's behalf."
            comment:read:
              "View the comments on the user's designs, and the associated\
              \ metadata."
            comment:write: Create comments and replies on the user's designs.
            collaboration:event:
              Receive webhook notifications about events relevant
              to the user.
            brandtemplate:meta:read:
              View the metadata of the brand templates associated
              with the user's brand.
            brandtemplate:content:read:
              Read the content of the brand templates associated
              with the user's brand.
            profile:read: Read a user's profile and account information.
          tokenUrl: https://api.canva.com/rest/v1/oauth/token
      type: oauth2
