name: Continuous integration checks on public components
run-name: ${{ github.actor }} pushed a commit 🚀
on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  ecommerce_demo:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./demos/ecommerce_shop
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4
      - name: Set up node
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
      - name: NPM ci repository root
        run: cd ../.. && npm ci
      - name: Run prettier
        run: npm run format:check
      - name: Lint types
        run: npm run lint:types
      - name: ESLint
        run: npm run lint
      - name: Frontend - Run build
        run: cd frontend && npm run build
