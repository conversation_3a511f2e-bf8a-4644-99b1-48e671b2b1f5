"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.storage = exports.DatabaseStorage = exports.MemStorage = void 0;
const schema_1 = require("@shared/schema");
const db_1 = require("./db");
const drizzle_orm_1 = require("drizzle-orm");
class MemStorage {
    constructor() {
        this.users = new Map();
        this.venues = new Map();
        this.events = new Map();
        this.menuCategories = new Map();
        this.menuItems = new Map();
        this.orders = new Map();
        this.orderItems = new Map();
        this.artistMerch = new Map();
        this.queueStatus = new Map();
        this.tickets = new Map();
        this.preEventOrders = new Map();
        this.preEventOrderItems = new Map();
        this.currentId = 1;
        this.seedData();
    }
    seedData() {
        // Create test user account
        const testUser = {
            id: 1,
            email: "<EMAIL>",
            passwordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
            role: "guest",
            firstName: "Test",
            lastName: "User",
            username: "testuser",
            isGuest: false,
            createdAt: new Date()
        };
        this.users.set(1, testUser);
        // Create sample venue
        const venue = {
            id: 1,
            name: "Madison Square Garden",
            address: "4 Pennsylvania Plaza, New York, NY 10001",
            wifiSSID: "MSG_Guest",
            isActive: true
        };
        this.venues.set(1, venue);
        // Create sample event
        const event = {
            id: 1,
            venueId: 1,
            name: "Arctic Monkeys - Live Concert",
            startTime: new Date("2024-12-20T20:00:00Z"),
            endTime: new Date("2024-12-20T23:00:00Z"),
            orderCutoff: new Date("2024-12-20T21:45:00Z"),
            isActive: true
        };
        this.events.set(1, event);
        // Create menu categories
        const categories = [
            { id: 1, name: "Food", type: "food", eventId: 1 },
            { id: 2, name: "Drinks", type: "drinks", eventId: 1 },
            { id: 3, name: "Merchandise", type: "merch", eventId: 1 }
        ];
        categories.forEach(cat => this.menuCategories.set(cat.id, cat));
        // Create menu items
        const items = [
            {
                id: 1,
                categoryId: 1,
                name: "Venue Burger",
                description: "Premium beef patty with local fixings and crispy fries",
                price: "14.99",
                stock: 23,
                prepTime: 8,
                imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd",
                isAvailable: true
            },
            {
                id: 2,
                categoryId: 1,
                name: "Venue Special Pizza",
                description: "Wood-fired pizza with locally sourced ingredients",
                price: "18.99",
                stock: 12,
                prepTime: 12,
                imageUrl: "https://images.unsplash.com/photo-1513104890138-7c749659a591",
                isAvailable: true
            },
            {
                id: 3,
                categoryId: 2,
                name: "Craft Beer Flight",
                description: "Selection of 4 local craft beers to sample",
                price: "16.99",
                stock: 5,
                prepTime: 3,
                imageUrl: "https://images.unsplash.com/photo-1608270586620-248524c67de9",
                isAvailable: true
            },
            {
                id: 4,
                categoryId: 3,
                name: "Arctic Monkeys Tour T-Shirt",
                description: "Official 2024 tour merchandise",
                price: "29.99",
                stock: 45,
                prepTime: 2,
                imageUrl: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab",
                isAvailable: true
            }
        ];
        items.forEach(item => this.menuItems.set(item.id, item));
        // Initialize queue status
        const queueStatus = {
            id: 1,
            eventId: 1,
            preparing: 7,
            ready: 3,
            avgWaitTime: 12,
            updatedAt: new Date()
        };
        this.queueStatus.set(1, queueStatus);
        this.currentId = 5;
    }
    async getUser(id) {
        return this.users.get(id);
    }
    async getUserByEmail(email) {
        return Array.from(this.users.values()).find(user => user.email === email);
    }
    async getUserByUsername(username) {
        return Array.from(this.users.values()).find(user => user.username === username);
    }
    async createUser(insertUser) {
        const id = this.currentId++;
        const user = {
            id,
            createdAt: new Date(),
            role: insertUser.role ?? null,
            email: insertUser.email ?? null,
            phone: insertUser.phone ?? null,
            username: insertUser.username ?? null,
            firstName: insertUser.firstName ?? null,
            lastName: insertUser.lastName ?? null,
            passwordHash: insertUser.passwordHash ?? null,
            isGuest: insertUser.isGuest ?? null
        };
        this.users.set(id, user);
        return user;
    }
    async updateUser(id, updates) {
        const user = this.users.get(id);
        if (!user)
            return undefined;
        const updatedUser = {
            ...user,
            ...updates,
            role: updates.role !== undefined ? updates.role : user.role,
            email: updates.email !== undefined ? updates.email : user.email,
            phone: updates.phone !== undefined ? updates.phone : user.phone,
            username: updates.username !== undefined ? updates.username : user.username,
            firstName: updates.firstName !== undefined ? updates.firstName : user.firstName,
            lastName: updates.lastName !== undefined ? updates.lastName : user.lastName,
            passwordHash: updates.passwordHash !== undefined ? updates.passwordHash : user.passwordHash,
            isGuest: updates.isGuest !== undefined ? updates.isGuest : user.isGuest
        };
        this.users.set(id, updatedUser);
        return updatedUser;
    }
    async getVenue(id) {
        return this.venues.get(id);
    }
    async getActiveVenues() {
        return Array.from(this.venues.values()).filter(venue => venue.isActive);
    }
    async createVenue(insertVenue) {
        const id = this.currentId++;
        const venue = { ...insertVenue, id };
        this.venues.set(id, venue);
        return venue;
    }
    async getEvent(id) {
        return this.events.get(id);
    }
    async getActiveEvents() {
        return Array.from(this.events.values()).filter(event => event.isActive);
    }
    async getEventsByVenue(venueId) {
        return Array.from(this.events.values()).filter(event => event.venueId === venueId);
    }
    async createEvent(insertEvent) {
        const id = this.currentId++;
        const event = { ...insertEvent, id };
        this.events.set(id, event);
        return event;
    }
    async getMenuCategories(eventId) {
        return Array.from(this.menuCategories.values()).filter(cat => cat.eventId === eventId);
    }
    async getMenuItems(categoryId) {
        return Array.from(this.menuItems.values()).filter(item => item.categoryId === categoryId);
    }
    async getMenuItem(id) {
        return this.menuItems.get(id);
    }
    async createMenuItem(insertItem) {
        const id = this.currentId++;
        const item = { ...insertItem, id };
        this.menuItems.set(id, item);
        return item;
    }
    async updateMenuItemStock(id, stock) {
        const item = this.menuItems.get(id);
        if (item) {
            const updatedItem = { ...item, stock };
            this.menuItems.set(id, updatedItem);
            return updatedItem;
        }
        return undefined;
    }
    async getOrder(id) {
        return this.orders.get(id);
    }
    async getOrdersByUser(userId) {
        return Array.from(this.orders.values()).filter(order => order.userId === userId);
    }
    async getOrdersByEvent(eventId) {
        return Array.from(this.orders.values()).filter(order => order.eventId === eventId);
    }
    async createOrder(insertOrder) {
        const id = this.currentId++;
        const order = {
            ...insertOrder,
            id,
            createdAt: new Date(),
            updatedAt: new Date(),
            qrCode: `VF-${new Date().getFullYear()}-${id.toString().padStart(4, '0')}`
        };
        this.orders.set(id, order);
        return order;
    }
    async updateOrderStatus(id, status) {
        const order = this.orders.get(id);
        if (order) {
            const updatedOrder = { ...order, status, updatedAt: new Date() };
            this.orders.set(id, updatedOrder);
            return updatedOrder;
        }
        return undefined;
    }
    async getOrderItems(orderId) {
        return Array.from(this.orderItems.values()).filter(item => item.orderId === orderId);
    }
    async createOrderItem(insertItem) {
        const id = this.currentId++;
        const item = { ...insertItem, id };
        this.orderItems.set(id, item);
        return item;
    }
    async getArtistMerch(artistId, eventId) {
        return Array.from(this.artistMerch.values()).filter(merch => merch.artistId === artistId && merch.eventId === eventId);
    }
    async getArtistMerchItem(id) {
        return this.artistMerch.get(id);
    }
    async getEventMerch(eventId) {
        return Array.from(this.artistMerch.values()).filter(merch => merch.eventId === eventId);
    }
    async createArtistMerch(insertMerch) {
        const id = this.currentId++;
        const merch = { ...insertMerch, id };
        this.artistMerch.set(id, merch);
        return merch;
    }
    async updateMerchAvailability(id, isAvailable, stock) {
        const merch = this.artistMerch.get(id);
        if (!merch)
            return undefined;
        const updatedMerch = {
            ...merch,
            isAvailable,
            ...(stock !== undefined && { stock })
        };
        this.artistMerch.set(id, updatedMerch);
        return updatedMerch;
    }
    async getQueueStatus(eventId) {
        return Array.from(this.queueStatus.values()).find(status => status.eventId === eventId);
    }
    async updateQueueStatus(eventId, insertStatus) {
        const existingStatus = await this.getQueueStatus(eventId);
        if (existingStatus) {
            const updatedStatus = { ...existingStatus, ...insertStatus, updatedAt: new Date() };
            this.queueStatus.set(existingStatus.id, updatedStatus);
            return updatedStatus;
        }
        else {
            const id = this.currentId++;
            const status = { ...insertStatus, id, eventId, updatedAt: new Date() };
            this.queueStatus.set(id, status);
            return status;
        }
    }
    async getTickets(eventId) {
        return Array.from(this.tickets.values()).filter(ticket => ticket.eventId === eventId);
    }
    async createTicket(insertTicket) {
        const id = this.currentId++;
        const ticket = { ...insertTicket, id };
        this.tickets.set(id, ticket);
        return ticket;
    }
    async updateTicketAvailability(id, availableQuantity) {
        const ticket = this.tickets.get(id);
        if (ticket) {
            const updatedTicket = { ...ticket, availableQuantity };
            this.tickets.set(id, updatedTicket);
            return updatedTicket;
        }
        return undefined;
    }
    async getPreEventOrder(id) {
        return this.preEventOrders.get(id);
    }
    async getPreEventOrdersByUser(userId) {
        return Array.from(this.preEventOrders.values()).filter(order => order.userId === userId);
    }
    async createPreEventOrder(insertOrder) {
        const id = this.currentId++;
        const order = { ...insertOrder, id, createdAt: new Date() };
        this.preEventOrders.set(id, order);
        return order;
    }
    async updatePreEventOrder(id, updates) {
        const order = this.preEventOrders.get(id);
        if (order) {
            const updatedOrder = { ...order, ...updates };
            this.preEventOrders.set(id, updatedOrder);
            return updatedOrder;
        }
        return undefined;
    }
    async canModifyPreEventOrder(id) {
        const order = this.preEventOrders.get(id);
        if (!order)
            return false;
        // Example logic: allow modification if order is not too close to event time
        const now = new Date();
        const cutoffTime = new Date(order.pickupTime.getTime() - 30 * 60 * 1000); // 30 mins before pickup
        return now < cutoffTime;
    }
    async getPreEventOrderItems(preEventOrderId) {
        return Array.from(this.preEventOrderItems.values()).filter(item => item.preEventOrderId === preEventOrderId);
    }
    async createPreEventOrderItem(insertItem) {
        const id = this.currentId++;
        const item = { ...insertItem, id };
        this.preEventOrderItems.set(id, item);
        return item;
    }
    async updatePreEventOrderItem(id, updates) {
        const item = this.preEventOrderItems.get(id);
        if (item) {
            const updatedItem = { ...item, ...updates };
            this.preEventOrderItems.set(id, updatedItem);
            return updatedItem;
        }
        return undefined;
    }
}
exports.MemStorage = MemStorage;
// Database Storage Implementation
class DatabaseStorage {
    async getUser(id) {
        const [user] = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.id, id));
        return user || undefined;
    }
    async getUserByEmail(email) {
        const [user] = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.email, email));
        return user || undefined;
    }
    async getUserByUsername(username) {
        const [user] = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.username, username));
        return user || undefined;
    }
    async createUser(insertUser) {
        const [user] = await db_1.db
            .insert(schema_1.users)
            .values(insertUser)
            .returning();
        return user;
    }
    async updateUser(id, updates) {
        const [user] = await db_1.db
            .update(schema_1.users)
            .set(updates)
            .where((0, drizzle_orm_1.eq)(schema_1.users.id, id))
            .returning();
        return user || undefined;
    }
    async getVenue(id) {
        const [venue] = await db_1.db.select().from(schema_1.venues).where((0, drizzle_orm_1.eq)(schema_1.venues.id, id));
        return venue || undefined;
    }
    async getActiveVenues() {
        return await db_1.db.select().from(schema_1.venues).where((0, drizzle_orm_1.eq)(schema_1.venues.isActive, true));
    }
    async createVenue(insertVenue) {
        const [venue] = await db_1.db
            .insert(schema_1.venues)
            .values(insertVenue)
            .returning();
        return venue;
    }
    async getEvent(id) {
        const [event] = await db_1.db.select().from(schema_1.events).where((0, drizzle_orm_1.eq)(schema_1.events.id, id));
        return event || undefined;
    }
    async getActiveEvents() {
        return await db_1.db.select().from(schema_1.events).where((0, drizzle_orm_1.eq)(schema_1.events.isActive, true));
    }
    async getEventsByVenue(venueId) {
        return await db_1.db.select().from(schema_1.events).where((0, drizzle_orm_1.eq)(schema_1.events.venueId, venueId));
    }
    async createEvent(insertEvent) {
        const [event] = await db_1.db
            .insert(schema_1.events)
            .values(insertEvent)
            .returning();
        return event;
    }
    async getMenuCategories(eventId) {
        return await db_1.db.select().from(schema_1.menuCategories).where((0, drizzle_orm_1.eq)(schema_1.menuCategories.eventId, eventId));
    }
    async getMenuItems(categoryId) {
        return await db_1.db.select().from(schema_1.menuItems).where((0, drizzle_orm_1.eq)(schema_1.menuItems.categoryId, categoryId));
    }
    async getMenuItem(id) {
        const [item] = await db_1.db.select().from(schema_1.menuItems).where((0, drizzle_orm_1.eq)(schema_1.menuItems.id, id));
        return item || undefined;
    }
    async createMenuItem(insertItem) {
        const [item] = await db_1.db
            .insert(schema_1.menuItems)
            .values(insertItem)
            .returning();
        return item;
    }
    async updateMenuItemStock(id, stock) {
        const [item] = await db_1.db
            .update(schema_1.menuItems)
            .set({ stock })
            .where((0, drizzle_orm_1.eq)(schema_1.menuItems.id, id))
            .returning();
        return item || undefined;
    }
    async getOrder(id) {
        const [order] = await db_1.db.select().from(schema_1.orders).where((0, drizzle_orm_1.eq)(schema_1.orders.id, id));
        return order || undefined;
    }
    async getOrdersByUser(userId) {
        return await db_1.db.select().from(schema_1.orders).where((0, drizzle_orm_1.eq)(schema_1.orders.userId, userId));
    }
    async getOrdersByEvent(eventId) {
        return await db_1.db.select().from(schema_1.orders).where((0, drizzle_orm_1.eq)(schema_1.orders.eventId, eventId));
    }
    async createOrder(insertOrder) {
        const [order] = await db_1.db
            .insert(schema_1.orders)
            .values(insertOrder)
            .returning();
        return order;
    }
    async updateOrderStatus(id, status) {
        const [order] = await db_1.db
            .update(schema_1.orders)
            .set({ status, updatedAt: new Date() })
            .where((0, drizzle_orm_1.eq)(schema_1.orders.id, id))
            .returning();
        return order || undefined;
    }
    async getOrderItems(orderId) {
        return await db_1.db.select().from(schema_1.orderItems).where((0, drizzle_orm_1.eq)(schema_1.orderItems.orderId, orderId));
    }
    async createOrderItem(insertItem) {
        const [item] = await db_1.db
            .insert(schema_1.orderItems)
            .values(insertItem)
            .returning();
        return item;
    }
    async getArtistMerch(artistId, eventId) {
        return await db_1.db
            .select()
            .from(schema_1.artistMerch)
            .where((0, drizzle_orm_1.and)((0, drizzle_orm_1.eq)(schema_1.artistMerch.artistId, artistId), (0, drizzle_orm_1.eq)(schema_1.artistMerch.eventId, eventId)));
    }
    async getArtistMerchItem(id) {
        const [merch] = await db_1.db.select().from(schema_1.artistMerch).where((0, drizzle_orm_1.eq)(schema_1.artistMerch.id, id));
        return merch || undefined;
    }
    async getEventMerch(eventId) {
        return await db_1.db
            .select()
            .from(schema_1.artistMerch)
            .where((0, drizzle_orm_1.eq)(schema_1.artistMerch.eventId, eventId));
    }
    async createArtistMerch(insertMerch) {
        const [merch] = await db_1.db
            .insert(schema_1.artistMerch)
            .values(insertMerch)
            .returning();
        return merch;
    }
    async updateMerchAvailability(id, isAvailable, stock) {
        const updateData = { isAvailable };
        if (stock !== undefined) {
            updateData.stock = stock;
        }
        const [merch] = await db_1.db
            .update(schema_1.artistMerch)
            .set(updateData)
            .where((0, drizzle_orm_1.eq)(schema_1.artistMerch.id, id))
            .returning();
        return merch || undefined;
    }
    async getQueueStatus(eventId) {
        const [status] = await db_1.db.select().from(schema_1.queueStatus).where((0, drizzle_orm_1.eq)(schema_1.queueStatus.eventId, eventId));
        return status || undefined;
    }
    async updateQueueStatus(eventId, insertStatus) {
        const existing = await this.getQueueStatus(eventId);
        if (existing) {
            const [status] = await db_1.db
                .update(schema_1.queueStatus)
                .set({ ...insertStatus, updatedAt: new Date() })
                .where((0, drizzle_orm_1.eq)(schema_1.queueStatus.eventId, eventId))
                .returning();
            return status;
        }
        else {
            const [status] = await db_1.db
                .insert(schema_1.queueStatus)
                .values({ ...insertStatus, eventId, updatedAt: new Date() })
                .returning();
            return status;
        }
    }
    // Tickets
    async getTickets(eventId) {
        throw new Error("Method not implemented.");
    }
    async createTicket(ticket) {
        throw new Error("Method not implemented.");
    }
    async updateTicketAvailability(id, availableQuantity) {
        throw new Error("Method not implemented.");
    }
    // Pre-Event Orders
    async getPreEventOrder(id) {
        throw new Error("Method not implemented.");
    }
    async getPreEventOrdersByUser(userId) {
        throw new Error("Method not implemented.");
    }
    async createPreEventOrder(order) {
        throw new Error("Method not implemented.");
    }
    async updatePreEventOrder(id, updates) {
        throw new Error("Method not implemented.");
    }
    async canModifyPreEventOrder(id) {
        throw new Error("Method not implemented.");
    }
    // Pre-Event Order Items
    async getPreEventOrderItems(preEventOrderId) {
        throw new Error("Method not implemented.");
    }
    async createPreEventOrderItem(item) {
        throw new Error("Method not implemented.");
    }
    async updatePreEventOrderItem(id, updates) {
        throw new Error("Method not implemented.");
    }
}
exports.DatabaseStorage = DatabaseStorage;
exports.storage = new DatabaseStorage();
