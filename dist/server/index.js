"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = require("dotenv");
const venues_1 = __importDefault(require("./routes/venues"));
const events_1 = __importDefault(require("./routes/events"));
const orders_1 = __importDefault(require("./routes/orders"));
const auth_1 = __importDefault(require("./routes/auth")); // Import auth routes
// (If needed, import loyaltyRouter, etc., if we create separate routers for those)
(0, dotenv_1.config)(); // load .env variables
const app = (0, express_1.default)();
app.use((0, cors_1.default)());
app.use(express_1.default.json()); // parse JSON request bodies
// Mount API routers
app.use('/api/venues', venues_1.default);
app.use('/api/events', events_1.default);
app.use('/api/orders', orders_1.default);
app.use('/api/auth', auth_1.default); // Mount auth routes
// (We might have /api/loyalty or /api/analytics routes in future as well)
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
