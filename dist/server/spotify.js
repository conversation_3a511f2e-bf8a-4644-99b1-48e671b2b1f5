"use strict";
// =============================================================================
// SPOTIFY WEB API INTEGRATION
// =============================================================================
// This module handles all Spotify API interactions for artist data, tracks,
// and event information for the venue preorder platform.
//
// 10th Grade Level: This connects to Spotify to get information about artists,
// their songs, and concerts so we can show the right merchandise for each event.
//
// College Level: OAuth 2.0 integration with Spotify Web API for artist profile
// data, top tracks, and event information with proper token management.
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSpotifyAuthUrl = getSpotifyAuthUrl;
exports.exchangeCodeForTokens = exchangeCodeForTokens;
exports.refreshAccessToken = refreshAccessToken;
exports.getUserProfile = getUserProfile;
exports.searchArtists = searchArtists;
exports.getArtist = getArtist;
exports.getArtistTopTracks = getArtistTopTracks;
exports.getUserTopArtists = getUserTopArtists;
exports.generateMockEventData = generateMockEventData;
exports.getClientCredentialsToken = getClientCredentialsToken;
const spotify_web_api_node_1 = __importDefault(require("spotify-web-api-node"));
// Validate required environment variables
if (!process.env.SPOTIFY_CLIENT_ID || !process.env.SPOTIFY_CLIENT_SECRET) {
    throw new Error('Missing required Spotify credentials: SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET');
}
// Initialize Spotify API client
const spotifyApi = new spotify_web_api_node_1.default({
    clientId: process.env.SPOTIFY_CLIENT_ID,
    clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
    redirectUri: process.env.SPOTIFY_REDIRECT_URI || 'http://localhost:5000/api/auth/spotify/callback'
});
// =============================================================================
// SPOTIFY AUTHENTICATION
// =============================================================================
/**
 * Generate Spotify OAuth authorization URL
 * @param state - Random state parameter for security
 * @returns Authorization URL for user to visit
 */
function getSpotifyAuthUrl(state) {
    const scopes = [
        'user-read-private',
        'user-read-email',
        'user-top-read',
        'user-read-recently-played'
    ];
    return spotifyApi.createAuthorizeURL(scopes, state);
}
/**
 * Exchange authorization code for access token
 * @param code - Authorization code from Spotify callback
 * @returns Access token and refresh token
 */
async function exchangeCodeForTokens(code) {
    try {
        const data = await spotifyApi.authorizationCodeGrant(code);
        // Set the access token on the API object
        spotifyApi.setAccessToken(data.body.access_token);
        spotifyApi.setRefreshToken(data.body.refresh_token);
        return {
            accessToken: data.body.access_token,
            refreshToken: data.body.refresh_token,
            expiresIn: data.body.expires_in
        };
    }
    catch (error) {
        console.error('Error exchanging code for tokens:', error);
        throw new Error('Failed to authenticate with Spotify');
    }
}
/**
 * Refresh an expired access token
 * @param refreshToken - Refresh token from previous authentication
 * @returns New access token
 */
async function refreshAccessToken(refreshToken) {
    try {
        spotifyApi.setRefreshToken(refreshToken);
        const data = await spotifyApi.refreshAccessToken();
        spotifyApi.setAccessToken(data.body.access_token);
        return {
            accessToken: data.body.access_token,
            expiresIn: data.body.expires_in
        };
    }
    catch (error) {
        console.error('Error refreshing access token:', error);
        throw new Error('Failed to refresh Spotify token');
    }
}
// =============================================================================
// SPOTIFY DATA FETCHING
// =============================================================================
/**
 * Get current user's Spotify profile
 * @param accessToken - Valid Spotify access token
 * @returns User profile data
 */
async function getUserProfile(accessToken) {
    try {
        spotifyApi.setAccessToken(accessToken);
        const data = await spotifyApi.getMe();
        return {
            id: data.body.id,
            displayName: data.body.display_name,
            email: data.body.email,
            images: data.body.images,
            followers: data.body.followers?.total || 0,
            country: data.body.country
        };
    }
    catch (error) {
        console.error('Error fetching user profile:', error);
        throw new Error('Failed to fetch Spotify profile');
    }
}
/**
 * Search for artists on Spotify
 * @param query - Artist name to search for
 * @param accessToken - Valid Spotify access token
 * @returns Array of artist results
 */
async function searchArtists(query, accessToken) {
    try {
        spotifyApi.setAccessToken(accessToken);
        const data = await spotifyApi.searchArtists(query, { limit: 20 });
        return data.body.artists?.items.map(artist => ({
            id: artist.id,
            name: artist.name,
            images: artist.images,
            genres: artist.genres,
            popularity: artist.popularity,
            followers: artist.followers.total,
            externalUrls: artist.external_urls
        })) || [];
    }
    catch (error) {
        console.error('Error searching artists:', error);
        throw new Error('Failed to search artists');
    }
}
/**
 * Get artist details by Spotify ID
 * @param artistId - Spotify artist ID
 * @param accessToken - Valid Spotify access token
 * @returns Artist details
 */
async function getArtist(artistId, accessToken) {
    try {
        spotifyApi.setAccessToken(accessToken);
        const data = await spotifyApi.getArtist(artistId);
        return {
            id: data.body.id,
            name: data.body.name,
            images: data.body.images,
            genres: data.body.genres,
            popularity: data.body.popularity,
            followers: data.body.followers.total,
            externalUrls: data.body.external_urls
        };
    }
    catch (error) {
        console.error('Error fetching artist:', error);
        throw new Error('Failed to fetch artist details');
    }
}
/**
 * Get artist's top tracks
 * @param artistId - Spotify artist ID
 * @param accessToken - Valid Spotify access token
 * @param country - Country code for market (default: US)
 * @returns Array of top tracks
 */
async function getArtistTopTracks(artistId, accessToken, country = 'US') {
    try {
        spotifyApi.setAccessToken(accessToken);
        const data = await spotifyApi.getArtistTopTracks(artistId, country);
        return data.body.tracks.map(track => ({
            id: track.id,
            name: track.name,
            album: {
                id: track.album.id,
                name: track.album.name,
                images: track.album.images,
                releaseDate: track.album.release_date
            },
            artists: track.artists.map(artist => ({
                id: artist.id,
                name: artist.name
            })),
            duration: track.duration_ms,
            popularity: track.popularity,
            previewUrl: track.preview_url,
            externalUrls: track.external_urls
        }));
    }
    catch (error) {
        console.error('Error fetching artist top tracks:', error);
        throw new Error('Failed to fetch artist top tracks');
    }
}
/**
 * Get user's top artists
 * @param accessToken - Valid Spotify access token
 * @param timeRange - Time range for top artists (short_term, medium_term, long_term)
 * @param limit - Number of artists to return (max 50)
 * @returns Array of top artists
 */
async function getUserTopArtists(accessToken, timeRange = 'medium_term', limit = 20) {
    try {
        spotifyApi.setAccessToken(accessToken);
        const data = await spotifyApi.getMyTopArtists({
            time_range: timeRange,
            limit
        });
        return data.body.items.map(artist => ({
            id: artist.id,
            name: artist.name,
            images: artist.images,
            genres: artist.genres,
            popularity: artist.popularity,
            followers: artist.followers.total
        }));
    }
    catch (error) {
        console.error('Error fetching user top artists:', error);
        throw new Error('Failed to fetch top artists');
    }
}
// =============================================================================
// MOCK EVENT DATA GENERATION
// =============================================================================
/**
 * Generate mock event data for an artist
 * This would typically come from a ticketing API, but we'll generate realistic data
 * @param artist - Artist data from Spotify
 * @returns Mock event data
 */
function generateMockEventData(artist) {
    const venues = [
        'Madison Square Garden',
        'The Forum',
        'Red Rocks Amphitheatre',
        'Hollywood Bowl',
        'The Fillmore',
        'House of Blues',
        'Terminal 5',
        'The Wiltern'
    ];
    const cities = [
        'New York, NY',
        'Los Angeles, CA',
        'Chicago, IL',
        'Austin, TX',
        'Nashville, TN',
        'Seattle, WA',
        'Boston, MA',
        'Denver, CO'
    ];
    // Generate 1-3 upcoming events for this artist
    const eventCount = Math.floor(Math.random() * 3) + 1;
    const events = [];
    for (let i = 0; i < eventCount; i++) {
        const venue = venues[Math.floor(Math.random() * venues.length)];
        const city = cities[Math.floor(Math.random() * cities.length)];
        const daysFromNow = Math.floor(Math.random() * 90) + 7; // 7-97 days from now
        const eventDate = new Date();
        eventDate.setDate(eventDate.getDate() + daysFromNow);
        events.push({
            id: `event_${artist.id}_${i}`,
            artistId: artist.id,
            artistName: artist.name,
            venue: venue,
            city: city,
            date: eventDate.toISOString(),
            ticketPrice: Math.floor(Math.random() * 100) + 25, // $25-$125
            availableTickets: Math.floor(Math.random() * 500) + 100, // 100-600 tickets
            description: `${artist.name} live at ${venue}`,
            image: artist.images?.[0]?.url || null
        });
    }
    return events;
}
// =============================================================================
// CLIENT CREDENTIALS FLOW (FOR APP-ONLY REQUESTS)
// =============================================================================
/**
 * Get access token using client credentials flow (no user authentication required)
 * Use this for searching artists and getting public data
 * @returns Access token for app-only requests
 */
async function getClientCredentialsToken() {
    try {
        const data = await spotifyApi.clientCredentialsGrant();
        spotifyApi.setAccessToken(data.body.access_token);
        return {
            accessToken: data.body.access_token,
            expiresIn: data.body.expires_in
        };
    }
    catch (error) {
        console.error('Error getting client credentials token:', error);
        throw new Error('Failed to get Spotify app token');
    }
}
