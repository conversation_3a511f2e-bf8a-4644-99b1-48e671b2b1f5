"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.hashPassword = hashPassword;
exports.verifyPassword = verifyPassword;
exports.generateToken = generateToken;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const saltRounds = 10;
async function hashPassword(password) {
    return bcryptjs_1.default.hash(password, saltRounds);
}
async function verifyPassword(password, hash) {
    return bcryptjs_1.default.compare(password, hash);
}
function generateToken(user) {
    const secretKey = process.env.JWT_SECRET || 'your-secret-key'; // Replace with a strong secret key
    const token = jsonwebtoken_1.default.sign(user, secretKey, { expiresIn: '1h' });
    return token;
}
