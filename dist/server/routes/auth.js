"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const db_1 = require("../db");
const schema_1 = require("../../shared/schema");
const auth_1 = require("../auth");
const drizzle_orm_1 = require("drizzle-orm");
const secretKey = process.env.JWT_SECRET ?? "fallback_secret";
const router = (0, express_1.Router)();
/**
 * POST /api/auth/register
 * Registers a new user.
 */
router.post('/register', async (req, res) => {
    try {
        const userData = schema_1.insertUserSchema.parse(req.body);
        const existingUser = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.email, userData.email ?? ''));
        if (existingUser.length > 0) {
            return res.status(400).json({ message: 'Email already in use' });
        }
        const hashedPassword = await (0, auth_1.hashPassword)(userData.passwordHash);
        const newUser = await db_1.db
            .insert(schema_1.users)
            .values({ ...userData, passwordHash: hashedPassword })
            .returning();
        const token = (0, auth_1.generateToken)({ id: newUser[0].id, email: newUser[0].email, role: newUser[0].role || 'guest' });
        res.status(201).json({ token, user: newUser[0] });
    }
    catch (error) {
        console.error('Error registering user:', error);
        res.status(500).json({ message: 'Failed to register user' });
    }
});
/**
 * POST /api/auth/login
 * Logs in an existing user.
 */
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        const [user] = await db_1.db.select().from(schema_1.users).where((0, drizzle_orm_1.eq)(schema_1.users.email, email ?? ''));
        if (!user) {
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        const isValidPassword = await (0, auth_1.verifyPassword)(password, user.passwordHash ?? '');
        if (!isValidPassword) {
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        const token = (0, auth_1.generateToken)({ id: user.id, email: user.email !== null && user.email !== undefined ? user.email : '', role: user.role !== null && user.role !== undefined ? user.role : 'guest' });
        res.json({ token, user });
    }
    catch (error) {
        console.error('Error logging in user:', error);
        res.status(500).json({ message: 'Failed to login user' });
    }
});
exports.default = router;
