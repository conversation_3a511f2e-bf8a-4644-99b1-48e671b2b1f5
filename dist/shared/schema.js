"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.insertPreEventOrderItemSchema = exports.insertTicketSchema = exports.insertQueueStatusSchema = exports.insertArtistMerchSchema = exports.insertOrderItemSchema = exports.insertOrderSchema = exports.insertStaffSchedulesSchema = exports.insertInventoryForecastsSchema = exports.insertAiSettingsSchema = exports.insertAffiliatesSchema = exports.insertLoyaltyAccountsSchema = exports.insertPickupMappingsSchema = exports.insertPickupZonesSchema = exports.insertTimeWindowSchema = exports.insertPreEventOrderSchema = exports.insertMenuItemSchema = exports.insertMenuCategorySchema = exports.insertEventSchema = exports.insertVenueSettingsSchema = exports.insertVenueSchema = exports.insertUserSchema = exports.staffSchedules = exports.inventoryForecasts = exports.aiSettings = exports.affiliates = exports.loyaltyAccounts = exports.pickupMappings = exports.pickupZones = exports.timeWindows = exports.preEventOrderItems = exports.preEventOrders = exports.tickets = exports.queueStatus = exports.artistMerch = exports.orderItems = exports.orders = exports.menuItems = exports.menuCategories = exports.events = exports.venueSettings = exports.venues = exports.users = void 0;
const pg_core_1 = require("drizzle-orm/pg-core");
const drizzle_zod_1 = require("drizzle-zod");
exports.users = (0, pg_core_1.pgTable)("users", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    email: (0, pg_core_1.text)("email").unique(),
    phone: (0, pg_core_1.text)("phone"),
    username: (0, pg_core_1.text)("username"),
    firstName: (0, pg_core_1.text)("first_name"),
    lastName: (0, pg_core_1.text)("last_name"),
    role: (0, pg_core_1.text)("role").default("guest"), // guest, admin, artist
    passwordHash: (0, pg_core_1.text)("password_hash"),
    isGuest: (0, pg_core_1.boolean)("is_guest").default(false),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow(),
});
exports.venues = (0, pg_core_1.pgTable)("venues", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.text)("name").notNull(),
    address: (0, pg_core_1.text)("address").notNull(),
    wifiSSID: (0, pg_core_1.text)("wifi_ssid"),
    isActive: (0, pg_core_1.boolean)("is_active").default(true),
});
exports.venueSettings = (0, pg_core_1.pgTable)("venue_settings", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    enableMerch: (0, pg_core_1.boolean)("enable_merch").default(false).notNull(),
    enableLoyalty: (0, pg_core_1.boolean)("enable_loyalty").default(false).notNull(),
    enableAffiliate: (0, pg_core_1.boolean)("enable_affiliate").default(false).notNull(),
    enableAI: (0, pg_core_1.boolean)("enable_ai").default(false).notNull(),
    pickupMode: (0, pg_core_1.text)("pickup_mode").default("section").notNull()
});
exports.events = (0, pg_core_1.pgTable)("events", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    venueId: (0, pg_core_1.integer)("venue_id").references(() => exports.venues.id),
    name: (0, pg_core_1.text)("name").notNull(),
    startTime: (0, pg_core_1.timestamp)("start_time").notNull(),
    endTime: (0, pg_core_1.timestamp)("end_time").notNull(),
    orderCutoff: (0, pg_core_1.timestamp)("order_cutoff").notNull(),
    isActive: (0, pg_core_1.boolean)("is_active").default(true),
});
exports.menuCategories = (0, pg_core_1.pgTable)("menu_categories", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    name: (0, pg_core_1.text)("name").notNull(),
    type: (0, pg_core_1.text)("type").notNull(), // 'food', 'drinks', 'merch'
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
});
exports.menuItems = (0, pg_core_1.pgTable)("menu_items", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    categoryId: (0, pg_core_1.integer)("category_id").references(() => exports.menuCategories.id),
    name: (0, pg_core_1.text)("name").notNull(),
    description: (0, pg_core_1.text)("description"),
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
    stock: (0, pg_core_1.integer)("stock").default(0),
    prepTime: (0, pg_core_1.integer)("prep_time").default(5), // minutes
    imageUrl: (0, pg_core_1.text)("image_url"),
    isAvailable: (0, pg_core_1.boolean)("is_available").default(true),
});
exports.orders = (0, pg_core_1.pgTable)("orders", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").references(() => exports.users.id),
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
    totalAmount: (0, pg_core_1.decimal)("total_amount", { precision: 10, scale: 2 }).notNull(),
    status: (0, pg_core_1.text)("status").default("pending").notNull(), // pending, preparing, ready, picked_up, cancelled
    qrCode: (0, pg_core_1.text)("qr_code"),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow(),
    updatedAt: (0, pg_core_1.timestamp)("updated_at").defaultNow(),
});
exports.orderItems = (0, pg_core_1.pgTable)("order_items", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    orderId: (0, pg_core_1.integer)("order_id").references(() => exports.orders.id),
    menuItemId: (0, pg_core_1.integer)("menu_item_id").references(() => exports.menuItems.id),
    quantity: (0, pg_core_1.integer)("quantity").notNull(),
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
});
exports.artistMerch = (0, pg_core_1.pgTable)("artist_merch", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    artistId: (0, pg_core_1.integer)("artist_id").references(() => exports.users.id), // Assuming artists are users
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
    name: (0, pg_core_1.text)("name").notNull(),
    description: (0, pg_core_1.text)("description"),
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
    stock: (0, pg_core_1.integer)("stock").default(0),
    imageUrl: (0, pg_core_1.text)("image_url"),
    isAvailable: (0, pg_core_1.boolean)("is_available").default(true),
});
exports.queueStatus = (0, pg_core_1.pgTable)("queue_status", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
    preparing: (0, pg_core_1.integer)("preparing").default(0),
    ready: (0, pg_core_1.integer)("ready").default(0),
    avgWaitTime: (0, pg_core_1.integer)("avg_wait_time").default(0), // minutes
    updatedAt: (0, pg_core_1.timestamp)("updated_at").defaultNow(),
});
exports.tickets = (0, pg_core_1.pgTable)("tickets", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
    ticketType: (0, pg_core_1.text)("ticket_type").notNull(),
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
    totalQuantity: (0, pg_core_1.integer)("total_quantity").notNull(),
    availableQuantity: (0, pg_core_1.integer)("available_quantity").notNull(),
});
exports.preEventOrders = (0, pg_core_1.pgTable)("pre_event_orders", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").references(() => exports.users.id),
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
    orderTime: (0, pg_core_1.timestamp)("order_time").notNull(),
    pickupTime: (0, pg_core_1.timestamp)("pickup_time").notNull(),
    totalAmount: (0, pg_core_1.decimal)("total_amount", { precision: 10, scale: 2 }).notNull(),
    createdAt: (0, pg_core_1.timestamp)("created_at").defaultNow(),
});
exports.preEventOrderItems = (0, pg_core_1.pgTable)("pre_event_order_items", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    preEventOrderId: (0, pg_core_1.integer)("pre_event_order_id").references(() => exports.preEventOrders.id),
    menuItemId: (0, pg_core_1.integer)("menu_item_id").references(() => exports.menuItems.id),
    quantity: (0, pg_core_1.integer)("quantity").notNull(),
    price: (0, pg_core_1.decimal)("price", { precision: 10, scale: 2 }).notNull(),
});
exports.timeWindows = (0, pg_core_1.pgTable)("time_windows", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    eventId: (0, pg_core_1.integer)("event_id").references(() => exports.events.id),
    startTime: (0, pg_core_1.timestamp)("start_time").notNull(),
    endTime: (0, pg_core_1.timestamp)("end_time").notNull(),
    capacity: (0, pg_core_1.integer)("capacity").notNull(),
});
exports.pickupZones = (0, pg_core_1.pgTable)("pickup_zones", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    name: (0, pg_core_1.text)("name").notNull(),
    description: (0, pg_core_1.text)("description")
});
exports.pickupMappings = (0, pg_core_1.pgTable)("pickup_mappings", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    pickupZoneId: (0, pg_core_1.integer)("pickup_zone_id").notNull().references(() => exports.pickupZones.id, { onDelete: 'cascade' }),
    section: (0, pg_core_1.text)("section").notNull(),
    rowStart: (0, pg_core_1.integer)("row_start"),
    rowEnd: (0, pg_core_1.integer)("row_end")
});
exports.loyaltyAccounts = (0, pg_core_1.pgTable)("loyalty_accounts", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    userId: (0, pg_core_1.integer)("user_id").references(() => exports.users.id, { onDelete: 'cascade' }),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    points: (0, pg_core_1.integer)("points").notNull().default(0),
    tier: (0, pg_core_1.text)("tier"),
    updatedAt: (0, pg_core_1.timestamp)("updated_at").defaultNow().notNull()
});
exports.affiliates = (0, pg_core_1.pgTable)("affiliates", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    code: (0, pg_core_1.text)("code").notNull().unique(),
    description: (0, pg_core_1.text)("description"),
    discountPercent: (0, pg_core_1.integer)("discount_percent"),
    commissionPercent: (0, pg_core_1.integer)("commission_percent")
});
exports.aiSettings = (0, pg_core_1.pgTable)("ai_settings", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    enableInventoryForecast: (0, pg_core_1.boolean)("enable_inventory_forecast").default(false).notNull(),
    enableStaffScheduling: (0, pg_core_1.boolean)("enable_staff_scheduling").default(false).notNull(),
    enableAnalytics: (0, pg_core_1.boolean)("enable_analytics").default(false).notNull(),
    modelPreferences: (0, pg_core_1.text)("model_preferences")
});
exports.inventoryForecasts = (0, pg_core_1.pgTable)("inventory_forecasts", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    eventId: (0, pg_core_1.integer)("event_id").notNull().references(() => exports.events.id, { onDelete: 'cascade' }),
    itemId: (0, pg_core_1.integer)("item_id").notNull().references(() => exports.menuItems.id, { onDelete: 'cascade' }),
    forecastQuantity: (0, pg_core_1.integer)("forecast_quantity"),
    generatedAt: (0, pg_core_1.timestamp)("generated_at").defaultNow().notNull()
});
exports.staffSchedules = (0, pg_core_1.pgTable)("staff_schedules", {
    id: (0, pg_core_1.serial)("id").primaryKey(),
    eventId: (0, pg_core_1.integer)("event_id").notNull().references(() => exports.events.id, { onDelete: 'cascade' }),
    venueId: (0, pg_core_1.integer)("venue_id").notNull().references(() => exports.venues.id, { onDelete: 'cascade' }),
    recommendedStaff: (0, pg_core_1.integer)("recommended_staff"),
    schedulePlan: (0, pg_core_1.text)("schedule_plan"),
    generatedAt: (0, pg_core_1.timestamp)("generated_at").defaultNow().notNull()
});
// Zod validation schemas
exports.insertUserSchema = (0, drizzle_zod_1.createInsertSchema)(exports.users);
exports.insertVenueSchema = (0, drizzle_zod_1.createInsertSchema)(exports.venues);
exports.insertVenueSettingsSchema = (0, drizzle_zod_1.createInsertSchema)(exports.venueSettings);
exports.insertEventSchema = (0, drizzle_zod_1.createInsertSchema)(exports.events);
exports.insertMenuCategorySchema = (0, drizzle_zod_1.createInsertSchema)(exports.menuCategories);
exports.insertMenuItemSchema = (0, drizzle_zod_1.createInsertSchema)(exports.menuItems);
exports.insertPreEventOrderSchema = (0, drizzle_zod_1.createInsertSchema)(exports.preEventOrders);
exports.insertTimeWindowSchema = (0, drizzle_zod_1.createInsertSchema)(exports.timeWindows);
exports.insertPickupZonesSchema = (0, drizzle_zod_1.createInsertSchema)(exports.pickupZones);
exports.insertPickupMappingsSchema = (0, drizzle_zod_1.createInsertSchema)(exports.pickupMappings);
exports.insertLoyaltyAccountsSchema = (0, drizzle_zod_1.createInsertSchema)(exports.loyaltyAccounts);
exports.insertAffiliatesSchema = (0, drizzle_zod_1.createInsertSchema)(exports.affiliates);
exports.insertAiSettingsSchema = (0, drizzle_zod_1.createInsertSchema)(exports.aiSettings);
exports.insertInventoryForecastsSchema = (0, drizzle_zod_1.createInsertSchema)(exports.inventoryForecasts);
exports.insertStaffSchedulesSchema = (0, drizzle_zod_1.createInsertSchema)(exports.staffSchedules);
exports.insertOrderSchema = (0, drizzle_zod_1.createInsertSchema)(exports.orders);
exports.insertOrderItemSchema = (0, drizzle_zod_1.createInsertSchema)(exports.orderItems);
exports.insertArtistMerchSchema = (0, drizzle_zod_1.createInsertSchema)(exports.artistMerch);
exports.insertQueueStatusSchema = (0, drizzle_zod_1.createInsertSchema)(exports.queueStatus);
exports.insertTicketSchema = (0, drizzle_zod_1.createInsertSchema)(exports.tickets);
exports.insertPreEventOrderItemSchema = (0, drizzle_zod_1.createInsertSchema)(exports.preEventOrderItems);
