#!/bin/bash

# =============================================================================
# START DEVELOPMENT SERVER WITH NGROK
# =============================================================================
# This script starts your venue preorder platform with ngrok tunnel for Canva

set -e

echo "🚀 Starting Venue Preorder Platform with ngrok..."
echo "================================================="

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start development server in background
echo "🖥️  Starting development server on port 5000..."
npm run dev &
DEV_PID=$!

# Wait for server to start
echo "⏳ Waiting for server to start..."
sleep 5

# Check if server is running
if ! curl -s http://localhost:5000 > /dev/null; then
    echo "❌ Development server failed to start"
    kill $DEV_PID 2>/dev/null || true
    exit 1
fi

echo "✅ Development server running on http://localhost:5000"

# Start ngrok tunnel
echo "🌐 Starting ngrok tunnel..."
ngrok http 5000 &
NGROK_PID=$!

# Wait for ngrok to start
sleep 3

# Get ngrok URL
echo "🔍 Getting ngrok public URL..."
NGROK_URL=""
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | grep -o 'https://[^"]*\.ngrok-free\.app' | head -1)
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    echo "   Waiting for ngrok... ($i/10)"
    sleep 2
done

if [ -z "$NGROK_URL" ]; then
    echo "❌ Failed to get ngrok URL"
    echo "   Check ngrok status at: http://localhost:4040"
    kill $DEV_PID $NGROK_PID 2>/dev/null || true
    exit 1
fi

# Update .env file with ngrok URLs
echo "📝 Updating .env with ngrok URLs..."
if [ -f ".env" ]; then
    cp .env .env.backup
    sed -i.tmp "s|CANVA_REDIRECT_URI=.*|CANVA_REDIRECT_URI=${NGROK_URL}/api/canva/oauth/redirect|g" .env
    sed -i.tmp "s|CANVA_RETURN_NAV_URI=.*|CANVA_RETURN_NAV_URI=${NGROK_URL}/api/canva/return-nav|g" .env
    sed -i.tmp "s|CANVA_BASE_URL=.*|CANVA_BASE_URL=${NGROK_URL}|g" .env
    rm -f .env.tmp
fi

echo ""
echo "🎉 SUCCESS! Your venue preorder platform is now live!"
echo "===================================================="
echo ""
echo "🌐 Public URL: $NGROK_URL"
echo "🏠 Local URL:  http://localhost:5000"
echo "🔧 ngrok Web Interface: http://localhost:4040"
echo ""
echo "📋 CANVA INTEGRATION SETUP:"
echo "============================"
echo ""
echo "1. Go to your Canva Developer Dashboard:"
echo "   https://www.canva.dev/docs/apps/"
echo ""
echo "2. Click 'Configuration' and update these URLs:"
echo ""
echo "   📌 Authorized Redirect URL:"
echo "   $NGROK_URL/api/canva/oauth/redirect"
echo ""
echo "   📌 Return Navigation URL:"
echo "   $NGROK_URL/api/canva/return-nav"
echo ""
echo "3. Save the configuration in Canva"
echo ""
echo "🎯 READY TO TEST:"
echo "================="
echo "• Visit: $NGROK_URL"
echo "• Test Spotify login"
echo "• Test Canva design studio"
echo "• Create merchandise with Printful"
echo ""

# Cleanup function
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    kill $DEV_PID $NGROK_PID 2>/dev/null || true
    echo "✅ Services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

echo "💡 Keep this terminal open to maintain the tunnel"
echo "   Press Ctrl+C to stop all services"
echo ""
echo "🎸 Your venue preorder MVP is ready to rock! 🎵"

# Keep script running
wait
