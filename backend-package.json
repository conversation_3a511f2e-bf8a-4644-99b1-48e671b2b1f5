{"name": "venue-preorder-backend", "version": "1.0.0", "type": "module", "scripts": {"build": "npx tsc", "start": "node dist/index.js", "dev": "tsx server/index.ts"}, "dependencies": {"@neondatabase/serverless": "^0.10.4", "@supabase/supabase-js": "^2.39.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.13", "@types/spotify-web-api-node": "^5.0.11", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "connect-pg-simple": "^10.0.0", "dotenv": "^16.3.1", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "express": "^4.21.2", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^2.0.1", "openid-client": "^6.5.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "spotify-web-api-node": "^5.0.2", "stripe": "^18.2.1", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/ws": "^8.5.13", "drizzle-kit": "^0.30.4", "tsx": "^4.19.1", "typescript": "5.6.3", "esbuild": "^0.20.0"}}