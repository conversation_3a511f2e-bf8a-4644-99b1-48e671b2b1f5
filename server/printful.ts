// =============================================================================
// PRINTFUL API INTEGRATION - US FULFILLMENT SERVICE
// =============================================================================
// This module handles all Printful API interactions for product creation,
// order fulfillment, and shipping for the venue merch preorder platform.
//
// 10th Grade Level: This connects to Printful to automatically create t-shirts
// and other merchandise when artists upload designs, and ships them to customers.
//
// College Level: RESTful API integration with Printful's print-on-demand service
// for automated product creation, order processing, and fulfillment tracking.

import axios from 'axios';

// Validate required environment variables
if (!process.env.PRINTFUL_API_KEY) {
  throw new Error('Missing required Printful credentials: PRINTFUL_API_KEY');
}

// Initialize Printful API client
const printfulApi = axios.create({
  baseURL: 'https://api.printful.com',
  headers: {
    'Authorization': `Bearer ${process.env.PRINTFUL_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// =============================================================================
// PRINTFUL PRODUCT MANAGEMENT
// =============================================================================

/**
 * Get all available Printful products (t-shirts, hoodies, etc.)
 * @returns Array of available products with variants
 */
export async function getAvailableProducts() {
  try {
    const response = await printfulApi.get('/products');
    
    return response.data.result.map((product: any) => ({
      id: product.id,
      type: product.type,
      title: product.title,
      description: product.description,
      image: product.image,
      variants: product.variants || []
    }));
  } catch (error: any) {
    console.error('Error fetching Printful products:', error.response?.data || error.message);
    throw new Error('Failed to fetch available products');
  }
}

/**
 * Get detailed product information including variants and pricing
 * @param productId - Printful product ID
 * @returns Detailed product information
 */
export async function getProductDetails(productId: number) {
  try {
    const response = await printfulApi.get(`/products/${productId}`);
    
    const product = response.data.result.product;
    const variants = response.data.result.variants;
    
    return {
      id: product.id,
      type: product.type,
      title: product.title,
      description: product.description,
      image: product.image,
      variants: variants.map((variant: any) => ({
        id: variant.id,
        name: variant.name,
        size: variant.size,
        color: variant.color,
        colorCode: variant.color_code,
        image: variant.image,
        price: variant.price,
        inStock: variant.in_stock
      }))
    };
  } catch (error: any) {
    console.error('Error fetching product details:', error.response?.data || error.message);
    throw new Error('Failed to fetch product details');
  }
}

// =============================================================================
// DESIGN FILE MANAGEMENT
// =============================================================================

/**
 * Upload design file to Printful
 * @param fileBuffer - Image file buffer
 * @param fileName - Name of the file
 * @returns File information from Printful
 */
export async function uploadDesignFile(fileBuffer: Buffer, fileName: string) {
  try {
    const formData = new FormData();
    const blob = new Blob([fileBuffer]);
    formData.append('file', blob, fileName);
    
    const response = await printfulApi.post('/files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return {
      id: response.data.result.id,
      type: response.data.result.type,
      hash: response.data.result.hash,
      url: response.data.result.url,
      filename: response.data.result.filename,
      mime_type: response.data.result.mime_type,
      size: response.data.result.size,
      width: response.data.result.width,
      height: response.data.result.height
    };
  } catch (error: any) {
    console.error('Error uploading design file:', error.response?.data || error.message);
    throw new Error('Failed to upload design file');
  }
}

// =============================================================================
// STORE PRODUCT CREATION
// =============================================================================

/**
 * Create a new product in Printful store with artist design
 * @param productData - Product creation data
 * @returns Created product information
 */
export async function createStoreProduct(productData: {
  name: string;
  description: string;
  variantId: number;
  designFileId: number;
  retailPrice: number;
  artistId: string;
  eventId: string;
}) {
  try {
    const storeProductData = {
      sync_product: {
        name: productData.name,
        thumbnail: '', // Will be auto-generated
        is_ignored: false
      },
      sync_variants: [
        {
          retail_price: productData.retailPrice.toFixed(2),
          variant_id: productData.variantId,
          is_ignored: false,
          files: [
            {
              id: productData.designFileId,
              type: 'default',
              hash: '', // Will be filled by Printful
              url: '', // Will be filled by Printful
              filename: 'design.png',
              mime_type: 'image/png',
              size: 0,
              width: 0,
              height: 0,
              x: 0,
              y: 0,
              scale: 1,
              visible: true
            }
          ]
        }
      ]
    };
    
    const response = await printfulApi.post('/store/products', storeProductData);
    
    return {
      id: response.data.result.id,
      external_id: response.data.result.external_id,
      name: response.data.result.name,
      variants: response.data.result.sync_variants.map((variant: any) => ({
        id: variant.id,
        external_id: variant.external_id,
        variant_id: variant.variant_id,
        retail_price: variant.retail_price,
        sku: variant.sku,
        currency: variant.currency,
        product: variant.product,
        image: variant.files?.[0]?.preview_url || ''
      })),
      artistId: productData.artistId,
      eventId: productData.eventId
    };
  } catch (error: any) {
    console.error('Error creating store product:', error.response?.data || error.message);
    throw new Error('Failed to create store product');
  }
}

// =============================================================================
// ORDER PROCESSING
// =============================================================================

/**
 * Create order in Printful for fulfillment
 * @param orderData - Order information
 * @returns Created order details
 */
export async function createPrintfulOrder(orderData: {
  externalId: string;
  recipient: {
    name: string;
    address1: string;
    city: string;
    state_code: string;
    country_code: string;
    zip: string;
    phone?: string;
    email: string;
  };
  items: Array<{
    sync_variant_id: number;
    quantity: number;
    retail_price: number;
  }>;
  packing_slip?: {
    email: string;
    phone: string;
    message: string;
  };
}) {
  try {
    const printfulOrderData = {
      external_id: orderData.externalId,
      shipping: 'STANDARD',
      recipient: orderData.recipient,
      items: orderData.items.map(item => ({
        sync_variant_id: item.sync_variant_id,
        quantity: item.quantity,
        retail_price: item.retail_price.toFixed(2)
      })),
      packing_slip: orderData.packing_slip
    };
    
    const response = await printfulApi.post('/orders', printfulOrderData);
    
    return {
      id: response.data.result.id,
      external_id: response.data.result.external_id,
      status: response.data.result.status,
      shipping: response.data.result.shipping,
      created: response.data.result.created,
      updated: response.data.result.updated,
      recipient: response.data.result.recipient,
      items: response.data.result.items,
      costs: response.data.result.costs,
      retail_costs: response.data.result.retail_costs,
      shipments: response.data.result.shipments || []
    };
  } catch (error: any) {
    console.error('Error creating Printful order:', error.response?.data || error.message);
    throw new Error('Failed to create Printful order');
  }
}

/**
 * Get order status and tracking information
 * @param orderId - Printful order ID
 * @returns Order status and tracking details
 */
export async function getOrderStatus(orderId: number) {
  try {
    const response = await printfulApi.get(`/orders/${orderId}`);
    
    return {
      id: response.data.result.id,
      external_id: response.data.result.external_id,
      status: response.data.result.status,
      shipping: response.data.result.shipping,
      created: response.data.result.created,
      updated: response.data.result.updated,
      shipments: response.data.result.shipments?.map((shipment: any) => ({
        id: shipment.id,
        carrier: shipment.carrier,
        service: shipment.service,
        tracking_number: shipment.tracking_number,
        tracking_url: shipment.tracking_url,
        created: shipment.created,
        ship_date: shipment.ship_date,
        shipped_at: shipment.shipped_at,
        reshipment: shipment.reshipment
      })) || []
    };
  } catch (error: any) {
    console.error('Error fetching order status:', error.response?.data || error.message);
    throw new Error('Failed to fetch order status');
  }
}

// =============================================================================
// SHIPPING CALCULATIONS
// =============================================================================

/**
 * Calculate shipping costs for an order
 * @param orderData - Order data for shipping calculation
 * @returns Shipping cost information
 */
export async function calculateShipping(orderData: {
  recipient: {
    country_code: string;
    state_code?: string;
    city?: string;
    zip?: string;
  };
  items: Array<{
    variant_id: number;
    quantity: number;
  }>;
}) {
  try {
    const response = await printfulApi.post('/shipping/rates', orderData);
    
    return response.data.result.map((rate: any) => ({
      id: rate.id,
      name: rate.name,
      rate: rate.rate,
      currency: rate.currency,
      minDeliveryDays: rate.minDeliveryDays,
      maxDeliveryDays: rate.maxDeliveryDays
    }));
  } catch (error: any) {
    console.error('Error calculating shipping:', error.response?.data || error.message);
    throw new Error('Failed to calculate shipping costs');
  }
}

// =============================================================================
// WEBHOOK HANDLING
// =============================================================================

/**
 * Process Printful webhook events
 * @param webhookData - Webhook payload from Printful
 * @returns Processed webhook information
 */
export function processWebhook(webhookData: any) {
  const { type, created, retries, store, data } = webhookData;
  
  switch (type) {
    case 'order_updated':
      return {
        type: 'order_status_changed',
        orderId: data.order.id,
        externalId: data.order.external_id,
        status: data.order.status,
        shipments: data.order.shipments || []
      };
      
    case 'order_failed':
      return {
        type: 'order_failed',
        orderId: data.order.id,
        externalId: data.order.external_id,
        reason: data.reason || 'Unknown error'
      };
      
    case 'package_shipped':
      return {
        type: 'package_shipped',
        orderId: data.order.id,
        externalId: data.order.external_id,
        shipment: data.shipment
      };
      
    default:
      return {
        type: 'unknown',
        originalType: type,
        data
      };
  }
}
