import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

// Define a type for the authenticated request
export interface AuthenticatedRequest extends Request {
  user?: { id: number; email: string; role: string };
}

export const authenticate = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  // Extract token from header
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Verify token
  const secretKey = process.env.JWT_SECRET || 'your-secret-key';
  jwt.verify(token, secretKey, (err: any, decoded: any) => {
    if (err) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Add user to request
    req.user = decoded;
    next();
  });
};

export const authorize = (...roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Forbidden' });
    }

    next();
  };
};
