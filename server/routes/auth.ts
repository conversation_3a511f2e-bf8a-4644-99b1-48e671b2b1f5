import { Router, Request, Response, NextFunction } from 'express';
import { db } from '../db';
import { users, insertUserSchema } from '../../shared/schema';
import { hashPassword, verifyPassword, generateToken } from '../auth';
import { z } from 'zod';
import { eq } from 'drizzle-orm';
import jwt from 'jsonwebtoken';
import { authenticate, authorize, AuthenticatedRequest } from '../middleware/authMiddleware';

const secretKey = process.env.JWT_SECRET ?? "fallback_secret";

const router = Router();

/**
 * POST /api/auth/register
 * Registers a new user.
 */
router.post('/register', async (req: Request, res: Response) => {
  try {
    const userData = insertUserSchema.parse(req.body);
    const existingUser = await db.select().from(users).where(eq(users.email, userData.email ?? ''));

    if (existingUser.length > 0) {
      return res.status(400).json({ message: 'Email already in use' });
    }

    const hashedPassword = await hashPassword(userData.passwordHash);
    const newUser = await db
      .insert(users)
      .values({ ...userData, passwordHash: hashedPassword })
      .returning();

    const token = generateToken({ id: newUser[0].id, email: newUser[0].email!, role: newUser[0].role! || 'guest' });
    res.status(201).json({ token, user: newUser[0] });
  } catch (error: any) {
    console.error('Error registering user:', error);
    res.status(500).json({ message: 'Failed to register user' });
  }
});

/**
 * POST /api/auth/login
 * Logs in an existing user.
 */
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    const [user] = await db.select().from(users).where(eq(users.email, email ?? ''));

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const isValidPassword = await verifyPassword(password, user.passwordHash ?? '');
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const token = generateToken({ id: user.id, email: user.email !== null && user.email !== undefined ? user.email : '', role: user.role !== null && user.role !== undefined ? user.role : 'guest' });
    res.json({ token, user });
  } catch (error: any) {
    console.error('Error logging in user:', error);
    res.status(500).json({ message: 'Failed to login user' });
  }
});

export default router;
