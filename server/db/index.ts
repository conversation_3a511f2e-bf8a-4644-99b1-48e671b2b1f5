import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { Client } from 'pg';
import * as schema from '../../shared/schema';

const client = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT),
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  database: process.env.PGDATABASE,
});

client.connect();

const db = drizzle(client, { schema });

const migrateDb = async () => {
  try {
    console.log('Migrating database...');
    await migrate(db, { migrationsFolder: 'drizzle' });
    console.log('Database migrated successfully!');
  } catch (error) {
    console.error('Failed to migrate database:', error);
  }
};

migrateDb();

export { db };
