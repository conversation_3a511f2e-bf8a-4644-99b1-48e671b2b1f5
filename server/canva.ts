// =============================================================================
// CANVA CONNECT API INTEGRATION - DESIGN CREATION SERVICE
// =============================================================================
// This module handles Canva Connect API integration for OAuth authentication,
// design creation, and export functionality for the venue merch preorder platform.
//
// 10th Grade Level: This lets artists log into Canva through our app and create
// t-shirt designs, then automatically saves them for merchandise.
//
// College Level: OAuth 2.0 integration with Canva Connect API for authenticated
// design creation, asset management, and automated export to Printful pipeline.

import axios from 'axios';
import crypto from 'crypto';

// Validate required environment variables
if (!process.env.CANVA_CLIENT_ID || !process.env.CANVA_CLIENT_SECRET) {
  throw new Error('Missing required Canva credentials: CANVA_CLIENT_ID and CANVA_CLIENT_SECRET');
}

// Canva API base URL
const CANVA_API_BASE = 'https://api.canva.com/rest/v1';

// =============================================================================
// CANVA OAUTH AUTHENTICATION
// =============================================================================

/**
 * Generate Canva OAuth authorization URL
 * @param userId - Current user ID for state parameter
 * @param artistId - Artist ID for design association
 * @returns Authorization URL and state for security
 */
export function generateCanvaAuthUrl(userId: string, artistId: string) {
  // Generate secure state parameter
  const state = crypto.randomBytes(32).toString('hex');

  // Store state temporarily (in production, use Redis or database)
  // For now, we'll encode user info in the state
  const stateData = {
    userId,
    artistId,
    timestamp: Date.now(),
    nonce: state
  };

  const encodedState = Buffer.from(JSON.stringify(stateData)).toString('base64');

  const authUrl = new URL('https://www.canva.com/api/oauth/authorize');
  authUrl.searchParams.set('client_id', process.env.CANVA_CLIENT_ID!);
  authUrl.searchParams.set('redirect_uri', process.env.CANVA_REDIRECT_URI!);
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('state', encodedState);
  authUrl.searchParams.set('scope', 'design:read design:write asset:read asset:write folder:read folder:write');

  return {
    authUrl: authUrl.toString(),
    state: encodedState
  };
}

/**
 * Exchange authorization code for access token
 * @param code - Authorization code from Canva
 * @param state - State parameter for validation
 * @returns Access token and user info
 */
export async function exchangeCanvaCode(code: string, state: string) {
  try {
    // Validate and decode state
    const stateData = JSON.parse(Buffer.from(state, 'base64').toString());

    // Check state timestamp (should be within 10 minutes)
    if (Date.now() - stateData.timestamp > 10 * 60 * 1000) {
      throw new Error('State parameter expired');
    }

    // Exchange code for token
    const tokenResponse = await axios.post('https://api.canva.com/rest/v1/oauth/token', {
      grant_type: 'authorization_code',
      client_id: process.env.CANVA_CLIENT_ID,
      client_secret: process.env.CANVA_CLIENT_SECRET,
      redirect_uri: process.env.CANVA_REDIRECT_URI,
      code: code
    }, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    const { access_token, refresh_token, expires_in, scope } = tokenResponse.data;

    // Get user info from Canva
    const userResponse = await axios.get(`${CANVA_API_BASE}/users/me`, {
      headers: {
        'Authorization': `Bearer ${access_token}`
      }
    });

    return {
      accessToken: access_token,
      refreshToken: refresh_token,
      expiresIn: expires_in,
      scope: scope,
      canvaUser: userResponse.data,
      userId: stateData.userId,
      artistId: stateData.artistId
    };
  } catch (error: any) {
    console.error('Error exchanging Canva code:', error.response?.data || error.message);
    throw new Error('Failed to authenticate with Canva');
  }
}

/**
 * Refresh Canva access token
 * @param refreshToken - Refresh token from previous authentication
 * @returns New access token
 */
export async function refreshCanvaToken(refreshToken: string) {
  try {
    const response = await axios.post('https://api.canva.com/rest/v1/oauth/token', {
      grant_type: 'refresh_token',
      client_id: process.env.CANVA_CLIENT_ID,
      client_secret: process.env.CANVA_CLIENT_SECRET,
      refresh_token: refreshToken
    }, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    return {
      accessToken: response.data.access_token,
      expiresIn: response.data.expires_in,
      scope: response.data.scope
    };
  } catch (error: any) {
    console.error('Error refreshing Canva token:', error.response?.data || error.message);
    throw new Error('Failed to refresh Canva token');
  }
}

// =============================================================================
// CANVA DESIGN MANAGEMENT
// =============================================================================

/**
 * Create a new design in Canva
 * @param accessToken - Valid Canva access token
 * @param designType - Type of design (presentation, poster, etc.)
 * @param title - Design title
 * @returns Created design information
 */
export async function createCanvaDesign(accessToken: string, designType: string = 'presentation', title: string = 'Merchandise Design') {
  try {
    const response = await axios.post(`${CANVA_API_BASE}/designs`, {
      design_type: designType,
      title: title
    }, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    return {
      id: response.data.design.id,
      title: response.data.design.title,
      designType: response.data.design.design_type,
      createdAt: response.data.design.created_at,
      updatedAt: response.data.design.updated_at,
      urls: response.data.design.urls
    };
  } catch (error: any) {
    console.error('Error creating Canva design:', error.response?.data || error.message);
    throw new Error('Failed to create design in Canva');
  }
}

/**
 * Get user's designs from Canva
 * @param accessToken - Valid Canva access token
 * @param limit - Number of designs to retrieve
 * @returns Array of user's designs
 */
export async function getUserDesigns(accessToken: string, limit: number = 20) {
  try {
    const response = await axios.get(`${CANVA_API_BASE}/designs`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      },
      params: {
        limit: limit
      }
    });

    return response.data.items.map((design: any) => ({
      id: design.id,
      title: design.title,
      designType: design.design_type,
      createdAt: design.created_at,
      updatedAt: design.updated_at,
      thumbnail: design.thumbnail?.url,
      urls: design.urls
    }));
  } catch (error: any) {
    console.error('Error fetching user designs:', error.response?.data || error.message);
    throw new Error('Failed to fetch designs from Canva');
  }
}

/**
 * Export design from Canva
 * @param accessToken - Valid Canva access token
 * @param designId - Canva design ID
 * @param format - Export format (PNG, JPG, PDF)
 * @returns Export job information
 */
export async function exportCanvaDesign(accessToken: string, designId: string, format: string = 'PNG') {
  try {
    const response = await axios.post(`${CANVA_API_BASE}/designs/${designId}/export`, {
      format: {
        type: format.toLowerCase(),
        ...(format.toUpperCase() === 'PNG' && { quality: 'high', dpi: 300 })
      }
    }, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    return {
      jobId: response.data.job.id,
      status: response.data.job.status,
      designId: designId,
      format: format
    };
  } catch (error: any) {
    console.error('Error exporting Canva design:', error.response?.data || error.message);
    throw new Error('Failed to export design from Canva');
  }
}

/**
 * Get export job status and download URL
 * @param accessToken - Valid Canva access token
 * @param jobId - Export job ID
 * @returns Export job status and download URL
 */
export async function getExportJobStatus(accessToken: string, jobId: string) {
  try {
    const response = await axios.get(`${CANVA_API_BASE}/exports/${jobId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    return {
      jobId: response.data.job.id,
      status: response.data.job.status,
      result: response.data.job.result,
      downloadUrl: response.data.job.result?.url,
      error: response.data.job.error
    };
  } catch (error: any) {
    console.error('Error getting export job status:', error.response?.data || error.message);
    throw new Error('Failed to get export job status');
  }
}

// =============================================================================
// DESIGN EXPORT HANDLING
// =============================================================================

/**
 * Process design export from Canva Apps SDK
 * @param exportData - Design export data from Canva
 * @param artistId - Artist who created the design
 * @param eventId - Event the design is for
 * @returns Processed design information
 */
export async function processCanvaExport(exportData: {
  designId: string;
  exportUrl: string;
  title: string;
  format: string;
  dimensions: {
    width: number;
    height: number;
  };
}, artistId: string, eventId: string) {
  try {
    // Download the design file from Canva
    const response = await axios.get(exportData.exportUrl, {
      responseType: 'arraybuffer'
    });
    
    const designBuffer = Buffer.from(response.data);
    
    // Generate filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `canva-design-${exportData.designId}-${timestamp}.${exportData.format.toLowerCase()}`;
    
    return {
      designId: exportData.designId,
      title: exportData.title,
      filename: filename,
      buffer: designBuffer,
      format: exportData.format,
      dimensions: exportData.dimensions,
      artistId: artistId,
      eventId: eventId,
      source: 'canva',
      createdAt: new Date().toISOString()
    };
  } catch (error: any) {
    console.error('Error processing Canva export:', error.message);
    throw new Error('Failed to process design export from Canva');
  }
}

// =============================================================================
// CANVA WEBHOOK HANDLING
// =============================================================================

/**
 * Process webhooks from Canva Apps SDK
 * @param webhookData - Webhook payload from Canva
 * @returns Processed webhook information
 */
export function processCanvaWebhook(webhookData: any) {
  const { type, timestamp, data } = webhookData;
  
  switch (type) {
    case 'design.exported':
      return {
        type: 'design_exported',
        designId: data.design.id,
        exportUrl: data.export.url,
        title: data.design.title,
        format: data.export.format,
        dimensions: data.export.dimensions,
        userId: data.user.id,
        timestamp: timestamp
      };
      
    case 'design.created':
      return {
        type: 'design_created',
        designId: data.design.id,
        title: data.design.title,
        userId: data.user.id,
        timestamp: timestamp
      };
      
    case 'design.updated':
      return {
        type: 'design_updated',
        designId: data.design.id,
        title: data.design.title,
        userId: data.user.id,
        timestamp: timestamp
      };
      
    default:
      return {
        type: 'unknown',
        originalType: type,
        data: data,
        timestamp: timestamp
      };
  }
}

// =============================================================================
// DESIGN TEMPLATES
// =============================================================================

/**
 * Create predefined design templates for different merchandise types
 * @param merchType - Type of merchandise (t-shirt, hoodie, mug, etc.)
 * @returns Template configuration for Canva
 */
export function createMerchTemplate(merchType: string) {
  const templates = {
    't-shirt': {
      dimensions: { width: 3000, height: 3600 },
      printArea: { width: 2400, height: 3000 },
      safeArea: { width: 2200, height: 2800 },
      description: 'Standard t-shirt design template'
    },
    'hoodie': {
      dimensions: { width: 3000, height: 3600 },
      printArea: { width: 2400, height: 3000 },
      safeArea: { width: 2200, height: 2800 },
      description: 'Hoodie design template'
    },
    'mug': {
      dimensions: { width: 2700, height: 1080 },
      printArea: { width: 2500, height: 900 },
      safeArea: { width: 2300, height: 800 },
      description: 'Coffee mug wrap-around design'
    },
    'poster': {
      dimensions: { width: 2400, height: 3600 },
      printArea: { width: 2200, height: 3400 },
      safeArea: { width: 2000, height: 3200 },
      description: 'Concert poster design template'
    },
    'sticker': {
      dimensions: { width: 1200, height: 1200 },
      printArea: { width: 1100, height: 1100 },
      safeArea: { width: 1000, height: 1000 },
      description: 'Square sticker design template'
    }
  };
  
  const template = templates[merchType as keyof typeof templates];
  
  if (!template) {
    throw new Error(`Unsupported merchandise type: ${merchType}`);
  }
  
  return {
    type: merchType,
    ...template,
    guidelines: {
      dpi: 300,
      colorMode: 'RGB',
      fileFormat: 'PNG',
      transparentBackground: true,
      bleed: 0.125, // 1/8 inch bleed for print
      notes: [
        'Keep important elements within the safe area',
        'Use high contrast colors for better print quality',
        'Avoid very thin lines (less than 1pt)',
        'Text should be at least 8pt for readability'
      ]
    }
  };
}

// =============================================================================
// ARTIST BRAND INTEGRATION
// =============================================================================

/**
 * Generate artist-specific brand assets for Canva integration
 * @param artistData - Artist information from Spotify
 * @returns Brand configuration for Canva
 */
export function generateArtistBrand(artistData: {
  id: string;
  name: string;
  genres: string[];
  images: Array<{ url: string; width: number; height: number }>;
  primaryColor?: string;
  secondaryColor?: string;
}) {
  // Generate color palette based on artist genres
  const genreColors = {
    'rock': ['#000000', '#FF0000', '#FFFFFF'],
    'pop': ['#FF69B4', '#00BFFF', '#FFD700'],
    'hip-hop': ['#000000', '#FFD700', '#FF0000'],
    'electronic': ['#00FFFF', '#FF00FF', '#00FF00'],
    'country': ['#8B4513', '#DAA520', '#FFFFFF'],
    'jazz': ['#4B0082', '#FFD700', '#000000'],
    'classical': ['#000080', '#C0C0C0', '#FFFFFF'],
    'alternative': ['#696969', '#FF4500', '#FFFFFF']
  };
  
  // Get colors based on primary genre
  const primaryGenre = artistData.genres[0]?.toLowerCase() || 'rock';
  const colors = genreColors[primaryGenre as keyof typeof genreColors] || genreColors.rock;
  
  return {
    artist: {
      id: artistData.id,
      name: artistData.name,
      image: artistData.images[0]?.url || null
    },
    colors: [
      artistData.primaryColor || colors[0],
      artistData.secondaryColor || colors[1],
      ...colors.slice(2),
      '#000000', // Always include black
      '#FFFFFF'  // Always include white
    ],
    fonts: [
      'Impact',      // Bold, attention-grabbing
      'Arial Black', // Strong, readable
      'Helvetica',   // Clean, modern
      'Times New Roman', // Classic, elegant
      'Courier New'  // Typewriter, vintage
    ],
    templates: {
      logoPlacement: {
        corner: { x: 50, y: 50, size: 200 },
        center: { x: 1500, y: 1800, size: 400 },
        bottom: { x: 1500, y: 3200, size: 300 }
      },
      textStyles: {
        headline: { size: 72, weight: 'bold', color: colors[0] },
        subheading: { size: 48, weight: 'normal', color: colors[1] },
        body: { size: 24, weight: 'normal', color: '#000000' }
      }
    }
  };
}

// =============================================================================
// DESIGN VALIDATION
// =============================================================================

/**
 * Validate design meets print requirements
 * @param designData - Design information
 * @returns Validation result with any issues
 */
export function validateDesignForPrint(designData: {
  dimensions: { width: number; height: number };
  format: string;
  dpi?: number;
  colorMode?: string;
}) {
  const issues: string[] = [];
  const warnings: string[] = [];
  
  // Check dimensions
  if (designData.dimensions.width < 1200 || designData.dimensions.height < 1200) {
    issues.push('Design dimensions too small for quality printing (minimum 1200x1200px)');
  }
  
  // Check DPI
  if (designData.dpi && designData.dpi < 300) {
    warnings.push('DPI below 300 may result in lower print quality');
  }
  
  // Check format
  if (!['PNG', 'JPG', 'JPEG', 'PDF'].includes(designData.format.toUpperCase())) {
    issues.push('Unsupported file format for printing');
  }
  
  // Check color mode
  if (designData.colorMode && designData.colorMode !== 'RGB') {
    warnings.push('CMYK color mode recommended for print, but RGB is acceptable');
  }
  
  return {
    isValid: issues.length === 0,
    issues: issues,
    warnings: warnings,
    recommendations: [
      'Use 300 DPI for best print quality',
      'Keep important elements away from edges',
      'Use high contrast colors',
      'Test design on different background colors'
    ]
  };
}
