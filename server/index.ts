import express from 'express';
import cors from 'cors';
import { config as dotenvConfig } from 'dotenv';
import { db } from './db/index'; // Drizzle DB connection
import venuesRouter from './routes/venues';
import eventsRouter from './routes/events';
import ordersRouter from './routes/orders';
import authRouter from './routes/auth'; // Import auth routes
// (If needed, import loyaltyRouter, etc., if we create separate routers for those)
dotenvConfig(); // load .env variables

const app = express();
app.use(cors());
app.use(express.json()); // parse JSON request bodies

// Mount API routers
app.use('/api/venues', venuesRouter);
app.use('/api/events', eventsRouter);
app.use('/api/orders', ordersRouter);
app.use('/api/auth', authRouter); // Mount auth routes
// (We might have /api/loyalty or /api/analytics routes in future as well)

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
