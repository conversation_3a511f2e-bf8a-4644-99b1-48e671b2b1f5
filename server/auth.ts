import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const saltRounds = 10;

export async function hashPassword(password: string | null | undefined): Promise<string> {
  if (password === null || password === undefined) {
    throw new Error("Password cannot be null or undefined.");
  }
  return bcrypt.hash(password, saltRounds);
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}

export function generateToken(user: { id: number; email: string; role: string }): string {
  const secretKey = process.env.JWT_SECRET || 'your-secret-key'; // Replace with a strong secret key
  const token = jwt.sign(user, secretKey, { expiresIn: '1h' });
  return token;
}
