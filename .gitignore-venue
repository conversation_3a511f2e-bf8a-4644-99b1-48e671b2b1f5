# =============================================================================
# SECURITY - NEVER COMMIT THESE FILES
# =============================================================================
# Environment variables containing API keys and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Virtual environments
.venv/
venv/
env/
ENV/

# API keys and credentials
*.key
*.pem
*.p12
*.pfx
secrets/
credentials/
CREDENTIALS_BACKUP.md
*_BACKUP.md
*_CREDENTIALS.*

# Database files
*.db
*.sqlite
*.sqlite3

# =============================================================================
# BUILD & DEPENDENCIES
# =============================================================================
node_modules/
dist/
build/
.next/
.nuxt/
.cache/

# =============================================================================
# SYSTEM FILES
# =============================================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# IDE & EDITOR FILES
# =============================================================================
.vscode/
.idea/
*.swp
*.swo
*~

# =============================================================================
# LOGS & TEMPORARY FILES
# =============================================================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# =============================================================================
# PROJECT SPECIFIC
# =============================================================================
server/public
vite.config.ts.*
*.tar.gz
uploads/
temp/

# =============================================================================
# BACKUP & ARCHIVE FILES
# =============================================================================
*.bak
*.backup
*.old
*.orig
*.tmp