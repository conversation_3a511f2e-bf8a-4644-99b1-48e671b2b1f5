// =============================================================================
// ARTIST SEARCH COMPONENT
// =============================================================================
// This component allows users to search for artists on Spotify and view their
// information, top tracks, and upcoming events (mock data).
//
// 10th Grade Level: This is a search box where you can type an artist's name
// and see their information, songs, and upcoming concerts.
//
// College Level: React component with Spotify Web API integration for artist
// search, data display, and event generation with proper state management.

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  Music, 
  Calendar, 
  MapPin, 
  Users, 
  Play,
  ExternalLink,
  Shirt
} from 'lucide-react';
import { searchArtists, getArtistData, getArtistImageUrl, formatTrackDuration } from '@/lib/spotify';

interface ArtistSearchProps {
  onArtistSelect?: (artist: any) => void;
  showEvents?: boolean;
  showMerchButton?: boolean;
}

export default function ArtistSearch({ 
  onArtistSelect, 
  showEvents = true, 
  showMerchButton = false 
}: ArtistSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [selectedArtist, setSelectedArtist] = useState<any>(null);
  const [artistData, setArtistData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle artist search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setLoading(true);
      setError(null);
      
      const results = await searchArtists(searchQuery);
      setSearchResults(results);
      
      if (results.length === 0) {
        setError('No artists found. Try a different search term.');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to search artists');
    } finally {
      setLoading(false);
    }
  };

  // Handle artist selection
  const handleArtistSelect = async (artist: any) => {
    try {
      setLoading(true);
      setError(null);
      setSelectedArtist(artist);
      
      // Fetch detailed artist data including top tracks and mock events
      const data = await getArtistData(artist.id);
      setArtistData(data);
      
      // Notify parent component
      onArtistSelect?.(artist);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch artist data');
    } finally {
      setLoading(false);
    }
  };

  // Handle search on Enter key
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Artists
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              placeholder="Search for artists (e.g., Taylor Swift, Drake, Billie Eilish)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch}
              disabled={loading || !searchQuery.trim()}
            >
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && !selectedArtist && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              {searchResults.map((artist) => (
                <div
                  key={artist.id}
                  className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleArtistSelect(artist)}
                >
                  <img
                    src={getArtistImageUrl(artist.images, 'small')}
                    alt={artist.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="font-medium">{artist.name}</h3>
                    <div className="flex gap-2 mt-1">
                      <Badge variant="secondary">
                        <Users className="h-3 w-3 mr-1" />
                        {artist.followers.toLocaleString()} followers
                      </Badge>
                      <Badge variant="outline">
                        Popularity: {artist.popularity}%
                      </Badge>
                    </div>
                    {artist.genres.length > 0 && (
                      <div className="flex gap-1 mt-2">
                        {artist.genres.slice(0, 3).map((genre: string) => (
                          <Badge key={genre} variant="outline" className="text-xs">
                            {genre}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  <Button variant="outline" size="sm">
                    Select
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selected Artist Details */}
      {selectedArtist && artistData && (
        <div className="space-y-6">
          {/* Artist Header */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-start gap-6">
                <img
                  src={getArtistImageUrl(selectedArtist.images, 'large')}
                  alt={selectedArtist.name}
                  className="w-32 h-32 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h2 className="text-3xl font-bold mb-2">{selectedArtist.name}</h2>
                  <div className="flex gap-4 mb-4">
                    <Badge className="bg-green-500">
                      <Users className="h-3 w-3 mr-1" />
                      {selectedArtist.followers.toLocaleString()} followers
                    </Badge>
                    <Badge variant="outline">
                      Popularity: {selectedArtist.popularity}%
                    </Badge>
                  </div>
                  <div className="flex gap-2 mb-4">
                    {selectedArtist.genres.map((genre: string) => (
                      <Badge key={genre} variant="secondary">
                        {genre}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.open(selectedArtist.externalUrls.spotify, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View on Spotify
                    </Button>
                    {showMerchButton && (
                      <Button size="sm">
                        <Shirt className="h-4 w-4 mr-2" />
                        Create Merchandise
                      </Button>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setSelectedArtist(null);
                        setArtistData(null);
                      }}
                    >
                      Back to Search
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Tracks */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Music className="h-5 w-5" />
                Top Tracks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {artistData.topTracks.slice(0, 5).map((track: any, index: number) => (
                  <div key={track.id} className="flex items-center gap-4 p-3 border rounded-lg">
                    <span className="text-sm font-medium text-gray-500 w-6">
                      {index + 1}
                    </span>
                    <img
                      src={track.album.images[2]?.url || track.album.images[0]?.url}
                      alt={track.album.name}
                      className="w-12 h-12 rounded object-cover"
                    />
                    <div className="flex-1">
                      <p className="font-medium">{track.name}</p>
                      <p className="text-sm text-gray-500">{track.album.name}</p>
                    </div>
                    <Badge variant="outline">
                      {formatTrackDuration(track.duration)}
                    </Badge>
                    {track.previewUrl && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => window.open(track.previewUrl, '_blank')}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Mock Events */}
          {showEvents && artistData.events && artistData.events.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Upcoming Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  {artistData.events.map((event: any) => (
                    <div key={event.id} className="flex items-center gap-4 p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{event.venue}</h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                          <MapPin className="h-3 w-3" />
                          {event.city}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Calendar className="h-3 w-3" />
                          {new Date(event.date).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">${event.ticketPrice}</p>
                        <p className="text-sm text-gray-500">
                          {event.availableTickets} tickets left
                        </p>
                      </div>
                      <Button size="sm">
                        View Event
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
