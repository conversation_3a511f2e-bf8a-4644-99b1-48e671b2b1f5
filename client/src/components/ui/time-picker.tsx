import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/hooks/use-cart';
import { Clock, MapPin } from 'lucide-react';

interface TimeSlot {
  time: string;
  available: boolean;
  waitTime: string;
  status: 'available' | 'busy' | 'full';
}

interface TimePickerProps {
  onTimeSelect?: (time: string) => void;
}

export default function TimePicker({ onTimeSelect }: TimePickerProps) {
  const { pickupTime, setPickupTime } = useCart();
  
  const timeSlots: TimeSlot[] = [
    { time: '8:15 PM', available: true, waitTime: '~5min', status: 'available' },
    { time: '8:30 PM', available: true, waitTime: '~8min', status: 'available' },
    { time: '8:45 PM', available: true, waitTime: '~12min', status: 'busy' },
    { time: '9:00 PM', available: false, waitTime: 'Full', status: 'full' },
    { time: '9:15 PM', available: true, waitTime: '~15min', status: 'busy' },
    { time: '9:30 PM', available: true, waitTime: '~10min', status: 'available' },
  ];

  const handleTimeSelect = (time: string) => {
    setPickupTime(time);
    onTimeSelect?.(time);
  };

  const getSlotStyle = (slot: TimeSlot, isSelected: boolean) => {
    if (isSelected) return "bg-primary text-white border-primary";
    if (!slot.available) return "bg-gray-100 text-gray-400 cursor-not-allowed";
    if (slot.status === 'available') return "bg-white hover:bg-primary/5 border-gray-200 hover:border-primary";
    return "bg-white hover:bg-warning/5 border-gray-200 hover:border-warning";
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Clock className="h-5 w-5" />
          <span>Choose Your Pickup Time</span>
        </CardTitle>
        <p className="text-gray-600">Select a 15-minute window that works for you</p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {timeSlots.map((slot) => {
            const isSelected = pickupTime === slot.time;
            
            return (
              <Button
                key={slot.time}
                variant="outline"
                className={`h-auto p-4 flex flex-col items-center space-y-1 ${getSlotStyle(slot, isSelected)}`}
                onClick={() => slot.available && handleTimeSelect(slot.time)}
                disabled={!slot.available}
              >
                <div className="text-lg font-bold">{slot.time}</div>
                <div className="text-sm">
                  {slot.available ? 'Available' : 'Full'}
                </div>
                <div className="text-xs">{slot.waitTime}</div>
                {slot.status === 'busy' && slot.available && (
                  <Badge variant="outline" className="text-xs">
                    Busy
                  </Badge>
                )}
              </Button>
            );
          })}
        </div>

        {/* Pickup Location Info */}
        <Card className="bg-gray-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Pickup Location</h3>
              <Badge variant="secondary" className="flex items-center space-x-1">
                <MapPin className="h-3 w-3" />
                <span>Booth #3</span>
              </Badge>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Located near the main entrance, left side of the lobby. Look for the VenueFlow pickup sign.
            </p>
            <div className="flex items-center text-sm text-gray-500">
              <i className="fas fa-info-circle mr-2"></i>
              You'll receive a notification when your order is ready for pickup
            </div>
          </CardContent>
        </Card>

        {pickupTime && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center text-green-800">
              <i className="fas fa-check-circle mr-2"></i>
              <span className="font-medium">
                Pickup time selected: {pickupTime}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
