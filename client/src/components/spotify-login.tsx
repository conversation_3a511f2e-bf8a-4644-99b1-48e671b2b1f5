// =============================================================================
// SPOTIFY LOGIN COMPONENT
// =============================================================================
// This component handles Spotify OAuth login for artists and users
//
// 10th Grade Level: This is a button that lets users log in with their Spotify
// account so we can get information about their favorite artists and music.
//
// College Level: React component implementing Spotify OAuth 2.0 flow with
// proper error handling and user feedback.

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Music, User, CheckCircle, AlertCircle } from 'lucide-react';
import { initiateSpotifyLogin, isSpotifyConnected, getSpotifyProfile } from '@/lib/spotify';

interface SpotifyLoginProps {
  onSuccess?: (profile: any) => void;
  onError?: (error: string) => void;
  showProfile?: boolean;
}

export default function SpotifyLogin({ onSuccess, onError, showProfile = true }: SpotifyLoginProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check connection status on component mount
  useEffect(() => {
    checkSpotifyConnection();
  }, []);

  // Check if user is already connected to Spotify
  const checkSpotifyConnection = async () => {
    try {
      setLoading(true);
      const connected = await isSpotifyConnected();
      setIsConnected(connected);

      if (connected && showProfile) {
        const userProfile = await getSpotifyProfile();
        setProfile(userProfile);
      }
    } catch (err) {
      console.error('Error checking Spotify connection:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle Spotify login button click
  const handleSpotifyLogin = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await initiateSpotifyLogin();
      // User will be redirected to Spotify, so this component will unmount
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to connect to Spotify';
      setError(errorMessage);
      onError?.(errorMessage);
      setLoading(false);
    }
  };

  // Handle successful connection (called after redirect back from Spotify)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const spotifyParam = urlParams.get('spotify');
    const token = urlParams.get('token');

    if (spotifyParam === 'true' && token) {
      // User just returned from Spotify OAuth
      localStorage.setItem('token', token);
      setIsConnected(true);
      
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Fetch profile and notify parent
      if (showProfile) {
        getSpotifyProfile()
          .then(userProfile => {
            setProfile(userProfile);
            onSuccess?.(userProfile);
          })
          .catch(err => {
            console.error('Error fetching profile after login:', err);
          });
      } else {
        onSuccess?.(null);
      }
    }
  }, [onSuccess, showProfile]);

  if (loading) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center gap-2">
            <Music className="h-5 w-5 animate-spin" />
            <span>Connecting to Spotify...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isConnected && profile && showProfile) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Connected to Spotify
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            {profile.images?.[0]?.url ? (
              <img 
                src={profile.images[0].url} 
                alt={profile.displayName}
                className="w-12 h-12 rounded-full"
              />
            ) : (
              <div className="w-12 h-12 rounded-full bg-green-500 flex items-center justify-center">
                <User className="h-6 w-6 text-white" />
              </div>
            )}
            <div>
              <p className="font-medium">{profile.displayName}</p>
              <p className="text-sm text-gray-500">{profile.email}</p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Badge variant="secondary">
              {profile.followers} followers
            </Badge>
            <Badge variant="outline">
              {profile.country}
            </Badge>
          </div>

          <Button 
            variant="outline" 
            size="sm" 
            onClick={checkSpotifyConnection}
            className="w-full"
          >
            Refresh Profile
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (isConnected && !showProfile) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Successfully connected to Spotify! You can now access artist features and personalized recommendations.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5 text-green-500" />
          Connect with Spotify
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600">
          Connect your Spotify account to access artist features, personalized recommendations, 
          and exclusive merchandise from your favorite artists.
        </p>

        <div className="space-y-2">
          <h4 className="font-medium text-sm">What you'll get:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Access to artist merchandise</li>
            <li>• Personalized event recommendations</li>
            <li>• Early access to exclusive drops</li>
            <li>• Integration with your music taste</li>
          </ul>
        </div>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <Button 
          onClick={handleSpotifyLogin}
          disabled={loading}
          className="w-full bg-green-500 hover:bg-green-600 text-white"
        >
          <Music className="h-4 w-4 mr-2" />
          {loading ? 'Connecting...' : 'Connect with Spotify'}
        </Button>

        <p className="text-xs text-gray-500 text-center">
          We'll only access your public profile and music preferences. 
          You can disconnect at any time.
        </p>
      </CardContent>
    </Card>
  );
}
