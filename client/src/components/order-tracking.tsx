import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Hand } from 'lucide-react';

interface OrderTrackingProps {
  orderId: number;
}

export default function OrderTracking({ orderId }: OrderTrackingProps) {
  const { data: order, isLoading } = useQuery({
    queryKey: [`/api/orders/${orderId}`],
    enabled: !!orderId,
    refetchInterval: 5000 // Refetch every 5 seconds for real-time updates
  });

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!order) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-gray-500">Order not found</p>
        </CardContent>
      </Card>
    );
  }

  const getStatusStep = (status: string) => {
    switch (status) {
      case 'confirmed': return 1;
      case 'preparing': return 2;
      case 'ready': return 3;
      case 'completed': return 4;
      default: return 0;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-blue-500';
      case 'preparing': return 'bg-yellow-500';
      case 'ready': return 'bg-green-500';
      case 'completed': return 'bg-gray-500';
      default: return 'bg-gray-300';
    }
  };

  const steps = [
    { id: 1, name: 'Confirmed', icon: CheckCircle, time: order.createdAt },
    { id: 2, name: 'Preparing', icon: Clock, time: null },
    { id: 3, name: 'Ready', icon: Bell, time: null },
    { id: 4, name: 'Complete', icon: Hand, time: null }
  ];

  const currentStep = getStatusStep(order.status);
  const progress = (currentStep / steps.length) * 100;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Order {order.qrCode}</CardTitle>
            <p className="text-gray-600">
              Pickup: {new Date(order.pickupTime).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
              })} • {order.pickupLocation}
            </p>
          </div>
          <div className="text-right">
            <Badge 
              variant={order.status === 'ready' ? 'default' : 'secondary'}
              className={`${getStatusColor(order.status)} text-white`}
            >
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
            {order.status === 'preparing' && (
              <p className="text-sm text-gray-500 mt-1">Est. 6 minutes</p>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Progress Steps */}
        <div className="relative">
          <Progress value={progress} className="h-2" />
          
          <div className="flex justify-between mt-4">
            {steps.map((step) => {
              const isActive = step.id <= currentStep;
              const isCurrent = step.id === currentStep;
              const Icon = step.icon;
              
              return (
                <div key={step.id} className="flex flex-col items-center">
                  <div className={`
                    w-10 h-10 rounded-full flex items-center justify-center mb-2
                    ${isActive ? 'bg-primary text-white' : 'bg-gray-200 text-gray-400'}
                    ${isCurrent ? 'animate-pulse' : ''}
                  `}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div className="text-sm font-medium text-center">
                    <div className={isActive ? 'text-primary' : 'text-gray-400'}>
                      {step.name}
                    </div>
                    {step.time && (
                      <div className="text-xs text-gray-500">
                        {new Date(step.time).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Order Items */}
        <div className="border-t pt-6">
          <h4 className="font-medium mb-4">Your Items</h4>
          <div className="space-y-3">
            {order.items?.map((item: any) => (
              <div key={item.id} className="flex justify-between items-center">
                <div>
                  <span className="font-medium">{item.quantity}x {item.menuItem?.name}</span>
                  {item.customizations && (
                    <span className="text-gray-500 text-sm ml-2">
                      {Object.entries(item.customizations).map(([key, value]) => 
                        `${key}: ${value}`
                      ).join(', ')}
                    </span>
                  )}
                </div>
                <span className="text-gray-600">
                  ${(parseFloat(item.unitPrice) * item.quantity).toFixed(2)}
                </span>
              </div>
            ))}
          </div>
          
          <div className="border-t mt-4 pt-4">
            <div className="flex justify-between items-center font-semibold">
              <span>Total</span>
              <span className="text-primary text-lg">
                ${parseFloat(order.totalAmount).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
