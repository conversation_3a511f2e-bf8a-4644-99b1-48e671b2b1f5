// =============================================================================
// CANVA DESIGN STUDIO COMPONENT
// =============================================================================
// This component integrates Canva Apps SDK for in-app design creation
//
// 10th Grade Level: This is like having Canva built into our app so artists
// can create t-shirt designs without leaving our website.
//
// College Level: React component with Canva Apps SDK integration for embedded
// design creation, real-time collaboration, and automated export to Printful.

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Palette, 
  Download, 
  Save, 
  Eye, 
  Shirt, 
  Coffee,
  Image,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';

interface CanvaDesignStudioProps {
  artistId: string;
  eventId: string;
  merchType: 't-shirt' | 'hoodie' | 'mug' | 'poster' | 'sticker';
  onDesignComplete?: (designData: any) => void;
  onError?: (error: string) => void;
}

export default function CanvaDesignStudio({ 
  artistId, 
  eventId, 
  merchType, 
  onDesignComplete, 
  onError 
}: CanvaDesignStudioProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [canvaConfig, setCanvaConfig] = useState<any>(null);
  const [currentDesign, setCurrentDesign] = useState<any>(null);
  const [designValidation, setDesignValidation] = useState<any>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const canvaContainerRef = useRef<HTMLDivElement>(null);
  const canvaEditorRef = useRef<any>(null);

  // Initialize Canva SDK
  useEffect(() => {
    initializeCanva();
  }, [artistId, eventId, merchType]);

  const initializeCanva = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get Canva configuration from backend
      const configResponse = await fetch(`/api/canva/config?artistId=${artistId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!configResponse.ok) {
        throw new Error('Failed to get Canva configuration');
      }

      const config = await configResponse.json();
      setCanvaConfig(config);

      // Get merchandise template
      const templateResponse = await fetch(`/api/canva/templates/${merchType}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!templateResponse.ok) {
        throw new Error('Failed to get merchandise template');
      }

      const template = await templateResponse.json();

      // Initialize Canva Apps SDK (this would be the actual Canva SDK)
      // For now, we'll simulate the integration
      await initializeCanvaSDK(config, template);

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to initialize Canva design studio';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Simulate Canva SDK initialization
  const initializeCanvaSDK = async (config: any, template: any) => {
    // In a real implementation, this would initialize the Canva Apps SDK
    // For demonstration, we'll create a mock editor interface
    
    const mockEditor = {
      config: config,
      template: template,
      currentDesign: null,
      
      // Mock methods that would be provided by Canva SDK
      createDesign: (templateData: any) => {
        return {
          id: `design_${Date.now()}`,
          title: `${merchType} Design`,
          dimensions: template.dimensions,
          elements: []
        };
      },
      
      exportDesign: async (format: string = 'PNG') => {
        // Simulate export process
        return {
          designId: mockEditor.currentDesign?.id || 'mock_design',
          exportUrl: 'https://example.com/mock-export.png',
          title: mockEditor.currentDesign?.title || 'Mock Design',
          format: format,
          dimensions: template.dimensions
        };
      },
      
      validateDesign: (designData: any) => {
        return {
          isValid: true,
          issues: [],
          warnings: ['This is a mock validation'],
          recommendations: [
            'Use 300 DPI for best print quality',
            'Keep important elements away from edges'
          ]
        };
      }
    };

    canvaEditorRef.current = mockEditor;
    
    // Create initial design
    const initialDesign = mockEditor.createDesign(template);
    setCurrentDesign(initialDesign);
  };

  // Handle design export
  const handleExportDesign = async () => {
    if (!canvaEditorRef.current) {
      setError('Design editor not initialized');
      return;
    }

    try {
      setIsExporting(true);
      setError(null);

      // Export design from Canva
      const exportData = await canvaEditorRef.current.exportDesign('PNG');
      
      // Validate design for print
      const validation = await validateDesign(exportData);
      setDesignValidation(validation);

      if (!validation.isValid) {
        setError('Design validation failed. Please fix the issues before exporting.');
        return;
      }

      // Process export on backend
      const response = await fetch('/api/canva/export', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          exportData,
          artistId,
          eventId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to process design export');
      }

      const processedDesign = await response.json();
      
      // Notify parent component
      onDesignComplete?.(processedDesign);

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to export design';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  // Validate design for print
  const validateDesign = async (designData: any) => {
    try {
      const response = await fetch('/api/canva/validate-design', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ designData })
      });

      if (!response.ok) {
        throw new Error('Failed to validate design');
      }

      return await response.json();
    } catch (err) {
      console.error('Design validation error:', err);
      return {
        isValid: false,
        issues: ['Failed to validate design'],
        warnings: [],
        recommendations: []
      };
    }
  };

  // Get merchandise type icon
  const getMerchIcon = () => {
    switch (merchType) {
      case 't-shirt':
      case 'hoodie':
        return <Shirt className="h-5 w-5" />;
      case 'mug':
        return <Coffee className="h-5 w-5" />;
      case 'poster':
      case 'sticker':
        return <Image className="h-5 w-5" />;
      default:
        return <Palette className="h-5 w-5" />;
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center p-12">
          <div className="flex items-center gap-3">
            <Loader className="h-6 w-6 animate-spin" />
            <span className="text-lg">Loading Canva Design Studio...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getMerchIcon()}
            Canva Design Studio - {merchType.charAt(0).toUpperCase() + merchType.slice(1)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Badge variant="outline">
                {canvaConfig?.editor?.dimensions?.width}x{canvaConfig?.editor?.dimensions?.height}px
              </Badge>
              <Badge variant="outline">300 DPI</Badge>
              <Badge variant="outline">Print Ready</Badge>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setDesignValidation(canvaEditorRef.current?.validateDesign(currentDesign))}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button 
                onClick={handleExportDesign}
                disabled={isExporting}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isExporting ? (
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                {isExporting ? 'Exporting...' : 'Export Design'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Design Validation Results */}
      {designValidation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {designValidation.isValid ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-500" />
              )}
              Design Validation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="issues">
              <TabsList>
                <TabsTrigger value="issues">Issues ({designValidation.issues?.length || 0})</TabsTrigger>
                <TabsTrigger value="warnings">Warnings ({designValidation.warnings?.length || 0})</TabsTrigger>
                <TabsTrigger value="recommendations">Tips ({designValidation.recommendations?.length || 0})</TabsTrigger>
              </TabsList>
              
              <TabsContent value="issues" className="space-y-2">
                {designValidation.issues?.length > 0 ? (
                  designValidation.issues.map((issue: string, index: number) => (
                    <Alert key={index} className="border-red-200 bg-red-50">
                      <AlertDescription className="text-red-800">{issue}</AlertDescription>
                    </Alert>
                  ))
                ) : (
                  <p className="text-green-600">No issues found! ✅</p>
                )}
              </TabsContent>
              
              <TabsContent value="warnings" className="space-y-2">
                {designValidation.warnings?.map((warning: string, index: number) => (
                  <Alert key={index} className="border-yellow-200 bg-yellow-50">
                    <AlertDescription className="text-yellow-800">{warning}</AlertDescription>
                  </Alert>
                ))}
              </TabsContent>
              
              <TabsContent value="recommendations" className="space-y-2">
                {designValidation.recommendations?.map((rec: string, index: number) => (
                  <Alert key={index} className="border-blue-200 bg-blue-50">
                    <AlertDescription className="text-blue-800">{rec}</AlertDescription>
                  </Alert>
                ))}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Canva Editor Container */}
      <Card>
        <CardContent className="p-0">
          <div 
            ref={canvaContainerRef}
            className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center"
          >
            {/* This would be replaced with the actual Canva Apps SDK iframe */}
            <div className="text-center space-y-4">
              <Palette className="h-16 w-16 mx-auto text-purple-500" />
              <div>
                <h3 className="text-xl font-semibold">Canva Design Editor</h3>
                <p className="text-gray-600">
                  In a real implementation, the Canva Apps SDK would be embedded here
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Template: {merchType} | Dimensions: {canvaConfig?.editor?.dimensions?.width}x{canvaConfig?.editor?.dimensions?.height}px
                </p>
              </div>
              <Button 
                onClick={() => setCurrentDesign({ 
                  id: `mock_${Date.now()}`, 
                  title: `Mock ${merchType} Design`,
                  mockData: true 
                })}
                variant="outline"
              >
                <Save className="h-4 w-4 mr-2" />
                Simulate Design Creation
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Design Info */}
      {currentDesign && (
        <Card>
          <CardHeader>
            <CardTitle>Current Design</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Design ID:</strong> {currentDesign.id}</p>
              <p><strong>Title:</strong> {currentDesign.title}</p>
              <p><strong>Type:</strong> {merchType}</p>
              <p><strong>Status:</strong> 
                <Badge className="ml-2">
                  {currentDesign.mockData ? 'Mock Design' : 'Ready for Export'}
                </Badge>
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
