import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

interface VenueBannerProps {
  venueId?: number;
}

export default function VenueBanner({ venueId = 1 }: VenueBannerProps) {
  const [isVisible, setIsVisible] = useState(false);

  const { data: venue } = useQuery({
    queryKey: [`/api/venues/${venueId}`],
    enabled: !!venueId
  });

  useEffect(() => {
    // Simulate venue detection after 2 seconds
    const timer = setTimeout(() => setIsVisible(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  if (!isVisible || !venue) return null;

  return (
    <div className="bg-gradient-to-r from-primary to-purple-600 text-white px-4 py-3 text-center animate-in slide-in-from-top duration-500">
      <div className="flex items-center justify-center space-x-2">
        <i className="fas fa-map-marker-alt"></i>
        <span className="font-medium">You're live at {venue.name}!</span>
        <button className="ml-4 bg-white text-primary px-3 py-1 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors">
          Order Now
        </button>
      </div>
    </div>
  );
}
