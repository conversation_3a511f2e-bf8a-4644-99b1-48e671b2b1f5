// =============================================================================
// CANVA AUTHENTICATION COMPONENT
// =============================================================================
// This component handles Canva OAuth authentication for artists
//
// 10th Grade Level: This is a button that lets artists log into Canva so they
// can create designs for their merchandise directly in our app.
//
// College Level: React component implementing Canva OAuth 2.0 flow with
// proper error handling and token management.

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Palette, User, CheckCircle, AlertCircle, ExternalLink } from 'lucide-react';

interface CanvaAuthProps {
  artistId?: string;
  onSuccess?: (authData: any) => void;
  onError?: (error: string) => void;
  showProfile?: boolean;
}

export default function CanvaAuth({ artistId, onSuccess, onError, showProfile = true }: CanvaAuthProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [canvaProfile, setCanvaProfile] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check for auth result in URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const canvaAuth = urlParams.get('canva_auth');
    const userId = urlParams.get('user_id');
    const errorMessage = urlParams.get('message');

    if (canvaAuth === 'success' && userId) {
      setIsConnected(true);
      onSuccess?.({ userId });
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (canvaAuth === 'error' && errorMessage) {
      const decodedError = decodeURIComponent(errorMessage);
      setError(decodedError);
      onError?.(decodedError);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [onSuccess, onError]);

  // Initiate Canva OAuth
  const handleCanvaAuth = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Please log in first');
      }

      const response = await fetch(`/api/canva/auth?artistId=${artistId || ''}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to initiate Canva authentication');
      }

      const { authUrl } = await response.json();
      
      // Redirect to Canva OAuth
      window.location.href = authUrl;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to connect to Canva';
      setError(errorMessage);
      onError?.(errorMessage);
      setLoading(false);
    }
  };

  // Check if user is already connected (this would check stored tokens)
  const checkCanvaConnection = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return false;

      // TODO: Check if user has valid Canva tokens in database
      // For now, we'll simulate this check
      const storedCanvaAuth = localStorage.getItem('canva_connected');
      if (storedCanvaAuth) {
        setIsConnected(true);
        setCanvaProfile(JSON.parse(storedCanvaAuth));
      }
    } catch (error) {
      console.error('Error checking Canva connection:', error);
    }
  };

  useEffect(() => {
    checkCanvaConnection();
  }, []);

  if (loading) {
    return (
      <Card className="w-full max-w-md">
        <CardContent className="flex items-center justify-center p-6">
          <div className="flex items-center gap-2">
            <Palette className="h-5 w-5 animate-spin" />
            <span>Connecting to Canva...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isConnected && canvaProfile && showProfile) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Connected to Canva
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-purple-500 flex items-center justify-center">
              <User className="h-6 w-6 text-white" />
            </div>
            <div>
              <p className="font-medium">{canvaProfile.displayName || 'Canva User'}</p>
              <p className="text-sm text-gray-500">{canvaProfile.email}</p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Badge variant="secondary">
              Design Access
            </Badge>
            <Badge variant="outline">
              Export Enabled
            </Badge>
          </div>

          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.open('https://www.canva.com', '_blank')}
              className="flex-1"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open Canva
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={checkCanvaConnection}
            >
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isConnected && !showProfile) {
    return (
      <Alert className="border-green-200 bg-green-50">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          Successfully connected to Canva! You can now create and manage designs for your merchandise.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5 text-purple-500" />
          Connect with Canva
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600">
          Connect your Canva account to create professional merchandise designs directly in our platform.
        </p>

        <div className="space-y-2">
          <h4 className="font-medium text-sm">What you'll get:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Access to Canva's design tools</li>
            <li>• Professional merchandise templates</li>
            <li>• Direct export to Printful</li>
            <li>• Brand-consistent designs</li>
            <li>• High-resolution exports (300 DPI)</li>
          </ul>
        </div>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <Button 
          onClick={handleCanvaAuth}
          disabled={loading}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white"
        >
          <Palette className="h-4 w-4 mr-2" />
          {loading ? 'Connecting...' : 'Connect with Canva'}
        </Button>

        <p className="text-xs text-gray-500 text-center">
          You'll be redirected to Canva to authorize access. 
          We only access your designs and export capabilities.
        </p>
      </CardContent>
    </Card>
  );
}

// =============================================================================
// CANVA DESIGN MANAGER COMPONENT
// =============================================================================

interface CanvaDesignManagerProps {
  accessToken: string;
  onDesignSelect?: (design: any) => void;
  onExportComplete?: (exportData: any) => void;
}

export function CanvaDesignManager({ accessToken, onDesignSelect, onExportComplete }: CanvaDesignManagerProps) {
  const [designs, setDesigns] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch user's designs
  const fetchDesigns = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      const response = await fetch(`/api/canva/designs?accessToken=${accessToken}&limit=20`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch designs');
      }

      const userDesigns = await response.json();
      setDesigns(userDesigns);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch designs');
    } finally {
      setLoading(false);
    }
  };

  // Create new design
  const createNewDesign = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/canva/designs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          accessToken,
          designType: 'presentation',
          title: 'New Merchandise Design'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create design');
      }

      const newDesign = await response.json();
      
      // Open design in Canva
      if (newDesign.urls?.edit_url) {
        window.open(newDesign.urls.edit_url, '_blank');
      }
      
      // Refresh designs list
      fetchDesigns();
    } catch (err: any) {
      setError(err.message || 'Failed to create design');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (accessToken) {
      fetchDesigns();
    }
  }, [accessToken]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Your Canva Designs</span>
          <Button onClick={createNewDesign} disabled={loading} size="sm">
            <Palette className="h-4 w-4 mr-2" />
            New Design
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert className="border-red-200 bg-red-50 mb-4">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}

        {loading ? (
          <div className="flex items-center justify-center p-8">
            <Palette className="h-6 w-6 animate-spin mr-2" />
            <span>Loading designs...</span>
          </div>
        ) : designs.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {designs.map((design) => (
              <div
                key={design.id}
                className="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer"
                onClick={() => onDesignSelect?.(design)}
              >
                {design.thumbnail && (
                  <img
                    src={design.thumbnail}
                    alt={design.title}
                    className="w-full h-32 object-cover rounded mb-2"
                  />
                )}
                <h4 className="font-medium text-sm truncate">{design.title}</h4>
                <p className="text-xs text-gray-500">{design.designType}</p>
                <p className="text-xs text-gray-400">
                  {new Date(design.updatedAt).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-8">
            <Palette className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">No designs found</p>
            <Button onClick={createNewDesign} className="mt-4" size="sm">
              Create Your First Design
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
