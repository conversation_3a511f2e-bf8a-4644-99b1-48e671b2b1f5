// =============================================================================
// SUPABASE CONFIGURATION - Free Firebase Alternative
// =============================================================================
// Supabase provides real-time database, authentication, and more
// It's more generous with free tier than Firebase
// Get your credentials from: https://supabase.com

import { createClient } from '@supabase/supabase-js';

// Supabase configuration - these come from your Supabase project dashboard
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://demo-project.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'demo-anon-key';

// Create Supabase client for real-time features and authentication
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// =============================================================================
// FALLBACK FIREBASE CONFIGURATION (if you prefer Firebase)
// =============================================================================
// Uncomment this section if you want to use Firebase instead of Supabase

/*
import { initializeApp } from "firebase/app";
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signInAnonymously, onAuthStateChanged } from "firebase/auth";
import { getFirestore, doc, setDoc, getDoc, collection, query, where, onSnapshot } from "firebase/firestore";

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "demo-api-key",
  authDomain: `${import.meta.env.VITE_FIREBASE_PROJECT_ID || "demo-project"}.firebaseapp.com`,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "demo-project",
  storageBucket: `${import.meta.env.VITE_FIREBASE_PROJECT_ID || "demo-project"}.firebasestorage.app`,
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "demo-app-id",
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
*/

// =============================================================================
// SUPABASE AUTHENTICATION FUNCTIONS
// =============================================================================
// These functions handle user authentication using Supabase
// Much simpler than Firebase and more generous free tier

// Sign in with email and password
export const signInEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  if (error) throw error;
  return data;
};

// Sign up with email and password
export const signUpEmail = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });
  if (error) throw error;
  return data;
};

// Sign in as guest (anonymous)
export const signInGuest = async () => {
  const { data, error } = await supabase.auth.signInAnonymously();
  if (error) throw error;
  return data;
};

// Listen for authentication state changes
export const onAuthChange = (callback: (user: any) => void) => {
  return supabase.auth.onAuthStateChange((event, session) => {
    callback(session?.user || null);
  });
};

// =============================================================================
// SUPABASE DATABASE FUNCTIONS
// =============================================================================
// These functions handle real-time data operations

// Create user document in database
export const createUserDocument = async (userId: string, userData: any) => {
  const { error } = await supabase
    .from('users')
    .insert({ id: userId, ...userData });
  if (error) throw error;
};

// Get user document from database
export const getUserDocument = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
  if (error && error.code !== 'PGRST116') throw error; // PGRST116 = not found
  return data;
};

// Subscribe to user's orders with real-time updates
export const subscribeToOrders = (userId: string, callback: (orders: any[]) => void) => {
  // Set up real-time subscription for orders
  const subscription = supabase
    .channel('orders')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'orders',
        filter: `userId=eq.${userId}`
      },
      (payload) => {
        // When orders change, fetch updated list
        fetchUserOrders(userId).then(callback);
      }
    )
    .subscribe();

  // Initial fetch
  fetchUserOrders(userId).then(callback);

  // Return unsubscribe function
  return () => supabase.removeChannel(subscription);
};

// Subscribe to queue status with real-time updates
export const subscribeToQueueStatus = (eventId: string, callback: (status: any) => void) => {
  // Set up real-time subscription for queue status
  const subscription = supabase
    .channel('queue_status')
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'queue_status',
        filter: `eventId=eq.${eventId}`
      },
      (payload) => {
        callback(payload.new);
      }
    )
    .subscribe();

  // Initial fetch
  fetchQueueStatus(eventId).then(callback);

  // Return unsubscribe function
  return () => supabase.removeChannel(subscription);
};

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================
// These are internal functions used by the subscription functions above

// Fetch user's orders from database
const fetchUserOrders = async (userId: string) => {
  const { data, error } = await supabase
    .from('orders')
    .select('*')
    .eq('userId', userId)
    .order('created_at', { ascending: false });

  if (error) throw error;
  return data || [];
};

// Fetch queue status from database
const fetchQueueStatus = async (eventId: string) => {
  const { data, error } = await supabase
    .from('queue_status')
    .select('*')
    .eq('eventId', eventId)
    .single();

  if (error && error.code !== 'PGRST116') throw error;
  return data;
};
