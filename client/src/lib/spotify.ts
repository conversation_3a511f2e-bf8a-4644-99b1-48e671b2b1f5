// =============================================================================
// SPOTIFY CLIENT SERVICE
// =============================================================================
// This module handles Spotify integration on the frontend
// 
// 10th Grade Level: This helps the website talk to Spotify to get information
// about artists and their music for showing the right merchandise.
//
// College Level: Client-side Spotify Web API integration with OAuth flow
// and data fetching for artist profiles, tracks, and event generation.

// =============================================================================
// SPOTIFY AUTHENTICATION
// =============================================================================

/**
 * Initiate Spotify OAuth login
 * Redirects user to Spotify authorization page
 */
export async function initiateSpotifyLogin(): Promise<void> {
  try {
    const response = await fetch('/api/auth/spotify');
    const data = await response.json();
    
    if (data.authUrl) {
      // Redirect to Spotify authorization page
      window.location.href = data.authUrl;
    } else {
      throw new Error('Failed to get Spotify authorization URL');
    }
  } catch (error) {
    console.error('Error initiating Spotify login:', error);
    throw error;
  }
}

/**
 * Check if user is connected to Spotify
 * @returns Promise<boolean> - True if user has Spotify connection
 */
export async function isSpotifyConnected(): Promise<boolean> {
  try {
    const token = localStorage.getItem('token');
    if (!token) return false;

    const response = await fetch('/api/spotify/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Get user's Spotify profile
 * @returns Promise<SpotifyProfile> - User's Spotify profile data
 */
export async function getSpotifyProfile(): Promise<any> {
  try {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('No authentication token');

    const response = await fetch('/api/spotify/profile', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch Spotify profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching Spotify profile:', error);
    throw error;
  }
}

// =============================================================================
// ARTIST SEARCH & DATA
// =============================================================================

/**
 * Search for artists on Spotify
 * @param query - Artist name to search for
 * @returns Promise<SpotifyArtist[]> - Array of artist results
 */
export async function searchArtists(query: string): Promise<any[]> {
  try {
    const response = await fetch(`/api/spotify/search/artists?q=${encodeURIComponent(query)}`);
    
    if (!response.ok) {
      throw new Error('Failed to search artists');
    }

    return await response.json();
  } catch (error) {
    console.error('Error searching artists:', error);
    throw error;
  }
}

/**
 * Get artist details, top tracks, and mock events
 * @param artistId - Spotify artist ID
 * @returns Promise<ArtistData> - Complete artist data with events
 */
export async function getArtistData(artistId: string): Promise<any> {
  try {
    const response = await fetch(`/api/spotify/artists/${artistId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch artist data');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching artist data:', error);
    throw error;
  }
}

/**
 * Get user's top artists from Spotify
 * @returns Promise<SpotifyArtist[]> - User's top artists
 */
export async function getUserTopArtists(): Promise<any[]> {
  try {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('No authentication token');

    const response = await fetch('/api/spotify/me/top-artists', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch top artists');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching top artists:', error);
    throw error;
  }
}

// =============================================================================
// MERCHANDISE INTEGRATION
// =============================================================================

/**
 * Upload artist merchandise
 * @param merchData - Merchandise data including image file
 * @returns Promise<MerchItem> - Created merchandise item
 */
export async function uploadMerchandise(merchData: FormData): Promise<any> {
  try {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('No authentication token');

    const response = await fetch('/api/merch/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: merchData
    });

    if (!response.ok) {
      throw new Error('Failed to upload merchandise');
    }

    return await response.json();
  } catch (error) {
    console.error('Error uploading merchandise:', error);
    throw error;
  }
}

/**
 * Generate AI-powered merchandise
 * @param generationParams - Parameters for AI generation
 * @returns Promise<GeneratedMerch> - Generated merchandise data
 */
export async function generateAIMerchandise(generationParams: {
  artistId?: string;
  eventId: string;
  style: string;
  colors: string;
  merchType: string;
}): Promise<any> {
  try {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('No authentication token');

    const response = await fetch('/api/merch/generate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(generationParams)
    });

    if (!response.ok) {
      throw new Error('Failed to generate AI merchandise');
    }

    return await response.json();
  } catch (error) {
    console.error('Error generating AI merchandise:', error);
    throw error;
  }
}

/**
 * Create a new order with merchandise and food items
 * @param orderData - Order data including items
 * @returns Promise<Order> - Created order
 */
export async function createOrder(orderData: {
  eventId: string;
  items: Array<{
    id: string;
    type: 'menu' | 'merch';
    quantity: number;
    customizations?: any;
  }>;
  deliveryMethod?: 'pickup' | 'delivery';
  pickupLocation?: string;
  specialInstructions?: string;
}): Promise<any> {
  try {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('No authentication token');

    const response = await fetch('/api/orders/new', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(orderData)
    });

    if (!response.ok) {
      throw new Error('Failed to create order');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
}

/**
 * Get merchandise for an event
 * @param eventId - Event ID
 * @returns Promise<MerchItem[]> - Array of merchandise items
 */
export async function getEventMerchandise(eventId: string): Promise<any[]> {
  try {
    const response = await fetch(`/api/events/${eventId}/merch`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch event merchandise');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching event merchandise:', error);
    throw error;
  }
}

/**
 * Update merchandise availability
 * @param merchId - Merchandise ID
 * @param isAvailable - Whether item is available
 * @param stock - Updated stock count
 * @returns Promise<MerchItem> - Updated merchandise item
 */
export async function updateMerchandiseAvailability(
  merchId: string, 
  isAvailable: boolean, 
  stock?: number
): Promise<any> {
  try {
    const token = localStorage.getItem('token');
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/merch/${merchId}/availability`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ isAvailable, stock })
    });

    if (!response.ok) {
      throw new Error('Failed to update merchandise availability');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating merchandise availability:', error);
    throw error;
  }
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Format artist image URL with fallback
 * @param images - Array of Spotify image objects
 * @param size - Preferred size ('small', 'medium', 'large')
 * @returns string - Image URL or fallback
 */
export function getArtistImageUrl(images: any[], size: 'small' | 'medium' | 'large' = 'medium'): string {
  if (!images || images.length === 0) {
    return '/placeholder-artist.jpg'; // Fallback image
  }

  // Spotify images are usually ordered by size (largest first)
  const sizeIndex = size === 'large' ? 0 : size === 'medium' ? 1 : 2;
  const image = images[sizeIndex] || images[0];
  
  return image?.url || '/placeholder-artist.jpg';
}

/**
 * Format track duration from milliseconds to MM:SS
 * @param durationMs - Duration in milliseconds
 * @returns string - Formatted duration
 */
export function formatTrackDuration(durationMs: number): string {
  const minutes = Math.floor(durationMs / 60000);
  const seconds = Math.floor((durationMs % 60000) / 1000);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Check if user has required role for artist features
 * @returns boolean - True if user can access artist features
 */
export function canAccessArtistFeatures(): boolean {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  return user.role === 'artist' || user.role === 'admin';
}
