import { useEffect, useState } from 'react';

interface QueueStatus {
  preparing: number;
  ready: number;
  avgWaitTime: number;
}

interface UseRealtimeProps {
  eventId?: number;
}

export const useRealtime = ({ eventId }: UseRealtimeProps = {}) => {
  const [queueStatus, setQueueStatus] = useState<QueueStatus>({
    preparing: 0,
    ready: 0,
    avgWaitTime: 0
  });
  const [isConnected, setIsConnected] = useState(false);
  const [socket, setSocket] = useState<WebSocket | null>(null);

  useEffect(() => {
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      setIsConnected(true);
      setSocket(ws);
      
      // Subscribe to event updates
      if (eventId) {
        ws.send(JSON.stringify({
          type: 'subscribe',
          eventId
        }));
      }
    };
    
    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'queueUpdate') {
          setQueueStatus({
            preparing: data.preparing,
            ready: data.ready,
            avgWaitTime: data.avgWaitTime
          });
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    };
    
    ws.onclose = () => {
      setIsConnected(false);
      setSocket(null);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    return () => {
      ws.close();
    };
  }, [eventId]);

  return {
    queueStatus,
    isConnected,
    socket
  };
};
