import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { ShoppingCart, Clock, Ticket, UtensilsCrossed, Shirt, Plus, Minus } from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';

interface CartItem {
  itemType: 'ticket' | 'food' | 'merch';
  itemId: number;
  name: string;
  price: number;
  quantity: number;
  description?: string;
  customizations?: any;
}

export default function PreEventOrder() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [cart, setCart] = useState<CartItem[]>([]);
  const [pickupTime, setPickupTime] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedEventId, setSelectedEventId] = useState<number>(1);

  // Fetch available events
  const { data: events = [] } = useQuery({
    queryKey: ['/api/events'],
  });

  // Fetch tickets for selected event
  const { data: tickets = [] } = useQuery({
    queryKey: ['/api/tickets', selectedEventId],
    enabled: !!selectedEventId,
  });

  // Fetch food menu for selected event
  const { data: menuCategories = [] } = useQuery({
    queryKey: ['/api/menu-categories', selectedEventId],
    enabled: !!selectedEventId,
  });

  // Fetch artist merch for selected event
  const { data: artistMerch = [] } = useQuery({
    queryKey: ['/api/artist-merch', selectedEventId],
    enabled: !!selectedEventId,
  });

  // Create pre-event order mutation
  const createOrderMutation = useMutation({
    mutationFn: async (orderData: any) => {
      return await apiRequest('/api/pre-event-orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
        headers: { 'Content-Type': 'application/json' }
      });
    },
    onSuccess: () => {
      toast({
        title: "Order Placed Successfully!",
        description: "Your pre-event order has been confirmed. You can modify it up to 30 minutes before pickup time.",
      });
      setCart([]);
      setPickupTime('');
      setNotes('');
      queryClient.invalidateQueries({ queryKey: ['/api/pre-event-orders'] });
    },
    onError: (error: any) => {
      toast({
        title: "Order Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const addToCart = (item: Omit<CartItem, 'quantity'>) => {
    const existingIndex = cart.findIndex(
      cartItem => cartItem.itemType === item.itemType && cartItem.itemId === item.itemId
    );

    if (existingIndex >= 0) {
      const newCart = [...cart];
      newCart[existingIndex].quantity += 1;
      setCart(newCart);
    } else {
      setCart([...cart, { ...item, quantity: 1 }]);
    }

    toast({
      title: "Added to Cart",
      description: `${item.name} added to your pre-event order`,
    });
  };

  const updateQuantity = (itemType: string, itemId: number, change: number) => {
    const newCart = cart.map(item => {
      if (item.itemType === itemType && item.itemId === itemId) {
        const newQuantity = Math.max(0, item.quantity + change);
        return newQuantity === 0 ? null : { ...item, quantity: newQuantity };
      }
      return item;
    }).filter(Boolean) as CartItem[];
    
    setCart(newCart);
  };

  const getTotalAmount = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const handlePlaceOrder = () => {
    if (cart.length === 0) {
      toast({
        title: "Empty Cart",
        description: "Please add items to your cart before placing an order",
        variant: "destructive",
      });
      return;
    }

    if (!pickupTime) {
      toast({
        title: "Pickup Time Required",
        description: "Please select a pickup time for your order",
        variant: "destructive",
      });
      return;
    }

    const pickupDateTime = new Date(pickupTime);
    const canModifyUntil = new Date(pickupDateTime.getTime() - 30 * 60 * 1000); // 30 minutes before

    const orderData = {
      eventId: selectedEventId,
      pickupTime: pickupDateTime.toISOString(),
      canModifyUntil: canModifyUntil.toISOString(),
      totalAmount: getTotalAmount(),
      notes,
      items: cart.map(item => ({
        itemType: item.itemType,
        itemId: item.itemId,
        quantity: item.quantity,
        unitPrice: item.price,
        customizations: item.customizations || {}
      }))
    };

    createOrderMutation.mutate(orderData);
  };

  const selectedEvent = events.find((event: any) => event.id === selectedEventId);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">Pre-Event Ordering</h1>
          <p className="text-gray-600">
            Order tickets, food, and merchandise ahead of time. Modify your order up to 30 minutes before pickup.
          </p>
        </div>

        {/* Event Selection */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Select Event</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {events.map((event: any) => (
                <div
                  key={event.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedEventId === event.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedEventId(event.id)}
                >
                  <h3 className="font-semibold">{event.name}</h3>
                  <p className="text-sm text-gray-600">
                    {new Date(event.startTime).toLocaleDateString()} at{' '}
                    {new Date(event.startTime).toLocaleTimeString()}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {selectedEventId && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Items Selection */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="tickets" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="tickets" className="flex items-center gap-2">
                    <Ticket className="h-4 w-4" />
                    Tickets
                  </TabsTrigger>
                  <TabsTrigger value="food" className="flex items-center gap-2">
                    <UtensilsCrossed className="h-4 w-4" />
                    Food
                  </TabsTrigger>
                  <TabsTrigger value="merch" className="flex items-center gap-2">
                    <Shirt className="h-4 w-4" />
                    Merchandise
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="tickets" className="mt-6">
                  <div className="grid gap-4">
                    {tickets.map((ticket: any) => (
                      <Card key={ticket.id}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h3 className="font-semibold">{ticket.type} Ticket</h3>
                              <p className="text-sm text-gray-600">{ticket.description}</p>
                              {ticket.seatSection && (
                                <Badge variant="secondary" className="mt-2">
                                  {ticket.seatSection}
                                </Badge>
                              )}
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold">${ticket.price}</div>
                              <div className="text-sm text-gray-600">
                                {ticket.availableQuantity} available
                              </div>
                              <Button
                                onClick={() => addToCart({
                                  itemType: 'ticket',
                                  itemId: ticket.id,
                                  name: `${ticket.type} Ticket`,
                                  price: parseFloat(ticket.price),
                                  description: ticket.description
                                })}
                                className="mt-2"
                                disabled={ticket.availableQuantity === 0}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="food" className="mt-6">
                  <div className="grid gap-6">
                    {menuCategories.map((category: any) => (
                      <div key={category.id}>
                        <h3 className="text-xl font-semibold mb-4">{category.name}</h3>
                        <div className="grid gap-4">
                          {category.items?.map((item: any) => (
                            <Card key={item.id}>
                              <CardContent className="p-4">
                                <div className="flex justify-between items-start">
                                  <div className="flex-1">
                                    <h4 className="font-semibold">{item.name}</h4>
                                    <p className="text-sm text-gray-600">{item.description}</p>
                                    {item.prepTime && (
                                      <div className="flex items-center gap-1 mt-2 text-sm text-gray-500">
                                        <Clock className="h-3 w-3" />
                                        {item.prepTime} min prep
                                      </div>
                                    )}
                                  </div>
                                  <div className="text-right">
                                    <div className="text-lg font-bold">${item.price}</div>
                                    <div className="text-sm text-gray-600">
                                      Stock: {item.stock || 0}
                                    </div>
                                    <Button
                                      onClick={() => addToCart({
                                        itemType: 'food',
                                        itemId: item.id,
                                        name: item.name,
                                        price: parseFloat(item.price),
                                        description: item.description
                                      })}
                                      className="mt-2"
                                      disabled={!item.isAvailable || item.stock === 0}
                                    >
                                      <Plus className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="merch" className="mt-6">
                  <div className="grid gap-4">
                    {artistMerch.map((item: any) => (
                      <Card key={item.id}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-semibold">{item.name}</h4>
                              <p className="text-sm text-gray-600">{item.description}</p>
                              {item.isBundle && (
                                <Badge variant="secondary" className="mt-2">
                                  Bundle
                                </Badge>
                              )}
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold">${item.price}</div>
                              <div className="text-sm text-gray-600">
                                Stock: {item.stock || 0}
                              </div>
                              <Button
                                onClick={() => addToCart({
                                  itemType: 'merch',
                                  itemId: item.id,
                                  name: item.name,
                                  price: parseFloat(item.price),
                                  description: item.description
                                })}
                                className="mt-2"
                                disabled={item.stock === 0}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Cart and Checkout */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    Your Order
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {cart.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">Your cart is empty</p>
                  ) : (
                    <div className="space-y-4">
                      {cart.map((item, index) => (
                        <div key={`${item.itemType}-${item.itemId}`} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                          <div className="flex-1">
                            <h4 className="font-medium">{item.name}</h4>
                            <p className="text-sm text-gray-600">${item.price} each</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateQuantity(item.itemType, item.itemId, -1)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center">{item.quantity}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => updateQuantity(item.itemType, item.itemId, 1)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                      <div className="border-t pt-4">
                        <div className="flex justify-between items-center text-lg font-bold">
                          <span>Total: ${getTotalAmount().toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Pickup Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="pickupTime">Pickup Time</Label>
                    <Input
                      id="pickupTime"
                      type="datetime-local"
                      value={pickupTime}
                      onChange={(e) => setPickupTime(e.target.value)}
                    />
                    <p className="text-xs text-gray-600 mt-1">
                      You can modify your order up to 30 minutes before this time
                    </p>
                  </div>
                  
                  <div>
                    <Label htmlFor="notes">Special Instructions (Optional)</Label>
                    <Textarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Any special requests or notes..."
                    />
                  </div>

                  <Button
                    onClick={handlePlaceOrder}
                    disabled={cart.length === 0 || !pickupTime || createOrderMutation.isPending}
                    className="w-full"
                  >
                    {createOrderMutation.isPending ? 'Placing Order...' : 'Place Pre-Event Order'}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}