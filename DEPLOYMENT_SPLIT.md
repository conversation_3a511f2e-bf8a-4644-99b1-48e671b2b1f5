# 🚀 Split Deployment Guide

## 📋 Overview
- **Frontend**: Railway (Static React app)
- **Backend**: Vercel (Express API)

## 🎯 Frontend Deployment (Railway)

### 1. Current Repository Setup
- Repository: `venue-preorder-platform`
- Railway will build: `vite build` (frontend only)
- Railway will serve: Static files from `dist/`

### 2. Railway Configuration
- **Build Command**: `npm run build`
- **Start Command**: `npm start`
- **Environment Variables**: None needed for frontend

## 🎯 Backend Deployment (Vercel)

### 1. Create New Repository
```bash
# Create new repo for backend
mkdir venue-preorder-backend
cd venue-preorder-backend

# Copy backend files
cp -r ../venue-preorder-platform/server ./
cp -r ../venue-preorder-platform/shared ./
cp ../venue-preorder-platform/backend-package.json ./package.json
cp ../venue-preorder-platform/vercel.json ./
cp ../venue-preorder-platform/.env ./

# Initialize git
git init
git add .
git commit -m "Initial backend deployment"
```

### 2. Deploy to Vercel
1. Push backend repo to GitHub
2. Connect to Vercel
3. Deploy with environment variables

### 3. Environment Variables for Vercel
```
DATABASE_URL=your_neon_database_url
JWT_SECRET=your_jwt_secret
STRIPE_SECRET_KEY=your_stripe_key
SPOTIFY_CLIENT_ID=your_spotify_id
SPOTIFY_CLIENT_SECRET=your_spotify_secret
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_key
PRINTFUL_ACCESS_TOKEN=your_printful_token
CANVA_CLIENT_ID=your_canva_id
CANVA_CLIENT_SECRET=your_canva_secret
```

## 🔗 Update Frontend API URLs

After backend is deployed, update `client/src/config.ts`:
```typescript
export const API_BASE_URL = 'https://your-vercel-backend-url.vercel.app';
```

## ✅ Deployment Steps

1. **Deploy Backend First**:
   - Create backend repo
   - Deploy to Vercel
   - Get Vercel URL

2. **Update Frontend**:
   - Update API_BASE_URL in config.ts
   - Commit changes

3. **Deploy Frontend**:
   - Railway will automatically redeploy
   - Frontend will connect to Vercel backend

## 🎊 Result
- **Frontend**: `https://venue-preorder-platform-production.up.railway.app`
- **Backend**: `https://venue-preorder-backend.vercel.app`
