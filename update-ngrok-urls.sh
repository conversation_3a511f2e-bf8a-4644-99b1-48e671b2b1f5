#!/bin/bash

# =============================================================================
# UPDATE NGROK URLS FOR CANVA INTEGRATION
# =============================================================================
# This script automatically updates your .env file with the current ngrok URL

set -e

echo "🌐 Updating ngrok URLs for Canva integration..."
echo "=============================================="

# Check if ngrok is running
if ! curl -s http://localhost:4040/api/tunnels > /dev/null 2>&1; then
    echo "❌ ngrok is not running or not accessible on port 4040"
    echo "   Please start ngrok first: ngrok http 5000"
    exit 1
fi

# Get the current ngrok URL
NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o 'https://[^"]*\.ngrok-free\.app' | head -1)

if [ -z "$NGROK_URL" ]; then
    echo "❌ Could not retrieve ngrok URL"
    echo "   Make sure ngrok is running: ngrok http 5000"
    exit 1
fi

echo "✅ Found ngrok URL: $NGROK_URL"

# Update .env file
if [ -f ".env" ]; then
    # Create backup
    cp .env .env.backup
    
    # Update the URLs
    sed -i.tmp "s|CANVA_REDIRECT_URI=.*|CANVA_REDIRECT_URI=${NGROK_URL}/api/canva/oauth/redirect|g" .env
    sed -i.tmp "s|CANVA_RETURN_NAV_URI=.*|CANVA_RETURN_NAV_URI=${NGROK_URL}/api/canva/return-nav|g" .env
    sed -i.tmp "s|CANVA_BASE_URL=.*|CANVA_BASE_URL=${NGROK_URL}|g" .env
    
    # Remove temporary file
    rm -f .env.tmp
    
    echo "✅ Updated .env file with new ngrok URLs"
    echo ""
    echo "📋 CANVA DEVELOPER DASHBOARD SETUP:"
    echo "===================================="
    echo ""
    echo "1. Go to: https://www.canva.dev/docs/apps/"
    echo "2. Click 'Configuration'"
    echo "3. Update these URLs:"
    echo ""
    echo "   📌 Authorized Redirect URL:"
    echo "   ${NGROK_URL}/api/canva/oauth/redirect"
    echo ""
    echo "   📌 Return Navigation URL:"
    echo "   ${NGROK_URL}/api/canva/return-nav"
    echo ""
    echo "4. Save the configuration"
    echo ""
    echo "🚀 Your app is now accessible at: $NGROK_URL"
    echo ""
else
    echo "❌ .env file not found"
    exit 1
fi
