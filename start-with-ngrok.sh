#!/bin/bash

# =============================================================================
# VENUE PREORDER MVP - NGROK DEVELOPMENT SETUP
# =============================================================================
# This script starts the development server with ngrok for Canva integration
#
# Requirements: ngrok installed, .env configured

set -e  # Exit on any error

echo "🚀 Starting Venue Preorder MVP with ngrok for Canva integration..."
echo "=================================================================="

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed. Installing now..."
    
    # Check if Homebrew is available (macOS)
    if command -v brew &> /dev/null; then
        echo "📦 Installing ngrok via Homebrew..."
        brew install ngrok
    else
        echo "❌ Please install ngrok manually:"
        echo "   - Visit: https://ngrok.com/download"
        echo "   - Or use: npm install -g ngrok"
        exit 1
    fi
fi

echo "✅ ngrok is installed"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please run setup.sh first."
    exit 1
fi

# Check if development dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Start the development server in background
echo "🚀 Starting development server..."
npm run dev &
DEV_SERVER_PID=$!

# Wait a moment for the server to start
echo "⏳ Waiting for development server to start..."
sleep 5

# Check if the server is running
if ! curl -s http://localhost:5000 > /dev/null; then
    echo "❌ Development server failed to start on port 5000"
    kill $DEV_SERVER_PID 2>/dev/null || true
    exit 1
fi

echo "✅ Development server is running on http://localhost:5000"

# Start ngrok with configured authtoken
echo "🌐 Starting ngrok tunnel on port 5000..."
ngrok http 5000 &
NGROK_PID=$!

# Wait for ngrok to start
echo "⏳ Waiting for ngrok to establish tunnel..."
sleep 3

# Get ngrok URL
echo "🔍 Getting ngrok public URL..."
NGROK_URL=""
for i in {1..10}; do
    NGROK_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o 'https://[^"]*\.ngrok-free\.app' | head -1)
    if [ ! -z "$NGROK_URL" ]; then
        break
    fi
    echo "   Attempt $i/10: Waiting for ngrok..."
    sleep 2
done

if [ -z "$NGROK_URL" ]; then
    echo "❌ Failed to get ngrok URL. Please check ngrok status manually."
    echo "   Visit: http://localhost:4040"
    kill $DEV_SERVER_PID $NGROK_PID 2>/dev/null || true
    exit 1
fi

echo ""
echo "🎉 SUCCESS! Your app is now publicly accessible!"
echo "=============================================="
echo ""
echo "✅ Your Public ngrok URL:"
echo "   $NGROK_URL"
echo ""
echo "📋 CANVA INTEGRATION SETUP:"
echo "============================"
echo ""
echo "1. Go to your Canva Developer Dashboard:"
echo "   https://www.canva.dev/docs/apps/"
echo ""
echo "2. Click 'Configuration' and add these URLs:"
echo ""
echo "   📌 Authorized Redirect URL:"
echo "   $NGROK_URL/api/canva/oauth/redirect"
echo ""
echo "   📌 Return Navigation URL:"
echo "   $NGROK_URL/api/canva/return-nav"
echo ""
echo "3. Update your .env file with:"
echo ""
echo "   CANVA_REDIRECT_URI=$NGROK_URL/api/canva/oauth/redirect"
echo "   CANVA_RETURN_NAV_URI=$NGROK_URL/api/canva/return-nav"
echo "   CANVA_BASE_URL=$NGROK_URL"
echo ""
echo "📱 ACCESS YOUR APP:"
echo "==================="
echo ""
echo "🌐 Public URL (for Canva): $NGROK_URL"
echo "🏠 Local URL: http://localhost:5000"
echo "🔧 ngrok Dashboard: http://localhost:4040"
echo ""
echo "🎯 NEXT STEPS:"
echo "=============="
echo "1. Update Canva Developer Dashboard with the URLs above"
echo "2. Update your .env file with the ngrok URLs"
echo "3. Restart the development server: npm run dev"
echo "4. Visit $NGROK_URL to test Canva integration"
echo ""

# Function to handle cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    kill $DEV_SERVER_PID $NGROK_PID 2>/dev/null || true
    echo "✅ Services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

echo "💡 TIP: Keep this terminal open to maintain the ngrok tunnel"
echo "     Press Ctrl+C to stop all services"
echo ""
echo "🎸 Your venue preorder MVP is ready to rock with Canva! 🎵"
echo ""

# Keep the script running
wait
