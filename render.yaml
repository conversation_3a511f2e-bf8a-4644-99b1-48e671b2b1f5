services:
  - type: web
    name: frontend
    runtime: docker
    image: jesseniatriumph/frontend
    context: .
    envVars:
      - key: REACT_APP_API_URL
        value: /api
    routes:
      - source: /api
        target: backend
      - source: /(.*)
        target: /
    healthCheckPath: /

  - type: web
    name: backend
    runtime: docker
    image: jesseniatriumph/backend
    context: .
    port: 3000
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: postgres

databases:
  - name: postgres
    plan: free
