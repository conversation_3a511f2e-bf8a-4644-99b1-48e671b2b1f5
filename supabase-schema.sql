-- =============================================================================
-- SUPABASE DATABASE SCHEMA - VENUE MERCH PREORDER PLATFORM
-- =============================================================================
-- This SQL file creates the complete database structure for the Spotify-integrated
-- venue merch preorder platform with Printful fulfillment and Canva integration.
--
-- Run this in your Supabase SQL editor to set up the database structure.

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================================================
-- USERS TABLE (Enhanced with Spotify integration)
-- =============================================================================
CREATE TABLE users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255), -- Nullable for Spotify-only users
    role VARCHAR(20) DEFAULT 'guest' CHECK (role IN ('guest', 'artist', 'admin')),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    username VARCHAR(100) UNIQUE,
    is_guest BOOLEAN DEFAULT false,
    
    -- Spotify integration fields
    spotify_id VARCHAR(100) UNIQUE,
    spotify_access_token TEXT,
    spotify_refresh_token TEXT,
    spotify_token_expires_at TIMESTAMP,
    spotify_profile JSONB, -- Store full Spotify profile data
    
    -- Profile information
    avatar_url TEXT,
    phone VARCHAR(20),
    date_of_birth DATE,
    
    -- Preferences
    notification_preferences JSONB DEFAULT '{"email": true, "sms": false, "push": true}',
    privacy_settings JSONB DEFAULT '{"profile_public": false, "show_orders": false}',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_login_at TIMESTAMP
);

-- =============================================================================
-- ARTISTS TABLE (Spotify-connected artists)
-- =============================================================================
CREATE TABLE artists (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    
    -- Spotify data
    spotify_id VARCHAR(100) UNIQUE NOT NULL,
    spotify_data JSONB NOT NULL, -- Full artist data from Spotify
    
    -- Artist information
    name VARCHAR(255) NOT NULL,
    bio TEXT,
    genres TEXT[], -- Array of genres
    image_url TEXT,
    website_url TEXT,
    social_links JSONB, -- Instagram, Twitter, etc.
    
    -- Merch settings
    merch_settings JSONB DEFAULT '{"auto_approve": false, "revenue_share": 70, "design_templates": []}',
    approval_status VARCHAR(20) DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected', 'suspended')),
    
    -- Business information
    business_name VARCHAR(255),
    tax_id VARCHAR(50),
    payout_info JSONB, -- Bank account, PayPal, etc.
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    approved_at TIMESTAMP,
    approved_by UUID REFERENCES users(id)
);

-- =============================================================================
-- VENUES TABLE
-- =============================================================================
CREATE TABLE venues (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(50) NOT NULL,
    country VARCHAR(50) DEFAULT 'US',
    zip_code VARCHAR(20) NOT NULL,
    
    -- Contact information
    phone VARCHAR(20),
    email VARCHAR(255),
    website_url TEXT,
    
    -- Venue details
    capacity INTEGER,
    venue_type VARCHAR(50), -- concert_hall, club, arena, etc.
    amenities TEXT[],
    
    -- Pickup policies
    pickup_policy JSONB DEFAULT '{"cutoff_hours": 2, "pickup_window": 30, "instructions": ""}',
    
    -- Coordinates for mapping
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    
    -- Settings
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{"allow_pickup": true, "allow_shipping": true}',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- EVENTS TABLE (Enhanced with Spotify artist data)
-- =============================================================================
CREATE TABLE events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    venue_id UUID REFERENCES venues(id) ON DELETE CASCADE,
    
    -- Event information
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) DEFAULT 'concert',
    
    -- Spotify integration
    primary_artist_id UUID REFERENCES artists(id),
    featured_artists UUID[], -- Array of artist IDs
    spotify_playlist_id VARCHAR(100), -- Associated Spotify playlist
    
    -- Date and time
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    doors_open_time TIMESTAMP,
    
    -- Ticketing
    ticket_url TEXT,
    ticket_price_range JSONB, -- {"min": 25, "max": 150, "currency": "USD"}
    age_restriction VARCHAR(10), -- "18+", "21+", "all_ages"
    
    -- Event details
    image_url TEXT,
    poster_url TEXT,
    setlist TEXT[],
    
    -- Merch settings
    merch_cutoff_time TIMESTAMP, -- When to stop accepting merch orders
    pickup_start_time TIMESTAMP, -- When pickup becomes available
    pickup_end_time TIMESTAMP,   -- When pickup window closes
    
    -- Status
    status VARCHAR(20) DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'live', 'completed', 'cancelled')),
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- MERCH TABLE (Enhanced with Printful and Canva integration)
-- =============================================================================
CREATE TABLE merch (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    artist_id UUID REFERENCES artists(id) ON DELETE CASCADE,
    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
    
    -- Product information
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'apparel', -- apparel, accessories, posters, etc.
    
    -- Design information
    design_source VARCHAR(20) DEFAULT 'upload' CHECK (design_source IN ('upload', 'canva', 'ai_generated')),
    design_file_url TEXT,
    design_file_id VARCHAR(100), -- Printful file ID
    canva_design_id VARCHAR(100), -- Canva design ID if applicable
    design_metadata JSONB, -- Dimensions, colors, etc.
    
    -- Printful integration
    printful_product_id INTEGER, -- Printful product template ID
    printful_variant_id INTEGER, -- Specific variant (size, color)
    printful_sync_product_id INTEGER, -- Created product in Printful store
    printful_sync_variant_id INTEGER, -- Created variant in Printful store
    
    -- Pricing
    base_cost DECIMAL(10, 2), -- Cost from Printful
    retail_price DECIMAL(10, 2) NOT NULL,
    artist_revenue_share DECIMAL(5, 2) DEFAULT 70.00, -- Percentage
    
    -- Inventory
    stock_quantity INTEGER DEFAULT 0,
    unlimited_stock BOOLEAN DEFAULT false,
    
    -- Availability
    is_available BOOLEAN DEFAULT true,
    is_presale_only BOOLEAN DEFAULT false,
    is_vip_exclusive BOOLEAN DEFAULT false,
    
    -- Fulfillment
    fulfillment_type VARCHAR(20) DEFAULT 'printful' CHECK (fulfillment_type IN ('printful', 'gelato', 'manual')),
    delivery_options JSONB DEFAULT '{"pickup": true, "shipping": true}',
    
    -- Approval workflow
    approval_status VARCHAR(20) DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    approval_notes TEXT,
    approved_at TIMESTAMP,
    approved_by UUID REFERENCES users(id),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- ORDERS TABLE (Enhanced with fulfillment tracking)
-- =============================================================================
CREATE TABLE orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
    
    -- Order information
    order_number VARCHAR(50) UNIQUE NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Payment information
    stripe_payment_intent_id VARCHAR(100),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
    payment_method JSONB, -- Card details, etc.
    
    -- Fulfillment information
    fulfillment_method VARCHAR(20) DEFAULT 'pickup' CHECK (fulfillment_method IN ('pickup', 'shipping')),
    shipping_address JSONB, -- Full address object
    pickup_location VARCHAR(255),
    pickup_time_slot TIMESTAMP,
    
    -- Order status
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'completed', 'cancelled')),
    
    -- Printful integration
    printful_order_id INTEGER, -- Printful order ID
    printful_status VARCHAR(50), -- Printful order status
    tracking_number VARCHAR(100),
    tracking_url TEXT,
    
    -- Special instructions
    special_instructions TEXT,
    internal_notes TEXT,
    
    -- QR code for pickup
    qr_code TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- =============================================================================
-- ORDER ITEMS TABLE
-- =============================================================================
CREATE TABLE order_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    merch_id UUID REFERENCES merch(id) ON DELETE CASCADE,
    
    -- Item details
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    
    -- Product snapshot (in case merch details change)
    product_snapshot JSONB NOT NULL, -- Name, description, image, etc.
    
    -- Customizations
    size VARCHAR(10),
    color VARCHAR(50),
    customizations JSONB, -- Any special customizations
    
    -- Printful tracking
    printful_item_id INTEGER,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- DESIGN FILES TABLE (For Canva and upload tracking)
-- =============================================================================
CREATE TABLE design_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    artist_id UUID REFERENCES artists(id) ON DELETE CASCADE,
    
    -- File information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_url TEXT NOT NULL,
    file_size INTEGER,
    mime_type VARCHAR(100),
    
    -- Design metadata
    dimensions JSONB, -- {"width": 3000, "height": 3600}
    dpi INTEGER DEFAULT 300,
    color_mode VARCHAR(10) DEFAULT 'RGB',
    
    -- Source tracking
    source VARCHAR(20) DEFAULT 'upload' CHECK (source IN ('upload', 'canva', 'ai_generated')),
    canva_design_id VARCHAR(100),
    canva_export_data JSONB,
    
    -- Printful integration
    printful_file_id INTEGER,
    printful_file_hash VARCHAR(100),
    
    -- Usage tracking
    used_in_merch UUID[], -- Array of merch IDs using this design
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_spotify_id ON users(spotify_id);
CREATE INDEX idx_users_role ON users(role);

-- Artists indexes
CREATE INDEX idx_artists_spotify_id ON artists(spotify_id);
CREATE INDEX idx_artists_approval_status ON artists(approval_status);
CREATE INDEX idx_artists_user_id ON artists(user_id);

-- Events indexes
CREATE INDEX idx_events_venue_id ON events(venue_id);
CREATE INDEX idx_events_start_time ON events(start_time);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_primary_artist_id ON events(primary_artist_id);

-- Merch indexes
CREATE INDEX idx_merch_artist_id ON merch(artist_id);
CREATE INDEX idx_merch_event_id ON merch(event_id);
CREATE INDEX idx_merch_approval_status ON merch(approval_status);
CREATE INDEX idx_merch_is_available ON merch(is_available);

-- Orders indexes
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_event_id ON orders(event_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- Order items indexes
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_merch_id ON order_items(merch_id);

-- Design files indexes
CREATE INDEX idx_design_files_artist_id ON design_files(artist_id);
CREATE INDEX idx_design_files_source ON design_files(source);
CREATE INDEX idx_design_files_canva_design_id ON design_files(canva_design_id);

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE artists ENABLE ROW LEVEL SECURITY;
ALTER TABLE venues ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE merch ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE design_files ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can read own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid() = id);

-- Artists can manage their own data
CREATE POLICY "Artists can read own data" ON artists FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Artists can update own data" ON artists FOR UPDATE USING (user_id = auth.uid());

-- Artists can manage their own merch
CREATE POLICY "Artists can manage own merch" ON merch FOR ALL USING (
    artist_id IN (SELECT id FROM artists WHERE user_id = auth.uid())
);

-- Users can read approved merch
CREATE POLICY "Users can read approved merch" ON merch FOR SELECT USING (
    approval_status = 'approved' AND is_available = true
);

-- Users can manage their own orders
CREATE POLICY "Users can manage own orders" ON orders FOR ALL USING (user_id = auth.uid());

-- Users can read their own order items
CREATE POLICY "Users can read own order items" ON order_items FOR SELECT USING (
    order_id IN (SELECT id FROM orders WHERE user_id = auth.uid())
);

-- Artists can manage their own design files
CREATE POLICY "Artists can manage own designs" ON design_files FOR ALL USING (
    artist_id IN (SELECT id FROM artists WHERE user_id = auth.uid())
);

-- Public read access for venues and events
CREATE POLICY "Public can read venues" ON venues FOR SELECT USING (is_active = true);
CREATE POLICY "Public can read events" ON events FOR SELECT USING (is_active = true);

-- =============================================================================
-- FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_artists_updated_at BEFORE UPDATE ON artists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_venues_updated_at BEFORE UPDATE ON venues FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_events_updated_at BEFORE UPDATE ON events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_merch_updated_at BEFORE UPDATE ON merch FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_design_files_updated_at BEFORE UPDATE ON design_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
