import { pgTable, text, varchar, serial, integer, boolean, timestamp, decimal, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  email: text("email").unique(),
  phone: text("phone"),
  username: text("username"),
  firstName: text("first_name"),
  lastName: text("last_name"),
  role: text("role").default("guest"), // guest, admin, artist
  passwordHash: text("password_hash"),
  isGuest: boolean("is_guest").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

export const venues = pgTable("venues", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  address: text("address").notNull(),
  wifiSSID: text("wifi_ssid"),
  isActive: boolean("is_active").default(true),
});

export const venueSettings = pgTable("venue_settings", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  enableMerch: boolean("enable_merch").default(false).notNull(),
  enableLoyalty: boolean("enable_loyalty").default(false).notNull(),
  enableAffiliate: boolean("enable_affiliate").default(false).notNull(),
  enableAI: boolean("enable_ai").default(false).notNull(),
  pickupMode: text("pickup_mode").default("section").notNull()
});

export const events = pgTable("events", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").references(() => venues.id),
  name: text("name").notNull(),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  orderCutoff: timestamp("order_cutoff").notNull(),
  isActive: boolean("is_active").default(true),
});

export const menuCategories = pgTable("menu_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  type: text("type").notNull(), // 'food', 'drinks', 'merch'
  eventId: integer("event_id").references(() => events.id),
});

export const menuItems = pgTable("menu_items", {
  id: serial("id").primaryKey(),
  categoryId: integer("category_id").references(() => menuCategories.id),
  name: text("name").notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  stock: integer("stock").default(0),
  prepTime: integer("prep_time").default(5), // minutes
  imageUrl: text("image_url"),
  isAvailable: boolean("is_available").default(true),
});

export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  eventId: integer("event_id").references(() => events.id),
  totalAmount: decimal("total_amount", { precision: 10, scale: 2 }).notNull(),
  status: text("status").default("pending").notNull(), // pending, preparing, ready, picked_up, cancelled
  qrCode: text("qr_code"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const orderItems = pgTable("order_items", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").references(() => orders.id),
  menuItemId: integer("menu_item_id").references(() => menuItems.id),
  quantity: integer("quantity").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
});

export const artistMerch = pgTable("artist_merch", {
  id: serial("id").primaryKey(),
  artistId: integer("artist_id").references(() => users.id), // Assuming artists are users
  eventId: integer("event_id").references(() => events.id),
  name: text("name").notNull(),
  description: text("description"),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  stock: integer("stock").default(0),
  imageUrl: text("image_url"),
  isAvailable: boolean("is_available").default(true),
});

export const queueStatus = pgTable("queue_status", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  preparing: integer("preparing").default(0),
  ready: integer("ready").default(0),
  avgWaitTime: integer("avg_wait_time").default(0), // minutes
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const tickets = pgTable("tickets", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  ticketType: text("ticket_type").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  totalQuantity: integer("total_quantity").notNull(),
  availableQuantity: integer("available_quantity").notNull(),
});

export const preEventOrders = pgTable("pre_event_orders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  eventId: integer("event_id").references(() => events.id),
  orderTime: timestamp("order_time").notNull(),
  pickupTime: timestamp("pickup_time").notNull(),
  totalAmount: decimal("total_amount", { precision: 10, scale: 2 }).notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const preEventOrderItems = pgTable("pre_event_order_items", {
  id: serial("id").primaryKey(),
  preEventOrderId: integer("pre_event_order_id").references(() => preEventOrders.id),
  menuItemId: integer("menu_item_id").references(() => menuItems.id),
  quantity: integer("quantity").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
});

export const timeWindows = pgTable("time_windows", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").references(() => events.id),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  capacity: integer("capacity").notNull(),
});

export const pickupZones = pgTable("pickup_zones", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  name: text("name").notNull(),
  description: text("description")
});

export const pickupMappings = pgTable("pickup_mappings", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  pickupZoneId: integer("pickup_zone_id").notNull().references(() => pickupZones.id, { onDelete: 'cascade' }),
  section: text("section").notNull(),
  rowStart: integer("row_start"),
  rowEnd: integer("row_end")
});

export const loyaltyAccounts = pgTable("loyalty_accounts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id, { onDelete: 'cascade' }),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  points: integer("points").notNull().default(0),
  tier: text("tier"),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

export const affiliates = pgTable("affiliates", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  code: text("code").notNull().unique(),
  description: text("description"),
  discountPercent: integer("discount_percent"),
  commissionPercent: integer("commission_percent")
});

export const aiSettings = pgTable("ai_settings", {
  id: serial("id").primaryKey(),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  enableInventoryForecast: boolean("enable_inventory_forecast").default(false).notNull(),
  enableStaffScheduling: boolean("enable_staff_scheduling").default(false).notNull(),
  enableAnalytics: boolean("enable_analytics").default(false).notNull(),
  modelPreferences: text("model_preferences")
});

export const inventoryForecasts = pgTable("inventory_forecasts", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").notNull().references(() => events.id, { onDelete: 'cascade' }),
  itemId: integer("item_id").notNull().references(() => menuItems.id, { onDelete: 'cascade' }),
  forecastQuantity: integer("forecast_quantity"),
  generatedAt: timestamp("generated_at").defaultNow().notNull()
});

export const staffSchedules = pgTable("staff_schedules", {
  id: serial("id").primaryKey(),
  eventId: integer("event_id").notNull().references(() => events.id, { onDelete: 'cascade' }),
  venueId: integer("venue_id").notNull().references(() => venues.id, { onDelete: 'cascade' }),
  recommendedStaff: integer("recommended_staff"),
  schedulePlan: text("schedule_plan"),
  generatedAt: timestamp("generated_at").defaultNow().notNull()
});

// Zod validation schemas
export const insertUserSchema = createInsertSchema(users);
export const insertVenueSchema = createInsertSchema(venues);
export const insertVenueSettingsSchema = createInsertSchema(venueSettings);
export const insertEventSchema = createInsertSchema(events);
export const insertMenuCategorySchema = createInsertSchema(menuCategories);
export const insertMenuItemSchema = createInsertSchema(menuItems);
export const insertPreEventOrderSchema = createInsertSchema(preEventOrders);
export const insertTimeWindowSchema = createInsertSchema(timeWindows);
export const insertPickupZonesSchema = createInsertSchema(pickupZones);
export const insertPickupMappingsSchema = createInsertSchema(pickupMappings);
export const insertLoyaltyAccountsSchema = createInsertSchema(loyaltyAccounts);
export const insertAffiliatesSchema = createInsertSchema(affiliates);
export const insertAiSettingsSchema = createInsertSchema(aiSettings);
export const insertInventoryForecastsSchema = createInsertSchema(inventoryForecasts);
export const insertStaffSchedulesSchema = createInsertSchema(staffSchedules);

// Typescript types
export type User = typeof users.$inferSelect;
export type Venue = typeof venues.$inferSelect;
export type VenueSettings = typeof venueSettings.$inferSelect;
export type Event = typeof events.$inferSelect;
export type PreEventOrder = typeof preEventOrders.$inferSelect;
export type TimeWindow = typeof timeWindows.$inferSelect;
export type PickupZone = typeof pickupZones.$inferSelect;
export type PickupMapping = typeof pickupMappings.$inferSelect;
export type LoyaltyAccount = typeof loyaltyAccounts.$inferSelect;
export type Affiliate = typeof affiliates.$inferSelect;
export type AiSetting = typeof aiSettings.$inferSelect;
export type InventoryForecast = typeof inventoryForecasts.$inferSelect;
export type StaffSchedule = typeof staffSchedules.$inferSelect;
export type MenuItem = typeof menuItems.$inferSelect;
export type MenuCategory = typeof menuCategories.$inferSelect;
export type Order = typeof orders.$inferSelect;
export type OrderItem = typeof orderItems.$inferSelect;
export type ArtistMerch = typeof artistMerch.$inferSelect;
export type QueueStatus = typeof queueStatus.$inferSelect;
export type Ticket = typeof tickets.$inferSelect;
export type PreEventOrderItem = typeof preEventOrderItems.$inferSelect;

export const insertOrderSchema = createInsertSchema(orders);
export const insertOrderItemSchema = createInsertSchema(orderItems);
export const insertArtistMerchSchema = createInsertSchema(artistMerch);
export const insertQueueStatusSchema = createInsertSchema(queueStatus);
export const insertTicketSchema = createInsertSchema(tickets);
export const insertPreEventOrderItemSchema = createInsertSchema(preEventOrderItems);
